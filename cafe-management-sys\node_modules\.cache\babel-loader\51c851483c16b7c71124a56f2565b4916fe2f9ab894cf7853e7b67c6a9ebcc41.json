{"ast": null, "code": "var _jsxFileName = \"D:\\\\Github_Repos\\\\cafe-mangement-system-react\\\\cafe-management-sys\\\\src\\\\components\\\\CartComponents\\\\CartItems.jsx\";\n// components/CartItems.jsx\nimport React from 'react';\nimport { Grid, Box, Typography, Button } from '@mui/material';\nimport { ShoppingCart as ShoppingCartIcon } from '@mui/icons-material';\nimport { Link } from 'react-router-dom';\nimport CartItemCard from './CartItemCard';\nimport OrderSummary from './OrderSummary';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CartItems = ({\n  cartItems,\n  onQuantityChange,\n  onRemoveItem,\n  onClearCart,\n  onUpdateCustomization,\n  total,\n  cartCount,\n  totalPrepTime,\n  onNext,\n  isLoading\n}) => {\n  return /*#__PURE__*/_jsxDEV(Grid, {\n    container: true,\n    spacing: 3,\n    children: [/*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      md: 8,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          gap: 2\n        },\n        children: cartItems.map(item => /*#__PURE__*/_jsxDEV(CartItemCard, {\n          item: item,\n          onQuantityChange: onQuantityChange,\n          onRemoveItem: onRemoveItem,\n          onUpdateCustomization: onUpdateCustomization\n        }, item.cartItemId, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      md: 4,\n      children: /*#__PURE__*/_jsxDEV(OrderSummary, {\n        total: total,\n        cartCount: cartCount,\n        totalPrepTime: totalPrepTime,\n        onNext: onNext,\n        isLoading: isLoading,\n        nextLabel: \"Proceed to Checkout\",\n        showClearCart: true,\n        onClearCart: onClearCart,\n        cartItemsCount: cartItems.length\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_c = CartItems;\nexport default CartItems;\nvar _c;\n$RefreshReg$(_c, \"CartItems\");", "map": {"version": 3, "names": ["React", "Grid", "Box", "Typography", "<PERSON><PERSON>", "ShoppingCart", "ShoppingCartIcon", "Link", "CartItemCard", "OrderSummary", "jsxDEV", "_jsxDEV", "CartItems", "cartItems", "onQuantityChange", "onRemoveItem", "onClearCart", "onUpdateCustomization", "total", "cartCount", "totalPrepTime", "onNext", "isLoading", "container", "spacing", "children", "item", "xs", "md", "sx", "display", "flexDirection", "gap", "map", "cartItemId", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "next<PERSON><PERSON><PERSON>", "showClearCart", "cartItemsCount", "length", "_c", "$RefreshReg$"], "sources": ["D:/Github_Repos/cafe-mangement-system-react/cafe-management-sys/src/components/CartComponents/CartItems.jsx"], "sourcesContent": ["// components/CartItems.jsx\r\nimport React from 'react';\r\nimport { Grid, Box, Typography, Button } from '@mui/material';\r\nimport { ShoppingCart as ShoppingCartIcon } from '@mui/icons-material';\r\nimport { Link } from 'react-router-dom';\r\nimport CartItemCard from './CartItemCard';\r\nimport OrderSummary from './OrderSummary';\r\n\r\nconst CartItems = ({\r\n  cartItems,\r\n  onQuantityChange,\r\n  onRemoveItem,\r\n  onClearCart,\r\n  onUpdateCustomization,\r\n  total,\r\n  cartCount,\r\n  totalPrepTime,\r\n  onNext,\r\n  isLoading\r\n}) => {\r\n  return (\r\n    <Grid container spacing={3}>\r\n      <Grid item xs={12} md={8}>\r\n        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>\r\n          {cartItems.map((item) => (\r\n            <CartItemCard\r\n              key={item.cartItemId}\r\n              item={item}\r\n              onQuantityChange={onQuantityChange}\r\n              onRemoveItem={onRemoveItem}\r\n              onUpdateCustomization={onUpdateCustomization}\r\n            />\r\n          ))}\r\n        </Box>\r\n      </Grid>\r\n      <Grid item xs={12} md={4}>\r\n        <OrderSummary \r\n          total={total} \r\n          cartCount={cartCount} \r\n          totalPrepTime={totalPrepTime} \r\n          onNext={onNext}\r\n          isLoading={isLoading}\r\n          nextLabel=\"Proceed to Checkout\"\r\n          showClearCart\r\n          onClearCart={onClearCart}\r\n          cartItemsCount={cartItems.length}\r\n        />\r\n      </Grid>\r\n    </Grid>\r\n  );\r\n};\r\n\r\nexport default CartItems;"], "mappings": ";AAAA;AACA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,GAAG,EAAEC,UAAU,EAAEC,MAAM,QAAQ,eAAe;AAC7D,SAASC,YAAY,IAAIC,gBAAgB,QAAQ,qBAAqB;AACtE,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,YAAY,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,SAAS,GAAGA,CAAC;EACjBC,SAAS;EACTC,gBAAgB;EAChBC,YAAY;EACZC,WAAW;EACXC,qBAAqB;EACrBC,KAAK;EACLC,SAAS;EACTC,aAAa;EACbC,MAAM;EACNC;AACF,CAAC,KAAK;EACJ,oBACEX,OAAA,CAACV,IAAI;IAACsB,SAAS;IAACC,OAAO,EAAE,CAAE;IAAAC,QAAA,gBACzBd,OAAA,CAACV,IAAI;MAACyB,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACvBd,OAAA,CAACT,GAAG;QAAC2B,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,aAAa,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAP,QAAA,EAC3DZ,SAAS,CAACoB,GAAG,CAAEP,IAAI,iBAClBf,OAAA,CAACH,YAAY;UAEXkB,IAAI,EAAEA,IAAK;UACXZ,gBAAgB,EAAEA,gBAAiB;UACnCC,YAAY,EAAEA,YAAa;UAC3BE,qBAAqB,EAAEA;QAAsB,GAJxCS,IAAI,CAACQ,UAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKrB,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACP3B,OAAA,CAACV,IAAI;MAACyB,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACvBd,OAAA,CAACF,YAAY;QACXS,KAAK,EAAEA,KAAM;QACbC,SAAS,EAAEA,SAAU;QACrBC,aAAa,EAAEA,aAAc;QAC7BC,MAAM,EAAEA,MAAO;QACfC,SAAS,EAAEA,SAAU;QACrBiB,SAAS,EAAC,qBAAqB;QAC/BC,aAAa;QACbxB,WAAW,EAAEA,WAAY;QACzByB,cAAc,EAAE5B,SAAS,CAAC6B;MAAO;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEX,CAAC;AAACK,EAAA,GA1CI/B,SAAS;AA4Cf,eAAeA,SAAS;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}