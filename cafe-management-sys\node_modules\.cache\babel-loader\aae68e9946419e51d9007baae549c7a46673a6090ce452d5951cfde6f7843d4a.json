{"ast": null, "code": "// API Configuration\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:4969/api';\n\n// Helper function to get auth token (using sessionStorage for better security)\nconst getAuthToken = () => {\n  return sessionStorage.getItem('token') || localStorage.getItem('token');\n};\n\n// Helper function to create headers\nconst createHeaders = (includeAuth = true, isFormData = false) => {\n  const headers = {};\n\n  // Don't set Content-Type for FormData - browser will set it with boundary\n  if (!isFormData) {\n    headers['Content-Type'] = 'application/json';\n  }\n  if (includeAuth) {\n    const token = getAuthToken();\n    if (token) {\n      headers.Authorization = `Bearer ${token}`;\n    }\n  }\n  return headers;\n};\n\n// Generic API request function with enhanced error handling\nconst apiRequest = async (endpoint, options = {}) => {\n  const url = `${API_BASE_URL}${endpoint}`;\n  const config = {\n    headers: createHeaders(options.auth !== false, options.isFormData),\n    ...options\n  };\n  try {\n    const response = await fetch(url, config);\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n\n      // Handle specific HTTP status codes\n      switch (response.status) {\n        case 400:\n          throw new Error(errorData.message || 'Invalid request data');\n        case 401:\n          throw new Error(errorData.message || 'Authentication required');\n        case 403:\n          throw new Error(errorData.message || 'Access denied');\n        case 404:\n          throw new Error(errorData.message || 'Resource not found');\n        case 409:\n          throw new Error(errorData.message || 'Resource already exists');\n        case 429:\n          throw new Error(errorData.message || 'Too many requests. Please try again later.');\n        case 500:\n          throw new Error(errorData.message || 'Server error. Please try again later.');\n        default:\n          throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n      }\n    }\n    return await response.json();\n  } catch (error) {\n    console.error(`API request failed for ${endpoint}:`, error);\n    throw error;\n  }\n};\n\n// Authentication API\nexport const authAPI = {\n  login: credentials => apiRequest('/auth/login', {\n    method: 'POST',\n    body: JSON.stringify(credentials),\n    auth: false\n  }),\n  register: userData => apiRequest('/auth/register/customer', {\n    method: 'POST',\n    body: JSON.stringify(userData),\n    auth: false\n  }),\n  registerStaff: userData => apiRequest('/auth/register/staff', {\n    method: 'POST',\n    body: JSON.stringify(userData)\n  }),\n  updateProfile: userData => apiRequest('/auth/profile', {\n    method: 'PUT',\n    body: JSON.stringify(userData)\n  }),\n  deleteProfile: () => apiRequest('/auth/profile', {\n    method: 'DELETE'\n  })\n};\n\n// Menu API\nexport const menuAPI = {\n  getAll: () => apiRequest('/menu', {\n    auth: false\n  }),\n  getById: id => apiRequest(`/menu/${id}`, {\n    auth: false\n  }),\n  create: menuData => apiRequest('/menu', {\n    method: 'POST',\n    body: JSON.stringify(menuData)\n  }),\n  update: (id, menuData) => apiRequest(`/menu/${id}`, {\n    method: 'PUT',\n    body: JSON.stringify(menuData)\n  }),\n  delete: id => apiRequest(`/menu/${id}`, {\n    method: 'DELETE'\n  })\n};\n\n// Orders API\nexport const ordersAPI = {\n  getAll: () => apiRequest('/orders'),\n  getById: id => apiRequest(`/orders/${id}`),\n  create: orderData => apiRequest('/orders', {\n    method: 'POST',\n    body: JSON.stringify(orderData)\n  }),\n  update: (id, orderData) => apiRequest(`/orders/${id}`, {\n    method: 'PUT',\n    body: JSON.stringify(orderData)\n  }),\n  updateStatus: (id, status) => apiRequest(`/orders/${id}/status`, {\n    method: 'PATCH',\n    body: JSON.stringify({\n      status\n    })\n  }),\n  delete: id => apiRequest(`/orders/${id}`, {\n    method: 'DELETE'\n  }),\n  getByCustomer: customerId => apiRequest(`/orders/customer/${customerId}`),\n  getRecent: (limit = 10) => apiRequest(`/orders?limit=${limit}&sort=-createdAt`)\n};\n\n// Staff API\nexport const staffAPI = {\n  getAll: () => apiRequest('/staff-admin'),\n  add: staffData => apiRequest('/staff-admin', {\n    method: 'POST',\n    body: JSON.stringify(staffData)\n  }),\n  update: (id, staffData) => apiRequest(`/staff-admin/${id}`, {\n    method: 'PUT',\n    body: JSON.stringify(staffData)\n  }),\n  remove: id => apiRequest(`/staff-admin/${id}`, {\n    method: 'DELETE'\n  })\n};\n\n// Customers API\nexport const customersAPI = {\n  getAll: () => apiRequest('/customers'),\n  getById: id => apiRequest(`/customers/${id}`),\n  update: (id, customerData) => apiRequest(`/customers/${id}`, {\n    method: 'PUT',\n    body: JSON.stringify(customerData)\n  }),\n  delete: id => apiRequest(`/customers/${id}`, {\n    method: 'DELETE'\n  })\n};\n\n// Dashboard/Analytics API (these might need to be implemented on the backend)\nexport const analyticsAPI = {\n  getDashboardStats: () => apiRequest('/analytics/dashboard-stats').catch(() => {\n    // Fallback to mock data if endpoint doesn't exist\n    return {\n      totalRevenue: 12426,\n      ordersToday: 147,\n      menuItems: 42,\n      inventoryItems: 156,\n      lowStockItems: 3,\n      pendingOrders: 12,\n      revenueChange: 12.5,\n      ordersChange: 8.2\n    };\n  }),\n  getRevenueStats: (period = '30d') => apiRequest(`/analytics/revenue?period=${period}`).catch(() => {\n    // Mock data fallback\n    return {\n      total: 12426,\n      change: 12.5,\n      chartData: []\n    };\n  }),\n  getOrderStats: (period = '30d') => apiRequest(`/analytics/orders?period=${period}`).catch(() => {\n    // Mock data fallback\n    return {\n      total: 147,\n      change: 8.2,\n      chartData: []\n    };\n  }),\n  getInventoryAlerts: () => apiRequest('/analytics/inventory-alerts').catch(() => {\n    // Mock data fallback\n    return [{\n      id: 1,\n      item: 'Coffee beans',\n      currentStock: 5,\n      minStock: 10,\n      unit: 'lbs',\n      severity: 'high'\n    }, {\n      id: 2,\n      item: 'Milk',\n      currentStock: 2,\n      minStock: 5,\n      unit: 'gallons',\n      severity: 'medium'\n    }];\n  })\n};\n\n// Health check\nexport const healthAPI = {\n  check: () => apiRequest('/health', {\n    auth: false\n  })\n};\nexport default {\n  auth: authAPI,\n  menu: menuAPI,\n  orders: ordersAPI,\n  staff: staffAPI,\n  customers: customersAPI,\n  analytics: analyticsAPI,\n  health: healthAPI\n};", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_URL", "getAuthToken", "sessionStorage", "getItem", "localStorage", "createHeaders", "<PERSON><PERSON><PERSON>", "isFormData", "headers", "token", "Authorization", "apiRequest", "endpoint", "options", "url", "config", "auth", "response", "fetch", "ok", "errorData", "json", "catch", "status", "Error", "message", "error", "console", "authAPI", "login", "credentials", "method", "body", "JSON", "stringify", "register", "userData", "registerStaff", "updateProfile", "deleteProfile", "menuAPI", "getAll", "getById", "id", "create", "menuData", "update", "delete", "ordersAPI", "orderData", "updateStatus", "getByCustomer", "customerId", "getRecent", "limit", "staffAPI", "add", "staffData", "remove", "customersAPI", "customerData", "analyticsAPI", "getDashboardStats", "totalRevenue", "ordersToday", "menuItems", "inventoryItems", "lowStockItems", "pendingOrders", "revenueChange", "ordersChange", "getRevenueStats", "period", "total", "change", "chartData", "getOrderStats", "getInventoryAlerts", "item", "currentStock", "minStock", "unit", "severity", "healthAPI", "check", "menu", "orders", "staff", "customers", "analytics", "health"], "sources": ["D:/Github_Repos/cafe-mangement-system-react/cafe-management-sys/src/services/api.js"], "sourcesContent": ["// API Configuration\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:4969/api';\n\n// Helper function to get auth token (using sessionStorage for better security)\nconst getAuthToken = () => {\n  return sessionStorage.getItem('token') || localStorage.getItem('token');\n};\n\n// Helper function to create headers\nconst createHeaders = (includeAuth = true, isFormData = false) => {\n  const headers = {};\n\n  // Don't set Content-Type for FormData - browser will set it with boundary\n  if (!isFormData) {\n    headers['Content-Type'] = 'application/json';\n  }\n\n  if (includeAuth) {\n    const token = getAuthToken();\n    if (token) {\n      headers.Authorization = `Bearer ${token}`;\n    }\n  }\n\n  return headers;\n};\n\n// Generic API request function with enhanced error handling\nconst apiRequest = async (endpoint, options = {}) => {\n  const url = `${API_BASE_URL}${endpoint}`;\n  const config = {\n    headers: createHeaders(options.auth !== false, options.isFormData),\n    ...options,\n  };\n\n  try {\n    const response = await fetch(url, config);\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n\n      // Handle specific HTTP status codes\n      switch (response.status) {\n        case 400:\n          throw new Error(errorData.message || 'Invalid request data');\n        case 401:\n          throw new Error(errorData.message || 'Authentication required');\n        case 403:\n          throw new Error(errorData.message || 'Access denied');\n        case 404:\n          throw new Error(errorData.message || 'Resource not found');\n        case 409:\n          throw new Error(errorData.message || 'Resource already exists');\n        case 429:\n          throw new Error(errorData.message || 'Too many requests. Please try again later.');\n        case 500:\n          throw new Error(errorData.message || 'Server error. Please try again later.');\n        default:\n          throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n      }\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(`API request failed for ${endpoint}:`, error);\n    throw error;\n  }\n};\n\n// Authentication API\nexport const authAPI = {\n  login: (credentials) => \n    apiRequest('/auth/login', {\n      method: 'POST',\n      body: JSON.stringify(credentials),\n      auth: false,\n    }),\n    \n  register: (userData) => \n    apiRequest('/auth/register/customer', {\n      method: 'POST',\n      body: JSON.stringify(userData),\n      auth: false,\n    }),\n    \n  registerStaff: (userData) => \n    apiRequest('/auth/register/staff', {\n      method: 'POST',\n      body: JSON.stringify(userData),\n    }),\n    \n  updateProfile: (userData) => \n    apiRequest('/auth/profile', {\n      method: 'PUT',\n      body: JSON.stringify(userData),\n    }),\n    \n  deleteProfile: () => \n    apiRequest('/auth/profile', {\n      method: 'DELETE',\n    }),\n};\n\n// Menu API\nexport const menuAPI = {\n  getAll: () => apiRequest('/menu', { auth: false }),\n  \n  getById: (id) => apiRequest(`/menu/${id}`, { auth: false }),\n  \n  create: (menuData) => \n    apiRequest('/menu', {\n      method: 'POST',\n      body: JSON.stringify(menuData),\n    }),\n    \n  update: (id, menuData) => \n    apiRequest(`/menu/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(menuData),\n    }),\n    \n  delete: (id) => \n    apiRequest(`/menu/${id}`, {\n      method: 'DELETE',\n    }),\n};\n\n// Orders API\nexport const ordersAPI = {\n  getAll: () => apiRequest('/orders'),\n  \n  getById: (id) => apiRequest(`/orders/${id}`),\n  \n  create: (orderData) => \n    apiRequest('/orders', {\n      method: 'POST',\n      body: JSON.stringify(orderData),\n    }),\n    \n  update: (id, orderData) => \n    apiRequest(`/orders/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(orderData),\n    }),\n    \n  updateStatus: (id, status) => \n    apiRequest(`/orders/${id}/status`, {\n      method: 'PATCH',\n      body: JSON.stringify({ status }),\n    }),\n    \n  delete: (id) => \n    apiRequest(`/orders/${id}`, {\n      method: 'DELETE',\n    }),\n    \n  getByCustomer: (customerId) => \n    apiRequest(`/orders/customer/${customerId}`),\n    \n  getRecent: (limit = 10) => \n    apiRequest(`/orders?limit=${limit}&sort=-createdAt`),\n};\n\n// Staff API\nexport const staffAPI = {\n  getAll: () => apiRequest('/staff-admin'),\n  \n  add: (staffData) => \n    apiRequest('/staff-admin', {\n      method: 'POST',\n      body: JSON.stringify(staffData),\n    }),\n    \n  update: (id, staffData) => \n    apiRequest(`/staff-admin/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(staffData),\n    }),\n    \n  remove: (id) => \n    apiRequest(`/staff-admin/${id}`, {\n      method: 'DELETE',\n    }),\n};\n\n// Customers API\nexport const customersAPI = {\n  getAll: () => apiRequest('/customers'),\n  \n  getById: (id) => apiRequest(`/customers/${id}`),\n  \n  update: (id, customerData) => \n    apiRequest(`/customers/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(customerData),\n    }),\n    \n  delete: (id) => \n    apiRequest(`/customers/${id}`, {\n      method: 'DELETE',\n    }),\n};\n\n// Dashboard/Analytics API (these might need to be implemented on the backend)\nexport const analyticsAPI = {\n  getDashboardStats: () => \n    apiRequest('/analytics/dashboard-stats').catch(() => {\n      // Fallback to mock data if endpoint doesn't exist\n      return {\n        totalRevenue: 12426,\n        ordersToday: 147,\n        menuItems: 42,\n        inventoryItems: 156,\n        lowStockItems: 3,\n        pendingOrders: 12,\n        revenueChange: 12.5,\n        ordersChange: 8.2,\n      };\n    }),\n    \n  getRevenueStats: (period = '30d') => \n    apiRequest(`/analytics/revenue?period=${period}`).catch(() => {\n      // Mock data fallback\n      return {\n        total: 12426,\n        change: 12.5,\n        chartData: [],\n      };\n    }),\n    \n  getOrderStats: (period = '30d') => \n    apiRequest(`/analytics/orders?period=${period}`).catch(() => {\n      // Mock data fallback\n      return {\n        total: 147,\n        change: 8.2,\n        chartData: [],\n      };\n    }),\n    \n  getInventoryAlerts: () => \n    apiRequest('/analytics/inventory-alerts').catch(() => {\n      // Mock data fallback\n      return [\n        {\n          id: 1,\n          item: 'Coffee beans',\n          currentStock: 5,\n          minStock: 10,\n          unit: 'lbs',\n          severity: 'high',\n        },\n        {\n          id: 2,\n          item: 'Milk',\n          currentStock: 2,\n          minStock: 5,\n          unit: 'gallons',\n          severity: 'medium',\n        },\n      ];\n    }),\n};\n\n// Health check\nexport const healthAPI = {\n  check: () => apiRequest('/health', { auth: false }),\n};\n\nexport default {\n  auth: authAPI,\n  menu: menuAPI,\n  orders: ordersAPI,\n  staff: staffAPI,\n  customers: customersAPI,\n  analytics: analyticsAPI,\n  health: healthAPI,\n};\n"], "mappings": "AAAA;AACA,MAAMA,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;;AAEjF;AACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACzB,OAAOC,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC,IAAIC,YAAY,CAACD,OAAO,CAAC,OAAO,CAAC;AACzE,CAAC;;AAED;AACA,MAAME,aAAa,GAAGA,CAACC,WAAW,GAAG,IAAI,EAAEC,UAAU,GAAG,KAAK,KAAK;EAChE,MAAMC,OAAO,GAAG,CAAC,CAAC;;EAElB;EACA,IAAI,CAACD,UAAU,EAAE;IACfC,OAAO,CAAC,cAAc,CAAC,GAAG,kBAAkB;EAC9C;EAEA,IAAIF,WAAW,EAAE;IACf,MAAMG,KAAK,GAAGR,YAAY,CAAC,CAAC;IAC5B,IAAIQ,KAAK,EAAE;MACTD,OAAO,CAACE,aAAa,GAAG,UAAUD,KAAK,EAAE;IAC3C;EACF;EAEA,OAAOD,OAAO;AAChB,CAAC;;AAED;AACA,MAAMG,UAAU,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;EACnD,MAAMC,GAAG,GAAG,GAAGjB,YAAY,GAAGe,QAAQ,EAAE;EACxC,MAAMG,MAAM,GAAG;IACbP,OAAO,EAAEH,aAAa,CAACQ,OAAO,CAACG,IAAI,KAAK,KAAK,EAAEH,OAAO,CAACN,UAAU,CAAC;IAClE,GAAGM;EACL,CAAC;EAED,IAAI;IACF,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAACJ,GAAG,EAAEC,MAAM,CAAC;IAEzC,IAAI,CAACE,QAAQ,CAACE,EAAE,EAAE;MAChB,MAAMC,SAAS,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;;MAEzD;MACA,QAAQL,QAAQ,CAACM,MAAM;QACrB,KAAK,GAAG;UACN,MAAM,IAAIC,KAAK,CAACJ,SAAS,CAACK,OAAO,IAAI,sBAAsB,CAAC;QAC9D,KAAK,GAAG;UACN,MAAM,IAAID,KAAK,CAACJ,SAAS,CAACK,OAAO,IAAI,yBAAyB,CAAC;QACjE,KAAK,GAAG;UACN,MAAM,IAAID,KAAK,CAACJ,SAAS,CAACK,OAAO,IAAI,eAAe,CAAC;QACvD,KAAK,GAAG;UACN,MAAM,IAAID,KAAK,CAACJ,SAAS,CAACK,OAAO,IAAI,oBAAoB,CAAC;QAC5D,KAAK,GAAG;UACN,MAAM,IAAID,KAAK,CAACJ,SAAS,CAACK,OAAO,IAAI,yBAAyB,CAAC;QACjE,KAAK,GAAG;UACN,MAAM,IAAID,KAAK,CAACJ,SAAS,CAACK,OAAO,IAAI,4CAA4C,CAAC;QACpF,KAAK,GAAG;UACN,MAAM,IAAID,KAAK,CAACJ,SAAS,CAACK,OAAO,IAAI,uCAAuC,CAAC;QAC/E;UACE,MAAM,IAAID,KAAK,CAACJ,SAAS,CAACK,OAAO,IAAI,uBAAuBR,QAAQ,CAACM,MAAM,EAAE,CAAC;MAClF;IACF;IAEA,OAAO,MAAMN,QAAQ,CAACI,IAAI,CAAC,CAAC;EAC9B,CAAC,CAAC,OAAOK,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,0BAA0Bd,QAAQ,GAAG,EAAEc,KAAK,CAAC;IAC3D,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAME,OAAO,GAAG;EACrBC,KAAK,EAAGC,WAAW,IACjBnB,UAAU,CAAC,aAAa,EAAE;IACxBoB,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACJ,WAAW,CAAC;IACjCd,IAAI,EAAE;EACR,CAAC,CAAC;EAEJmB,QAAQ,EAAGC,QAAQ,IACjBzB,UAAU,CAAC,yBAAyB,EAAE;IACpCoB,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACE,QAAQ,CAAC;IAC9BpB,IAAI,EAAE;EACR,CAAC,CAAC;EAEJqB,aAAa,EAAGD,QAAQ,IACtBzB,UAAU,CAAC,sBAAsB,EAAE;IACjCoB,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACE,QAAQ;EAC/B,CAAC,CAAC;EAEJE,aAAa,EAAGF,QAAQ,IACtBzB,UAAU,CAAC,eAAe,EAAE;IAC1BoB,MAAM,EAAE,KAAK;IACbC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACE,QAAQ;EAC/B,CAAC,CAAC;EAEJG,aAAa,EAAEA,CAAA,KACb5B,UAAU,CAAC,eAAe,EAAE;IAC1BoB,MAAM,EAAE;EACV,CAAC;AACL,CAAC;;AAED;AACA,OAAO,MAAMS,OAAO,GAAG;EACrBC,MAAM,EAAEA,CAAA,KAAM9B,UAAU,CAAC,OAAO,EAAE;IAAEK,IAAI,EAAE;EAAM,CAAC,CAAC;EAElD0B,OAAO,EAAGC,EAAE,IAAKhC,UAAU,CAAC,SAASgC,EAAE,EAAE,EAAE;IAAE3B,IAAI,EAAE;EAAM,CAAC,CAAC;EAE3D4B,MAAM,EAAGC,QAAQ,IACflC,UAAU,CAAC,OAAO,EAAE;IAClBoB,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACW,QAAQ;EAC/B,CAAC,CAAC;EAEJC,MAAM,EAAEA,CAACH,EAAE,EAAEE,QAAQ,KACnBlC,UAAU,CAAC,SAASgC,EAAE,EAAE,EAAE;IACxBZ,MAAM,EAAE,KAAK;IACbC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACW,QAAQ;EAC/B,CAAC,CAAC;EAEJE,MAAM,EAAGJ,EAAE,IACThC,UAAU,CAAC,SAASgC,EAAE,EAAE,EAAE;IACxBZ,MAAM,EAAE;EACV,CAAC;AACL,CAAC;;AAED;AACA,OAAO,MAAMiB,SAAS,GAAG;EACvBP,MAAM,EAAEA,CAAA,KAAM9B,UAAU,CAAC,SAAS,CAAC;EAEnC+B,OAAO,EAAGC,EAAE,IAAKhC,UAAU,CAAC,WAAWgC,EAAE,EAAE,CAAC;EAE5CC,MAAM,EAAGK,SAAS,IAChBtC,UAAU,CAAC,SAAS,EAAE;IACpBoB,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACe,SAAS;EAChC,CAAC,CAAC;EAEJH,MAAM,EAAEA,CAACH,EAAE,EAAEM,SAAS,KACpBtC,UAAU,CAAC,WAAWgC,EAAE,EAAE,EAAE;IAC1BZ,MAAM,EAAE,KAAK;IACbC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACe,SAAS;EAChC,CAAC,CAAC;EAEJC,YAAY,EAAEA,CAACP,EAAE,EAAEpB,MAAM,KACvBZ,UAAU,CAAC,WAAWgC,EAAE,SAAS,EAAE;IACjCZ,MAAM,EAAE,OAAO;IACfC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;MAAEX;IAAO,CAAC;EACjC,CAAC,CAAC;EAEJwB,MAAM,EAAGJ,EAAE,IACThC,UAAU,CAAC,WAAWgC,EAAE,EAAE,EAAE;IAC1BZ,MAAM,EAAE;EACV,CAAC,CAAC;EAEJoB,aAAa,EAAGC,UAAU,IACxBzC,UAAU,CAAC,oBAAoByC,UAAU,EAAE,CAAC;EAE9CC,SAAS,EAAEA,CAACC,KAAK,GAAG,EAAE,KACpB3C,UAAU,CAAC,iBAAiB2C,KAAK,kBAAkB;AACvD,CAAC;;AAED;AACA,OAAO,MAAMC,QAAQ,GAAG;EACtBd,MAAM,EAAEA,CAAA,KAAM9B,UAAU,CAAC,cAAc,CAAC;EAExC6C,GAAG,EAAGC,SAAS,IACb9C,UAAU,CAAC,cAAc,EAAE;IACzBoB,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACuB,SAAS;EAChC,CAAC,CAAC;EAEJX,MAAM,EAAEA,CAACH,EAAE,EAAEc,SAAS,KACpB9C,UAAU,CAAC,gBAAgBgC,EAAE,EAAE,EAAE;IAC/BZ,MAAM,EAAE,KAAK;IACbC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACuB,SAAS;EAChC,CAAC,CAAC;EAEJC,MAAM,EAAGf,EAAE,IACThC,UAAU,CAAC,gBAAgBgC,EAAE,EAAE,EAAE;IAC/BZ,MAAM,EAAE;EACV,CAAC;AACL,CAAC;;AAED;AACA,OAAO,MAAM4B,YAAY,GAAG;EAC1BlB,MAAM,EAAEA,CAAA,KAAM9B,UAAU,CAAC,YAAY,CAAC;EAEtC+B,OAAO,EAAGC,EAAE,IAAKhC,UAAU,CAAC,cAAcgC,EAAE,EAAE,CAAC;EAE/CG,MAAM,EAAEA,CAACH,EAAE,EAAEiB,YAAY,KACvBjD,UAAU,CAAC,cAAcgC,EAAE,EAAE,EAAE;IAC7BZ,MAAM,EAAE,KAAK;IACbC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC0B,YAAY;EACnC,CAAC,CAAC;EAEJb,MAAM,EAAGJ,EAAE,IACThC,UAAU,CAAC,cAAcgC,EAAE,EAAE,EAAE;IAC7BZ,MAAM,EAAE;EACV,CAAC;AACL,CAAC;;AAED;AACA,OAAO,MAAM8B,YAAY,GAAG;EAC1BC,iBAAiB,EAAEA,CAAA,KACjBnD,UAAU,CAAC,4BAA4B,CAAC,CAACW,KAAK,CAAC,MAAM;IACnD;IACA,OAAO;MACLyC,YAAY,EAAE,KAAK;MACnBC,WAAW,EAAE,GAAG;MAChBC,SAAS,EAAE,EAAE;MACbC,cAAc,EAAE,GAAG;MACnBC,aAAa,EAAE,CAAC;MAChBC,aAAa,EAAE,EAAE;MACjBC,aAAa,EAAE,IAAI;MACnBC,YAAY,EAAE;IAChB,CAAC;EACH,CAAC,CAAC;EAEJC,eAAe,EAAEA,CAACC,MAAM,GAAG,KAAK,KAC9B7D,UAAU,CAAC,6BAA6B6D,MAAM,EAAE,CAAC,CAAClD,KAAK,CAAC,MAAM;IAC5D;IACA,OAAO;MACLmD,KAAK,EAAE,KAAK;MACZC,MAAM,EAAE,IAAI;MACZC,SAAS,EAAE;IACb,CAAC;EACH,CAAC,CAAC;EAEJC,aAAa,EAAEA,CAACJ,MAAM,GAAG,KAAK,KAC5B7D,UAAU,CAAC,4BAA4B6D,MAAM,EAAE,CAAC,CAAClD,KAAK,CAAC,MAAM;IAC3D;IACA,OAAO;MACLmD,KAAK,EAAE,GAAG;MACVC,MAAM,EAAE,GAAG;MACXC,SAAS,EAAE;IACb,CAAC;EACH,CAAC,CAAC;EAEJE,kBAAkB,EAAEA,CAAA,KAClBlE,UAAU,CAAC,6BAA6B,CAAC,CAACW,KAAK,CAAC,MAAM;IACpD;IACA,OAAO,CACL;MACEqB,EAAE,EAAE,CAAC;MACLmC,IAAI,EAAE,cAAc;MACpBC,YAAY,EAAE,CAAC;MACfC,QAAQ,EAAE,EAAE;MACZC,IAAI,EAAE,KAAK;MACXC,QAAQ,EAAE;IACZ,CAAC,EACD;MACEvC,EAAE,EAAE,CAAC;MACLmC,IAAI,EAAE,MAAM;MACZC,YAAY,EAAE,CAAC;MACfC,QAAQ,EAAE,CAAC;MACXC,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE;IACZ,CAAC,CACF;EACH,CAAC;AACL,CAAC;;AAED;AACA,OAAO,MAAMC,SAAS,GAAG;EACvBC,KAAK,EAAEA,CAAA,KAAMzE,UAAU,CAAC,SAAS,EAAE;IAAEK,IAAI,EAAE;EAAM,CAAC;AACpD,CAAC;AAED,eAAe;EACbA,IAAI,EAAEY,OAAO;EACbyD,IAAI,EAAE7C,OAAO;EACb8C,MAAM,EAAEtC,SAAS;EACjBuC,KAAK,EAAEhC,QAAQ;EACfiC,SAAS,EAAE7B,YAAY;EACvB8B,SAAS,EAAE5B,YAAY;EACvB6B,MAAM,EAAEP;AACV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}