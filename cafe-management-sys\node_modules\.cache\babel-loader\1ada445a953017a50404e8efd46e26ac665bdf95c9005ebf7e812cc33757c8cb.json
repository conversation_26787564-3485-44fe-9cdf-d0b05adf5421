{"ast": null, "code": "var _jsxFileName = \"D:\\\\Github_Repos\\\\cafe-mangement-system-react\\\\cafe-management-sys\\\\src\\\\components\\\\ProtectedRoute.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { Box, CircularProgress, Typography } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children,\n  requireAuth = true\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    loading\n  } = useAuth();\n  const location = useLocation();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '50vh',\n        gap: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 40\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this);\n  }\n  if (requireAuth && !isAuthenticated) {\n    // Redirect to login page with return url\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login-register\",\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this);\n  }\n  if (!requireAuth && isAuthenticated) {\n    var _location$state, _location$state$from;\n    // If user is already authenticated and trying to access login page,\n    // redirect to appropriate dashboard\n    const from = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : (_location$state$from = _location$state.from) === null || _location$state$from === void 0 ? void 0 : _location$state$from.pathname) || '/';\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: from,\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 12\n    }, this);\n  }\n  return children;\n};\n_s(ProtectedRoute, \"fNj96oVmPd4sFazcimgf9N7S8ao=\", false, function () {\n  return [useAuth, useLocation];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useAuth", "Box", "CircularProgress", "Typography", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "requireAuth", "_s", "isAuthenticated", "loading", "location", "sx", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "gap", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "color", "to", "state", "from", "replace", "_location$state", "_location$state$from", "pathname", "_c", "$RefreshReg$"], "sources": ["D:/Github_Repos/cafe-mangement-system-react/cafe-management-sys/src/components/ProtectedRoute.jsx"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { Box, CircularProgress, Typography } from '@mui/material';\n\nconst ProtectedRoute = ({ children, requireAuth = true }) => {\n  const { isAuthenticated, loading } = useAuth();\n  const location = useLocation();\n\n  if (loading) {\n    return (\n      <Box\n        sx={{\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          minHeight: '50vh',\n          gap: 2,\n        }}\n      >\n        <CircularProgress size={40} />\n        <Typography variant=\"body2\" color=\"text.secondary\">\n          Loading...\n        </Typography>\n      </Box>\n    );\n  }\n\n  if (requireAuth && !isAuthenticated) {\n    // Redirect to login page with return url\n    return (\n      <Navigate \n        to=\"/login-register\" \n        state={{ from: location }} \n        replace \n      />\n    );\n  }\n\n  if (!requireAuth && isAuthenticated) {\n    // If user is already authenticated and trying to access login page,\n    // redirect to appropriate dashboard\n    const from = location.state?.from?.pathname || '/';\n    return <Navigate to={from} replace />;\n  }\n\n  return children;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,GAAG,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,cAAc,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,WAAW,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC3D,MAAM;IAAEC,eAAe;IAAEC;EAAQ,CAAC,GAAGX,OAAO,CAAC,CAAC;EAC9C,MAAMY,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAE9B,IAAIY,OAAO,EAAE;IACX,oBACEN,OAAA,CAACJ,GAAG;MACFY,EAAE,EAAE;QACFC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,MAAM;QACjBC,GAAG,EAAE;MACP,CAAE;MAAAZ,QAAA,gBAEFF,OAAA,CAACH,gBAAgB;QAACkB,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9BnB,OAAA,CAACF,UAAU;QAACsB,OAAO,EAAC,OAAO;QAACC,KAAK,EAAC,gBAAgB;QAAAnB,QAAA,EAAC;MAEnD;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,IAAIhB,WAAW,IAAI,CAACE,eAAe,EAAE;IACnC;IACA,oBACEL,OAAA,CAACP,QAAQ;MACP6B,EAAE,EAAC,iBAAiB;MACpBC,KAAK,EAAE;QAAEC,IAAI,EAAEjB;MAAS,CAAE;MAC1BkB,OAAO;IAAA;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEN;EAEA,IAAI,CAAChB,WAAW,IAAIE,eAAe,EAAE;IAAA,IAAAqB,eAAA,EAAAC,oBAAA;IACnC;IACA;IACA,MAAMH,IAAI,GAAG,EAAAE,eAAA,GAAAnB,QAAQ,CAACgB,KAAK,cAAAG,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBF,IAAI,cAAAG,oBAAA,uBAApBA,oBAAA,CAAsBC,QAAQ,KAAI,GAAG;IAClD,oBAAO5B,OAAA,CAACP,QAAQ;MAAC6B,EAAE,EAAEE,IAAK;MAACC,OAAO;IAAA;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACvC;EAEA,OAAOjB,QAAQ;AACjB,CAAC;AAACE,EAAA,CA3CIH,cAAc;EAAA,QACmBN,OAAO,EAC3BD,WAAW;AAAA;AAAAmC,EAAA,GAFxB5B,cAAc;AA6CpB,eAAeA,cAAc;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}