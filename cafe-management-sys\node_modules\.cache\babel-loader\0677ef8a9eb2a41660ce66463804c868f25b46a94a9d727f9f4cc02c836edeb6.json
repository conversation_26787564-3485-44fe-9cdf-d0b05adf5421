{"ast": null, "code": "var _jsxFileName = \"D:\\\\Github_Repos\\\\cafe-mangement-system-react\\\\cafe-management-sys\\\\src\\\\pages\\\\admin\\\\AdminAnalytics.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Card, CardContent, Grid, Button, ButtonGroup, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip, LinearProgress, IconButton } from '@mui/material';\nimport { TrendingUp, TrendingDown, BarChart, PieChart, Timeline, Download, Refresh, DateRange } from '@mui/icons-material';\nimport { analyticsAPI } from '../../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AdminAnalytics = () => {\n  _s();\n  var _analyticsData$dashbo, _analyticsData$dashbo2, _analyticsData$dashbo3, _analyticsData$dashbo4, _analyticsData$dashbo5, _analyticsData$dashbo6, _analyticsData$custom, _analyticsData$custom2, _analyticsData$custom3, _analyticsData$produc, _analyticsData$custom4, _analyticsData$custom5, _analyticsData$custom6, _analyticsData$timeAn, _analyticsData$timeAn2, _analyticsData$timeAn3, _analyticsData$timeAn4;\n  const [loading, setLoading] = useState(true);\n  const [period, setPeriod] = useState('30d');\n  const [analyticsData, setAnalyticsData] = useState(null);\n  const [lastUpdated, setLastUpdated] = useState(new Date());\n  const periods = [{\n    value: '7d',\n    label: '7 Days'\n  }, {\n    value: '30d',\n    label: '30 Days'\n  }, {\n    value: '90d',\n    label: '90 Days'\n  }, {\n    value: '1y',\n    label: '1 Year'\n  }];\n  useEffect(() => {\n    fetchAnalyticsData();\n  }, [period]);\n  const fetchAnalyticsData = async () => {\n    try {\n      setLoading(true);\n      const [dashboardStats, revenueStats, orderStats] = await Promise.all([analyticsAPI.getDashboardStats(), analyticsAPI.getRevenueStats(period), analyticsAPI.getOrderStats(period)]);\n      setAnalyticsData({\n        dashboard: dashboardStats,\n        revenue: revenueStats,\n        orders: orderStats,\n        // Mock additional analytics data\n        customerMetrics: {\n          totalCustomers: 1247,\n          newCustomers: 89,\n          returningCustomers: 1158,\n          customerRetentionRate: 92.8\n        },\n        productPerformance: [{\n          name: 'Latte',\n          orders: 342,\n          revenue: 1539,\n          growth: 15.2\n        }, {\n          name: 'Cappuccino',\n          orders: 298,\n          revenue: 1192,\n          growth: 8.7\n        }, {\n          name: 'Espresso',\n          orders: 256,\n          revenue: 640,\n          growth: -2.3\n        }, {\n          name: 'Americano',\n          orders: 189,\n          revenue: 567,\n          growth: 12.1\n        }, {\n          name: 'Mocha',\n          orders: 167,\n          revenue: 751,\n          growth: 22.4\n        }],\n        timeAnalytics: {\n          peakHours: [{\n            hour: '8:00 AM',\n            orders: 45\n          }, {\n            hour: '12:00 PM',\n            orders: 52\n          }, {\n            hour: '3:00 PM',\n            orders: 38\n          }, {\n            hour: '6:00 PM',\n            orders: 29\n          }],\n          busyDays: [{\n            day: 'Monday',\n            orders: 156\n          }, {\n            day: 'Tuesday',\n            orders: 142\n          }, {\n            day: 'Wednesday',\n            orders: 167\n          }, {\n            day: 'Thursday',\n            orders: 189\n          }, {\n            day: 'Friday',\n            orders: 234\n          }, {\n            day: 'Saturday',\n            orders: 298\n          }, {\n            day: 'Sunday',\n            orders: 201\n          }]\n        }\n      });\n      setLastUpdated(new Date());\n    } catch (error) {\n      console.error('Error fetching analytics data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const MetricCard = ({\n    title,\n    value,\n    change,\n    icon,\n    color = 'primary',\n    suffix = ''\n  }) => /*#__PURE__*/_jsxDEV(Card, {\n    sx: {\n      height: '100%'\n    },\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            color: `${color}.main`\n          },\n          children: icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 0.5\n          },\n          children: change !== undefined && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [change > 0 ? /*#__PURE__*/_jsxDEV(TrendingUp, {\n              color: \"success\",\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 19\n            }, this) : change < 0 ? /*#__PURE__*/_jsxDEV(TrendingDown, {\n              color: \"error\",\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 19\n            }, this) : null, /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: change > 0 ? 'success.main' : change < 0 ? 'error.main' : 'text.secondary',\n              fontWeight: 600,\n              children: [change > 0 ? '+' : '', change, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        color: `${color}.main`,\n        fontWeight: 700,\n        gutterBottom: true,\n        children: [value, suffix]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 104,\n    columnNumber: 5\n  }, this);\n  if (loading && !analyticsData) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          mb: 3,\n          fontWeight: 600\n        },\n        children: \"Analytics Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          fontWeight: 600\n        },\n        children: \"Analytics Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          color: \"text.secondary\",\n          children: [\"Last updated: \", lastUpdated.toLocaleTimeString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: fetchAnalyticsData,\n          disabled: loading,\n          children: /*#__PURE__*/_jsxDEV(Refresh, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(Download, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 49\n          }, this),\n          children: \"Export Report\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Analysis Period\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ButtonGroup, {\n            variant: \"outlined\",\n            size: \"small\",\n            children: periods.map(p => /*#__PURE__*/_jsxDEV(Button, {\n              variant: period === p.value ? 'contained' : 'outlined',\n              onClick: () => setPeriod(p.value),\n              children: p.label\n            }, p.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Total Revenue\",\n          value: `$${(analyticsData === null || analyticsData === void 0 ? void 0 : (_analyticsData$dashbo = analyticsData.dashboard) === null || _analyticsData$dashbo === void 0 ? void 0 : (_analyticsData$dashbo2 = _analyticsData$dashbo.totalRevenue) === null || _analyticsData$dashbo2 === void 0 ? void 0 : _analyticsData$dashbo2.toLocaleString()) || '0'}`,\n          change: analyticsData === null || analyticsData === void 0 ? void 0 : (_analyticsData$dashbo3 = analyticsData.dashboard) === null || _analyticsData$dashbo3 === void 0 ? void 0 : _analyticsData$dashbo3.revenueChange,\n          icon: /*#__PURE__*/_jsxDEV(TrendingUp, {\n            fontSize: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 19\n          }, this),\n          color: \"success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Total Orders\",\n          value: (analyticsData === null || analyticsData === void 0 ? void 0 : (_analyticsData$dashbo4 = analyticsData.dashboard) === null || _analyticsData$dashbo4 === void 0 ? void 0 : (_analyticsData$dashbo5 = _analyticsData$dashbo4.ordersToday) === null || _analyticsData$dashbo5 === void 0 ? void 0 : _analyticsData$dashbo5.toLocaleString()) || '0',\n          change: analyticsData === null || analyticsData === void 0 ? void 0 : (_analyticsData$dashbo6 = analyticsData.dashboard) === null || _analyticsData$dashbo6 === void 0 ? void 0 : _analyticsData$dashbo6.ordersChange,\n          icon: /*#__PURE__*/_jsxDEV(BarChart, {\n            fontSize: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 19\n          }, this),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Total Customers\",\n          value: (analyticsData === null || analyticsData === void 0 ? void 0 : (_analyticsData$custom = analyticsData.customerMetrics) === null || _analyticsData$custom === void 0 ? void 0 : (_analyticsData$custom2 = _analyticsData$custom.totalCustomers) === null || _analyticsData$custom2 === void 0 ? void 0 : _analyticsData$custom2.toLocaleString()) || '0',\n          change: 12.5,\n          icon: /*#__PURE__*/_jsxDEV(PieChart, {\n            fontSize: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 19\n          }, this),\n          color: \"info\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Retention Rate\",\n          value: (analyticsData === null || analyticsData === void 0 ? void 0 : (_analyticsData$custom3 = analyticsData.customerMetrics) === null || _analyticsData$custom3 === void 0 ? void 0 : _analyticsData$custom3.customerRetentionRate) || '0',\n          change: 2.3,\n          icon: /*#__PURE__*/_jsxDEV(Timeline, {\n            fontSize: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 19\n          }, this),\n          color: \"warning\",\n          suffix: \"%\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Top Performing Products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n              children: /*#__PURE__*/_jsxDEV(Table, {\n                children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                  children: /*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Product\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 242,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: \"Orders\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 243,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: \"Revenue\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 244,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: \"Growth\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 245,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                  children: analyticsData === null || analyticsData === void 0 ? void 0 : (_analyticsData$produc = analyticsData.productPerformance) === null || _analyticsData$produc === void 0 ? void 0 : _analyticsData$produc.map(product => /*#__PURE__*/_jsxDEV(TableRow, {\n                    hover: true,\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"subtitle2\",\n                        fontWeight: 600,\n                        children: product.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 252,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 251,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: product.orders\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 257,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 256,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"subtitle2\",\n                        fontWeight: 600,\n                        children: [\"$\", product.revenue]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 262,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 261,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: /*#__PURE__*/_jsxDEV(Chip, {\n                        label: `${product.growth > 0 ? '+' : ''}${product.growth}%`,\n                        color: product.growth > 0 ? 'success' : product.growth < 0 ? 'error' : 'default',\n                        size: \"small\",\n                        icon: product.growth > 0 ? /*#__PURE__*/_jsxDEV(TrendingUp, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 271,\n                          columnNumber: 56\n                        }, this) : product.growth < 0 ? /*#__PURE__*/_jsxDEV(TrendingDown, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 271,\n                          columnNumber: 94\n                        }, this) : null\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 267,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 266,\n                      columnNumber: 25\n                    }, this)]\n                  }, product.name, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Customer Insights\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  gutterBottom: true,\n                  children: \"New Customers\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  fontWeight: 600,\n                  color: \"primary\",\n                  children: (analyticsData === null || analyticsData === void 0 ? void 0 : (_analyticsData$custom4 = analyticsData.customerMetrics) === null || _analyticsData$custom4 === void 0 ? void 0 : _analyticsData$custom4.newCustomers) || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  gutterBottom: true,\n                  children: \"Returning Customers\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  fontWeight: 600,\n                  color: \"success.main\",\n                  children: (analyticsData === null || analyticsData === void 0 ? void 0 : (_analyticsData$custom5 = analyticsData.customerMetrics) === null || _analyticsData$custom5 === void 0 ? void 0 : _analyticsData$custom5.returningCustomers) || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  gutterBottom: true,\n                  children: \"Retention Rate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  fontWeight: 600,\n                  color: \"info.main\",\n                  children: [(analyticsData === null || analyticsData === void 0 ? void 0 : (_analyticsData$custom6 = analyticsData.customerMetrics) === null || _analyticsData$custom6 === void 0 ? void 0 : _analyticsData$custom6.customerRetentionRate) || 0, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Peak Hours\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: 2\n              },\n              children: analyticsData === null || analyticsData === void 0 ? void 0 : (_analyticsData$timeAn = analyticsData.timeAnalytics) === null || _analyticsData$timeAn === void 0 ? void 0 : (_analyticsData$timeAn2 = _analyticsData$timeAn.peakHours) === null || _analyticsData$timeAn2 === void 0 ? void 0 : _analyticsData$timeAn2.map((hour, index) => /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: hour.hour\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(LinearProgress, {\n                    variant: \"determinate\",\n                    value: hour.orders / 60 * 100,\n                    sx: {\n                      width: 100,\n                      height: 8,\n                      borderRadius: 4\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle2\",\n                    fontWeight: 600,\n                    children: hour.orders\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 21\n                }, this)]\n              }, hour.hour, true, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Weekly Performance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: 2\n              },\n              children: analyticsData === null || analyticsData === void 0 ? void 0 : (_analyticsData$timeAn3 = analyticsData.timeAnalytics) === null || _analyticsData$timeAn3 === void 0 ? void 0 : (_analyticsData$timeAn4 = _analyticsData$timeAn3.busyDays) === null || _analyticsData$timeAn4 === void 0 ? void 0 : _analyticsData$timeAn4.map((day, index) => /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: day.day\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(LinearProgress, {\n                    variant: \"determinate\",\n                    value: day.orders / 300 * 100,\n                    sx: {\n                      width: 100,\n                      height: 8,\n                      borderRadius: 4\n                    },\n                    color: day.orders > 200 ? 'success' : day.orders > 150 ? 'warning' : 'error'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 364,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle2\",\n                    fontWeight: 600,\n                    children: day.orders\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 370,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 21\n                }, this)]\n              }, day.day, true, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 151,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminAnalytics, \"BdVc1n14NOFCDLAQ+rsgQ3Pb2EM=\");\n_c = AdminAnalytics;\nexport default AdminAnalytics;\nvar _c;\n$RefreshReg$(_c, \"AdminAnalytics\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "<PERSON><PERSON>", "ButtonGroup", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Chip", "LinearProgress", "IconButton", "TrendingUp", "TrendingDown", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Timeline", "Download", "Refresh", "DateRange", "analyticsAPI", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AdminAnalytics", "_s", "_analyticsData$dashbo", "_analyticsData$dashbo2", "_analyticsData$dashbo3", "_analyticsData$dashbo4", "_analyticsData$dashbo5", "_analyticsData$dashbo6", "_analyticsData$custom", "_analyticsData$custom2", "_analyticsData$custom3", "_analyticsData$produc", "_analyticsData$custom4", "_analyticsData$custom5", "_analyticsData$custom6", "_analyticsData$timeAn", "_analyticsData$timeAn2", "_analyticsData$timeAn3", "_analyticsData$timeAn4", "loading", "setLoading", "period", "<PERSON><PERSON><PERSON><PERSON>", "analyticsData", "setAnalyticsData", "lastUpdated", "setLastUpdated", "Date", "periods", "value", "label", "fetchAnalyticsData", "dashboardStats", "revenueStats", "orderStats", "Promise", "all", "getDashboardStats", "getRevenueStats", "getOrderStats", "dashboard", "revenue", "orders", "customerMetrics", "totalCustomers", "newCustomers", "returningCustomers", "customerRetentionRate", "productPerformance", "name", "growth", "timeAnalytics", "peakHours", "hour", "busyDays", "day", "error", "console", "MetricCard", "title", "change", "icon", "color", "suffix", "sx", "height", "children", "display", "alignItems", "justifyContent", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gap", "undefined", "fontSize", "variant", "fontWeight", "gutterBottom", "p", "toLocaleTimeString", "onClick", "disabled", "startIcon", "size", "map", "container", "spacing", "item", "xs", "sm", "md", "totalRevenue", "toLocaleString", "revenueChange", "ordersToday", "ordersChange", "align", "product", "hover", "flexDirection", "index", "width", "borderRadius", "_c", "$RefreshReg$"], "sources": ["D:/Github_Repos/cafe-mangement-system-react/cafe-management-sys/src/pages/admin/AdminAnalytics.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  Grid,\n  Button,\n  ButtonGroup,\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Chip,\n  LinearProgress,\n  IconButton,\n} from '@mui/material';\nimport {\n  TrendingUp,\n  TrendingDown,\n  BarChart,\n  PieChart,\n  Timeline,\n  Download,\n  Refresh,\n  DateRange,\n} from '@mui/icons-material';\nimport { analyticsAPI } from '../../services/api';\n\nconst AdminAnalytics = () => {\n  const [loading, setLoading] = useState(true);\n  const [period, setPeriod] = useState('30d');\n  const [analyticsData, setAnalyticsData] = useState(null);\n  const [lastUpdated, setLastUpdated] = useState(new Date());\n\n  const periods = [\n    { value: '7d', label: '7 Days' },\n    { value: '30d', label: '30 Days' },\n    { value: '90d', label: '90 Days' },\n    { value: '1y', label: '1 Year' },\n  ];\n\n  useEffect(() => {\n    fetchAnalyticsData();\n  }, [period]);\n\n  const fetchAnalyticsData = async () => {\n    try {\n      setLoading(true);\n      const [dashboardStats, revenueStats, orderStats] = await Promise.all([\n        analyticsAPI.getDashboardStats(),\n        analyticsAPI.getRevenueStats(period),\n        analyticsAPI.getOrderStats(period),\n      ]);\n      \n      setAnalyticsData({\n        dashboard: dashboardStats,\n        revenue: revenueStats,\n        orders: orderStats,\n        // Mock additional analytics data\n        customerMetrics: {\n          totalCustomers: 1247,\n          newCustomers: 89,\n          returningCustomers: 1158,\n          customerRetentionRate: 92.8,\n        },\n        productPerformance: [\n          { name: 'Latte', orders: 342, revenue: 1539, growth: 15.2 },\n          { name: 'Cappuccino', orders: 298, revenue: 1192, growth: 8.7 },\n          { name: 'Espresso', orders: 256, revenue: 640, growth: -2.3 },\n          { name: 'Americano', orders: 189, revenue: 567, growth: 12.1 },\n          { name: 'Mocha', orders: 167, revenue: 751, growth: 22.4 },\n        ],\n        timeAnalytics: {\n          peakHours: [\n            { hour: '8:00 AM', orders: 45 },\n            { hour: '12:00 PM', orders: 52 },\n            { hour: '3:00 PM', orders: 38 },\n            { hour: '6:00 PM', orders: 29 },\n          ],\n          busyDays: [\n            { day: 'Monday', orders: 156 },\n            { day: 'Tuesday', orders: 142 },\n            { day: 'Wednesday', orders: 167 },\n            { day: 'Thursday', orders: 189 },\n            { day: 'Friday', orders: 234 },\n            { day: 'Saturday', orders: 298 },\n            { day: 'Sunday', orders: 201 },\n          ],\n        },\n      });\n      setLastUpdated(new Date());\n    } catch (error) {\n      console.error('Error fetching analytics data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const MetricCard = ({ title, value, change, icon, color = 'primary', suffix = '' }) => (\n    <Card sx={{ height: '100%' }}>\n      <CardContent>\n        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>\n          <Box sx={{ color: `${color}.main` }}>\n            {icon}\n          </Box>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n            {change !== undefined && (\n              <>\n                {change > 0 ? (\n                  <TrendingUp color=\"success\" fontSize=\"small\" />\n                ) : change < 0 ? (\n                  <TrendingDown color=\"error\" fontSize=\"small\" />\n                ) : null}\n                <Typography\n                  variant=\"caption\"\n                  color={change > 0 ? 'success.main' : change < 0 ? 'error.main' : 'text.secondary'}\n                  fontWeight={600}\n                >\n                  {change > 0 ? '+' : ''}{change}%\n                </Typography>\n              </>\n            )}\n          </Box>\n        </Box>\n        <Typography variant=\"h4\" color={`${color}.main`} fontWeight={700} gutterBottom>\n          {value}{suffix}\n        </Typography>\n        <Typography variant=\"body2\" color=\"text.secondary\">\n          {title}\n        </Typography>\n      </CardContent>\n    </Card>\n  );\n\n  if (loading && !analyticsData) {\n    return (\n      <Box sx={{ p: 3 }}>\n        <Typography variant=\"h4\" sx={{ mb: 3, fontWeight: 600 }}>\n          Analytics Dashboard\n        </Typography>\n        <LinearProgress />\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ p: 3 }}>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h4\" sx={{ fontWeight: 600 }}>\n          Analytics Dashboard\n        </Typography>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          <Typography variant=\"caption\" color=\"text.secondary\">\n            Last updated: {lastUpdated.toLocaleTimeString()}\n          </Typography>\n          <IconButton onClick={fetchAnalyticsData} disabled={loading}>\n            <Refresh />\n          </IconButton>\n          <Button variant=\"outlined\" startIcon={<Download />}>\n            Export Report\n          </Button>\n        </Box>\n      </Box>\n\n      {/* Period Selection */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"h6\">Analysis Period</Typography>\n            <ButtonGroup variant=\"outlined\" size=\"small\">\n              {periods.map((p) => (\n                <Button\n                  key={p.value}\n                  variant={period === p.value ? 'contained' : 'outlined'}\n                  onClick={() => setPeriod(p.value)}\n                >\n                  {p.label}\n                </Button>\n              ))}\n            </ButtonGroup>\n          </Box>\n        </CardContent>\n      </Card>\n\n      {/* Key Metrics */}\n      <Grid container spacing={3} sx={{ mb: 3 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Total Revenue\"\n            value={`$${analyticsData?.dashboard?.totalRevenue?.toLocaleString() || '0'}`}\n            change={analyticsData?.dashboard?.revenueChange}\n            icon={<TrendingUp fontSize=\"large\" />}\n            color=\"success\"\n          />\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Total Orders\"\n            value={analyticsData?.dashboard?.ordersToday?.toLocaleString() || '0'}\n            change={analyticsData?.dashboard?.ordersChange}\n            icon={<BarChart fontSize=\"large\" />}\n            color=\"primary\"\n          />\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Total Customers\"\n            value={analyticsData?.customerMetrics?.totalCustomers?.toLocaleString() || '0'}\n            change={12.5}\n            icon={<PieChart fontSize=\"large\" />}\n            color=\"info\"\n          />\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Retention Rate\"\n            value={analyticsData?.customerMetrics?.customerRetentionRate || '0'}\n            change={2.3}\n            icon={<Timeline fontSize=\"large\" />}\n            color=\"warning\"\n            suffix=\"%\"\n          />\n        </Grid>\n      </Grid>\n\n      {/* Product Performance */}\n      <Grid container spacing={3} sx={{ mb: 3 }}>\n        <Grid item xs={12} md={8}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Top Performing Products\n              </Typography>\n              <TableContainer>\n                <Table>\n                  <TableHead>\n                    <TableRow>\n                      <TableCell>Product</TableCell>\n                      <TableCell align=\"right\">Orders</TableCell>\n                      <TableCell align=\"right\">Revenue</TableCell>\n                      <TableCell align=\"right\">Growth</TableCell>\n                    </TableRow>\n                  </TableHead>\n                  <TableBody>\n                    {analyticsData?.productPerformance?.map((product) => (\n                      <TableRow key={product.name} hover>\n                        <TableCell>\n                          <Typography variant=\"subtitle2\" fontWeight={600}>\n                            {product.name}\n                          </Typography>\n                        </TableCell>\n                        <TableCell align=\"right\">\n                          <Typography variant=\"body2\">\n                            {product.orders}\n                          </Typography>\n                        </TableCell>\n                        <TableCell align=\"right\">\n                          <Typography variant=\"subtitle2\" fontWeight={600}>\n                            ${product.revenue}\n                          </Typography>\n                        </TableCell>\n                        <TableCell align=\"right\">\n                          <Chip\n                            label={`${product.growth > 0 ? '+' : ''}${product.growth}%`}\n                            color={product.growth > 0 ? 'success' : product.growth < 0 ? 'error' : 'default'}\n                            size=\"small\"\n                            icon={product.growth > 0 ? <TrendingUp /> : product.growth < 0 ? <TrendingDown /> : null}\n                          />\n                        </TableCell>\n                      </TableRow>\n                    ))}\n                  </TableBody>\n                </Table>\n              </TableContainer>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} md={4}>\n          <Card sx={{ height: '100%' }}>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Customer Insights\n              </Typography>\n              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>\n                <Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                    New Customers\n                  </Typography>\n                  <Typography variant=\"h5\" fontWeight={600} color=\"primary\">\n                    {analyticsData?.customerMetrics?.newCustomers || 0}\n                  </Typography>\n                </Box>\n                <Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                    Returning Customers\n                  </Typography>\n                  <Typography variant=\"h5\" fontWeight={600} color=\"success.main\">\n                    {analyticsData?.customerMetrics?.returningCustomers || 0}\n                  </Typography>\n                </Box>\n                <Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                    Retention Rate\n                  </Typography>\n                  <Typography variant=\"h5\" fontWeight={600} color=\"info.main\">\n                    {analyticsData?.customerMetrics?.customerRetentionRate || 0}%\n                  </Typography>\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Time Analytics */}\n      <Grid container spacing={3}>\n        <Grid item xs={12} md={6}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Peak Hours\n              </Typography>\n              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>\n                {analyticsData?.timeAnalytics?.peakHours?.map((hour, index) => (\n                  <Box key={hour.hour} sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                    <Typography variant=\"body2\">\n                      {hour.hour}\n                    </Typography>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                      <LinearProgress\n                        variant=\"determinate\"\n                        value={(hour.orders / 60) * 100}\n                        sx={{ width: 100, height: 8, borderRadius: 4 }}\n                      />\n                      <Typography variant=\"subtitle2\" fontWeight={600}>\n                        {hour.orders}\n                      </Typography>\n                    </Box>\n                  </Box>\n                ))}\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Weekly Performance\n              </Typography>\n              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>\n                {analyticsData?.timeAnalytics?.busyDays?.map((day, index) => (\n                  <Box key={day.day} sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                    <Typography variant=\"body2\">\n                      {day.day}\n                    </Typography>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                      <LinearProgress\n                        variant=\"determinate\"\n                        value={(day.orders / 300) * 100}\n                        sx={{ width: 100, height: 8, borderRadius: 4 }}\n                        color={day.orders > 200 ? 'success' : day.orders > 150 ? 'warning' : 'error'}\n                      />\n                      <Typography variant=\"subtitle2\" fontWeight={600}>\n                        {day.orders}\n                      </Typography>\n                    </Box>\n                  </Box>\n                ))}\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nexport default AdminAnalytics;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,cAAc,EACdC,UAAU,QACL,eAAe;AACtB,SACEC,UAAU,EACVC,YAAY,EACZC,QAAQ,EACRC,QAAQ,EACRC,QAAQ,EACRC,QAAQ,EACRC,OAAO,EACPC,SAAS,QACJ,qBAAqB;AAC5B,SAASC,YAAY,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC3B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqD,MAAM,EAAEC,SAAS,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACuD,aAAa,EAAEC,gBAAgB,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACyD,WAAW,EAAEC,cAAc,CAAC,GAAG1D,QAAQ,CAAC,IAAI2D,IAAI,CAAC,CAAC,CAAC;EAE1D,MAAMC,OAAO,GAAG,CACd;IAAEC,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAS,CAAC,EAChC;IAAED,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAU,CAAC,EAClC;IAAED,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAU,CAAC,EAClC;IAAED,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAS,CAAC,CACjC;EAED7D,SAAS,CAAC,MAAM;IACd8D,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACV,MAAM,CAAC,CAAC;EAEZ,MAAMU,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFX,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM,CAACY,cAAc,EAAEC,YAAY,EAAEC,UAAU,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACnEzC,YAAY,CAAC0C,iBAAiB,CAAC,CAAC,EAChC1C,YAAY,CAAC2C,eAAe,CAACjB,MAAM,CAAC,EACpC1B,YAAY,CAAC4C,aAAa,CAAClB,MAAM,CAAC,CACnC,CAAC;MAEFG,gBAAgB,CAAC;QACfgB,SAAS,EAAER,cAAc;QACzBS,OAAO,EAAER,YAAY;QACrBS,MAAM,EAAER,UAAU;QAClB;QACAS,eAAe,EAAE;UACfC,cAAc,EAAE,IAAI;UACpBC,YAAY,EAAE,EAAE;UAChBC,kBAAkB,EAAE,IAAI;UACxBC,qBAAqB,EAAE;QACzB,CAAC;QACDC,kBAAkB,EAAE,CAClB;UAAEC,IAAI,EAAE,OAAO;UAAEP,MAAM,EAAE,GAAG;UAAED,OAAO,EAAE,IAAI;UAAES,MAAM,EAAE;QAAK,CAAC,EAC3D;UAAED,IAAI,EAAE,YAAY;UAAEP,MAAM,EAAE,GAAG;UAAED,OAAO,EAAE,IAAI;UAAES,MAAM,EAAE;QAAI,CAAC,EAC/D;UAAED,IAAI,EAAE,UAAU;UAAEP,MAAM,EAAE,GAAG;UAAED,OAAO,EAAE,GAAG;UAAES,MAAM,EAAE,CAAC;QAAI,CAAC,EAC7D;UAAED,IAAI,EAAE,WAAW;UAAEP,MAAM,EAAE,GAAG;UAAED,OAAO,EAAE,GAAG;UAAES,MAAM,EAAE;QAAK,CAAC,EAC9D;UAAED,IAAI,EAAE,OAAO;UAAEP,MAAM,EAAE,GAAG;UAAED,OAAO,EAAE,GAAG;UAAES,MAAM,EAAE;QAAK,CAAC,CAC3D;QACDC,aAAa,EAAE;UACbC,SAAS,EAAE,CACT;YAAEC,IAAI,EAAE,SAAS;YAAEX,MAAM,EAAE;UAAG,CAAC,EAC/B;YAAEW,IAAI,EAAE,UAAU;YAAEX,MAAM,EAAE;UAAG,CAAC,EAChC;YAAEW,IAAI,EAAE,SAAS;YAAEX,MAAM,EAAE;UAAG,CAAC,EAC/B;YAAEW,IAAI,EAAE,SAAS;YAAEX,MAAM,EAAE;UAAG,CAAC,CAChC;UACDY,QAAQ,EAAE,CACR;YAAEC,GAAG,EAAE,QAAQ;YAAEb,MAAM,EAAE;UAAI,CAAC,EAC9B;YAAEa,GAAG,EAAE,SAAS;YAAEb,MAAM,EAAE;UAAI,CAAC,EAC/B;YAAEa,GAAG,EAAE,WAAW;YAAEb,MAAM,EAAE;UAAI,CAAC,EACjC;YAAEa,GAAG,EAAE,UAAU;YAAEb,MAAM,EAAE;UAAI,CAAC,EAChC;YAAEa,GAAG,EAAE,QAAQ;YAAEb,MAAM,EAAE;UAAI,CAAC,EAC9B;YAAEa,GAAG,EAAE,UAAU;YAAEb,MAAM,EAAE;UAAI,CAAC,EAChC;YAAEa,GAAG,EAAE,QAAQ;YAAEb,MAAM,EAAE;UAAI,CAAC;QAElC;MACF,CAAC,CAAC;MACFhB,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,OAAO6B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD,CAAC,SAAS;MACRpC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsC,UAAU,GAAGA,CAAC;IAAEC,KAAK;IAAE9B,KAAK;IAAE+B,MAAM;IAAEC,IAAI;IAAEC,KAAK,GAAG,SAAS;IAAEC,MAAM,GAAG;EAAG,CAAC,kBAChFlE,OAAA,CAACzB,IAAI;IAAC4F,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAO,CAAE;IAAAC,QAAA,eAC3BrE,OAAA,CAACxB,WAAW;MAAA6F,QAAA,gBACVrE,OAAA,CAAC3B,GAAG;QAAC8F,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,cAAc,EAAE,eAAe;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACzFrE,OAAA,CAAC3B,GAAG;UAAC8F,EAAE,EAAE;YAAEF,KAAK,EAAE,GAAGA,KAAK;UAAQ,CAAE;UAAAI,QAAA,EACjCL;QAAI;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACN7E,OAAA,CAAC3B,GAAG;UAAC8F,EAAE,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEO,GAAG,EAAE;UAAI,CAAE;UAAAT,QAAA,EAC1DN,MAAM,KAAKgB,SAAS,iBACnB/E,OAAA,CAAAE,SAAA;YAAAmE,QAAA,GACGN,MAAM,GAAG,CAAC,gBACT/D,OAAA,CAACV,UAAU;cAAC2E,KAAK,EAAC,SAAS;cAACe,QAAQ,EAAC;YAAO;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAC7Cd,MAAM,GAAG,CAAC,gBACZ/D,OAAA,CAACT,YAAY;cAAC0E,KAAK,EAAC,OAAO;cAACe,QAAQ,EAAC;YAAO;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAC7C,IAAI,eACR7E,OAAA,CAAC1B,UAAU;cACT2G,OAAO,EAAC,SAAS;cACjBhB,KAAK,EAAEF,MAAM,GAAG,CAAC,GAAG,cAAc,GAAGA,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,gBAAiB;cAClFmB,UAAU,EAAE,GAAI;cAAAb,QAAA,GAEfN,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEA,MAAM,EAAC,GACjC;YAAA;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA,eACb;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN7E,OAAA,CAAC1B,UAAU;QAAC2G,OAAO,EAAC,IAAI;QAAChB,KAAK,EAAE,GAAGA,KAAK,OAAQ;QAACiB,UAAU,EAAE,GAAI;QAACC,YAAY;QAAAd,QAAA,GAC3ErC,KAAK,EAAEkC,MAAM;MAAA;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACb7E,OAAA,CAAC1B,UAAU;QAAC2G,OAAO,EAAC,OAAO;QAAChB,KAAK,EAAC,gBAAgB;QAAAI,QAAA,EAC/CP;MAAK;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACP;EAED,IAAIvD,OAAO,IAAI,CAACI,aAAa,EAAE;IAC7B,oBACE1B,OAAA,CAAC3B,GAAG;MAAC8F,EAAE,EAAE;QAAEiB,CAAC,EAAE;MAAE,CAAE;MAAAf,QAAA,gBAChBrE,OAAA,CAAC1B,UAAU;QAAC2G,OAAO,EAAC,IAAI;QAACd,EAAE,EAAE;UAAEM,EAAE,EAAE,CAAC;UAAES,UAAU,EAAE;QAAI,CAAE;QAAAb,QAAA,EAAC;MAEzD;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb7E,OAAA,CAACZ,cAAc;QAAAsF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC;EAEV;EAEA,oBACE7E,OAAA,CAAC3B,GAAG;IAAC8F,EAAE,EAAE;MAAEiB,CAAC,EAAE;IAAE,CAAE;IAAAf,QAAA,gBAChBrE,OAAA,CAAC3B,GAAG;MAAC8F,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEE,cAAc,EAAE,eAAe;QAAED,UAAU,EAAE,QAAQ;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzFrE,OAAA,CAAC1B,UAAU;QAAC2G,OAAO,EAAC,IAAI;QAACd,EAAE,EAAE;UAAEe,UAAU,EAAE;QAAI,CAAE;QAAAb,QAAA,EAAC;MAElD;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb7E,OAAA,CAAC3B,GAAG;QAAC8F,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEO,GAAG,EAAE;QAAE,CAAE;QAAAT,QAAA,gBACzDrE,OAAA,CAAC1B,UAAU;UAAC2G,OAAO,EAAC,SAAS;UAAChB,KAAK,EAAC,gBAAgB;UAAAI,QAAA,GAAC,gBACrC,EAACzC,WAAW,CAACyD,kBAAkB,CAAC,CAAC;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eACb7E,OAAA,CAACX,UAAU;UAACiG,OAAO,EAAEpD,kBAAmB;UAACqD,QAAQ,EAAEjE,OAAQ;UAAA+C,QAAA,eACzDrE,OAAA,CAACJ,OAAO;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACb7E,OAAA,CAACtB,MAAM;UAACuG,OAAO,EAAC,UAAU;UAACO,SAAS,eAAExF,OAAA,CAACL,QAAQ;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAR,QAAA,EAAC;QAEpD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7E,OAAA,CAACzB,IAAI;MAAC4F,EAAE,EAAE;QAAEM,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eAClBrE,OAAA,CAACxB,WAAW;QAAA6F,QAAA,eACVrE,OAAA,CAAC3B,GAAG;UAAC8F,EAAE,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEE,cAAc,EAAE,eAAe;YAAED,UAAU,EAAE;UAAS,CAAE;UAAAF,QAAA,gBAClFrE,OAAA,CAAC1B,UAAU;YAAC2G,OAAO,EAAC,IAAI;YAAAZ,QAAA,EAAC;UAAe;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACrD7E,OAAA,CAACrB,WAAW;YAACsG,OAAO,EAAC,UAAU;YAACQ,IAAI,EAAC,OAAO;YAAApB,QAAA,EACzCtC,OAAO,CAAC2D,GAAG,CAAEN,CAAC,iBACbpF,OAAA,CAACtB,MAAM;cAELuG,OAAO,EAAEzD,MAAM,KAAK4D,CAAC,CAACpD,KAAK,GAAG,WAAW,GAAG,UAAW;cACvDsD,OAAO,EAAEA,CAAA,KAAM7D,SAAS,CAAC2D,CAAC,CAACpD,KAAK,CAAE;cAAAqC,QAAA,EAEjCe,CAAC,CAACnD;YAAK,GAJHmD,CAAC,CAACpD,KAAK;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKN,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGP7E,OAAA,CAACvB,IAAI;MAACkH,SAAS;MAACC,OAAO,EAAE,CAAE;MAACzB,EAAE,EAAE;QAAEM,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACxCrE,OAAA,CAACvB,IAAI;QAACoH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA3B,QAAA,eAC9BrE,OAAA,CAAC6D,UAAU;UACTC,KAAK,EAAC,eAAe;UACrB9B,KAAK,EAAE,IAAI,CAAAN,aAAa,aAAbA,aAAa,wBAAArB,qBAAA,GAAbqB,aAAa,CAAEiB,SAAS,cAAAtC,qBAAA,wBAAAC,sBAAA,GAAxBD,qBAAA,CAA0B4F,YAAY,cAAA3F,sBAAA,uBAAtCA,sBAAA,CAAwC4F,cAAc,CAAC,CAAC,KAAI,GAAG,EAAG;UAC7EnC,MAAM,EAAErC,aAAa,aAAbA,aAAa,wBAAAnB,sBAAA,GAAbmB,aAAa,CAAEiB,SAAS,cAAApC,sBAAA,uBAAxBA,sBAAA,CAA0B4F,aAAc;UAChDnC,IAAI,eAAEhE,OAAA,CAACV,UAAU;YAAC0F,QAAQ,EAAC;UAAO;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtCZ,KAAK,EAAC;QAAS;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACP7E,OAAA,CAACvB,IAAI;QAACoH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA3B,QAAA,eAC9BrE,OAAA,CAAC6D,UAAU;UACTC,KAAK,EAAC,cAAc;UACpB9B,KAAK,EAAE,CAAAN,aAAa,aAAbA,aAAa,wBAAAlB,sBAAA,GAAbkB,aAAa,CAAEiB,SAAS,cAAAnC,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA0B4F,WAAW,cAAA3F,sBAAA,uBAArCA,sBAAA,CAAuCyF,cAAc,CAAC,CAAC,KAAI,GAAI;UACtEnC,MAAM,EAAErC,aAAa,aAAbA,aAAa,wBAAAhB,sBAAA,GAAbgB,aAAa,CAAEiB,SAAS,cAAAjC,sBAAA,uBAAxBA,sBAAA,CAA0B2F,YAAa;UAC/CrC,IAAI,eAAEhE,OAAA,CAACR,QAAQ;YAACwF,QAAQ,EAAC;UAAO;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACpCZ,KAAK,EAAC;QAAS;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACP7E,OAAA,CAACvB,IAAI;QAACoH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA3B,QAAA,eAC9BrE,OAAA,CAAC6D,UAAU;UACTC,KAAK,EAAC,iBAAiB;UACvB9B,KAAK,EAAE,CAAAN,aAAa,aAAbA,aAAa,wBAAAf,qBAAA,GAAbe,aAAa,CAAEoB,eAAe,cAAAnC,qBAAA,wBAAAC,sBAAA,GAA9BD,qBAAA,CAAgCoC,cAAc,cAAAnC,sBAAA,uBAA9CA,sBAAA,CAAgDsF,cAAc,CAAC,CAAC,KAAI,GAAI;UAC/EnC,MAAM,EAAE,IAAK;UACbC,IAAI,eAAEhE,OAAA,CAACP,QAAQ;YAACuF,QAAQ,EAAC;UAAO;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACpCZ,KAAK,EAAC;QAAM;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACP7E,OAAA,CAACvB,IAAI;QAACoH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA3B,QAAA,eAC9BrE,OAAA,CAAC6D,UAAU;UACTC,KAAK,EAAC,gBAAgB;UACtB9B,KAAK,EAAE,CAAAN,aAAa,aAAbA,aAAa,wBAAAb,sBAAA,GAAba,aAAa,CAAEoB,eAAe,cAAAjC,sBAAA,uBAA9BA,sBAAA,CAAgCqC,qBAAqB,KAAI,GAAI;UACpEa,MAAM,EAAE,GAAI;UACZC,IAAI,eAAEhE,OAAA,CAACN,QAAQ;YAACsF,QAAQ,EAAC;UAAO;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACpCZ,KAAK,EAAC,SAAS;UACfC,MAAM,EAAC;QAAG;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP7E,OAAA,CAACvB,IAAI;MAACkH,SAAS;MAACC,OAAO,EAAE,CAAE;MAACzB,EAAE,EAAE;QAAEM,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACxCrE,OAAA,CAACvB,IAAI;QAACoH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA3B,QAAA,eACvBrE,OAAA,CAACzB,IAAI;UAAA8F,QAAA,eACHrE,OAAA,CAACxB,WAAW;YAAA6F,QAAA,gBACVrE,OAAA,CAAC1B,UAAU;cAAC2G,OAAO,EAAC,IAAI;cAACE,YAAY;cAAAd,QAAA,EAAC;YAEtC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb7E,OAAA,CAAChB,cAAc;cAAAqF,QAAA,eACbrE,OAAA,CAACnB,KAAK;gBAAAwF,QAAA,gBACJrE,OAAA,CAACf,SAAS;kBAAAoF,QAAA,eACRrE,OAAA,CAACd,QAAQ;oBAAAmF,QAAA,gBACPrE,OAAA,CAACjB,SAAS;sBAAAsF,QAAA,EAAC;oBAAO;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC9B7E,OAAA,CAACjB,SAAS;sBAACuH,KAAK,EAAC,OAAO;sBAAAjC,QAAA,EAAC;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC3C7E,OAAA,CAACjB,SAAS;sBAACuH,KAAK,EAAC,OAAO;sBAAAjC,QAAA,EAAC;oBAAO;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC5C7E,OAAA,CAACjB,SAAS;sBAACuH,KAAK,EAAC,OAAO;sBAAAjC,QAAA,EAAC;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACZ7E,OAAA,CAAClB,SAAS;kBAAAuF,QAAA,EACP3C,aAAa,aAAbA,aAAa,wBAAAZ,qBAAA,GAAbY,aAAa,CAAEyB,kBAAkB,cAAArC,qBAAA,uBAAjCA,qBAAA,CAAmC4E,GAAG,CAAEa,OAAO,iBAC9CvG,OAAA,CAACd,QAAQ;oBAAoBsH,KAAK;oBAAAnC,QAAA,gBAChCrE,OAAA,CAACjB,SAAS;sBAAAsF,QAAA,eACRrE,OAAA,CAAC1B,UAAU;wBAAC2G,OAAO,EAAC,WAAW;wBAACC,UAAU,EAAE,GAAI;wBAAAb,QAAA,EAC7CkC,OAAO,CAACnD;sBAAI;wBAAAsB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACZ7E,OAAA,CAACjB,SAAS;sBAACuH,KAAK,EAAC,OAAO;sBAAAjC,QAAA,eACtBrE,OAAA,CAAC1B,UAAU;wBAAC2G,OAAO,EAAC,OAAO;wBAAAZ,QAAA,EACxBkC,OAAO,CAAC1D;sBAAM;wBAAA6B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACZ7E,OAAA,CAACjB,SAAS;sBAACuH,KAAK,EAAC,OAAO;sBAAAjC,QAAA,eACtBrE,OAAA,CAAC1B,UAAU;wBAAC2G,OAAO,EAAC,WAAW;wBAACC,UAAU,EAAE,GAAI;wBAAAb,QAAA,GAAC,GAC9C,EAACkC,OAAO,CAAC3D,OAAO;sBAAA;wBAAA8B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACP;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACZ7E,OAAA,CAACjB,SAAS;sBAACuH,KAAK,EAAC,OAAO;sBAAAjC,QAAA,eACtBrE,OAAA,CAACb,IAAI;wBACH8C,KAAK,EAAE,GAAGsE,OAAO,CAAClD,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAGkD,OAAO,CAAClD,MAAM,GAAI;wBAC5DY,KAAK,EAAEsC,OAAO,CAAClD,MAAM,GAAG,CAAC,GAAG,SAAS,GAAGkD,OAAO,CAAClD,MAAM,GAAG,CAAC,GAAG,OAAO,GAAG,SAAU;wBACjFoC,IAAI,EAAC,OAAO;wBACZzB,IAAI,EAAEuC,OAAO,CAAClD,MAAM,GAAG,CAAC,gBAAGrD,OAAA,CAACV,UAAU;0BAAAoF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,GAAG0B,OAAO,CAAClD,MAAM,GAAG,CAAC,gBAAGrD,OAAA,CAACT,YAAY;0BAAAmF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,GAAG;sBAAK;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1F;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACO,CAAC;kBAAA,GAvBC0B,OAAO,CAACnD,IAAI;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAwBjB,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP7E,OAAA,CAACvB,IAAI;QAACoH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA3B,QAAA,eACvBrE,OAAA,CAACzB,IAAI;UAAC4F,EAAE,EAAE;YAAEC,MAAM,EAAE;UAAO,CAAE;UAAAC,QAAA,eAC3BrE,OAAA,CAACxB,WAAW;YAAA6F,QAAA,gBACVrE,OAAA,CAAC1B,UAAU;cAAC2G,OAAO,EAAC,IAAI;cAACE,YAAY;cAAAd,QAAA,EAAC;YAEtC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb7E,OAAA,CAAC3B,GAAG;cAAC8F,EAAE,EAAE;gBAAEG,OAAO,EAAE,MAAM;gBAAEmC,aAAa,EAAE,QAAQ;gBAAE3B,GAAG,EAAE;cAAE,CAAE;cAAAT,QAAA,gBAC5DrE,OAAA,CAAC3B,GAAG;gBAAAgG,QAAA,gBACFrE,OAAA,CAAC1B,UAAU;kBAAC2G,OAAO,EAAC,OAAO;kBAAChB,KAAK,EAAC,gBAAgB;kBAACkB,YAAY;kBAAAd,QAAA,EAAC;gBAEhE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb7E,OAAA,CAAC1B,UAAU;kBAAC2G,OAAO,EAAC,IAAI;kBAACC,UAAU,EAAE,GAAI;kBAACjB,KAAK,EAAC,SAAS;kBAAAI,QAAA,EACtD,CAAA3C,aAAa,aAAbA,aAAa,wBAAAX,sBAAA,GAAbW,aAAa,CAAEoB,eAAe,cAAA/B,sBAAA,uBAA9BA,sBAAA,CAAgCiC,YAAY,KAAI;gBAAC;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN7E,OAAA,CAAC3B,GAAG;gBAAAgG,QAAA,gBACFrE,OAAA,CAAC1B,UAAU;kBAAC2G,OAAO,EAAC,OAAO;kBAAChB,KAAK,EAAC,gBAAgB;kBAACkB,YAAY;kBAAAd,QAAA,EAAC;gBAEhE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb7E,OAAA,CAAC1B,UAAU;kBAAC2G,OAAO,EAAC,IAAI;kBAACC,UAAU,EAAE,GAAI;kBAACjB,KAAK,EAAC,cAAc;kBAAAI,QAAA,EAC3D,CAAA3C,aAAa,aAAbA,aAAa,wBAAAV,sBAAA,GAAbU,aAAa,CAAEoB,eAAe,cAAA9B,sBAAA,uBAA9BA,sBAAA,CAAgCiC,kBAAkB,KAAI;gBAAC;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN7E,OAAA,CAAC3B,GAAG;gBAAAgG,QAAA,gBACFrE,OAAA,CAAC1B,UAAU;kBAAC2G,OAAO,EAAC,OAAO;kBAAChB,KAAK,EAAC,gBAAgB;kBAACkB,YAAY;kBAAAd,QAAA,EAAC;gBAEhE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb7E,OAAA,CAAC1B,UAAU;kBAAC2G,OAAO,EAAC,IAAI;kBAACC,UAAU,EAAE,GAAI;kBAACjB,KAAK,EAAC,WAAW;kBAAAI,QAAA,GACxD,CAAA3C,aAAa,aAAbA,aAAa,wBAAAT,sBAAA,GAAbS,aAAa,CAAEoB,eAAe,cAAA7B,sBAAA,uBAA9BA,sBAAA,CAAgCiC,qBAAqB,KAAI,CAAC,EAAC,GAC9D;gBAAA;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP7E,OAAA,CAACvB,IAAI;MAACkH,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAvB,QAAA,gBACzBrE,OAAA,CAACvB,IAAI;QAACoH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA3B,QAAA,eACvBrE,OAAA,CAACzB,IAAI;UAAA8F,QAAA,eACHrE,OAAA,CAACxB,WAAW;YAAA6F,QAAA,gBACVrE,OAAA,CAAC1B,UAAU;cAAC2G,OAAO,EAAC,IAAI;cAACE,YAAY;cAAAd,QAAA,EAAC;YAEtC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb7E,OAAA,CAAC3B,GAAG;cAAC8F,EAAE,EAAE;gBAAEG,OAAO,EAAE,MAAM;gBAAEmC,aAAa,EAAE,QAAQ;gBAAE3B,GAAG,EAAE;cAAE,CAAE;cAAAT,QAAA,EAC3D3C,aAAa,aAAbA,aAAa,wBAAAR,qBAAA,GAAbQ,aAAa,CAAE4B,aAAa,cAAApC,qBAAA,wBAAAC,sBAAA,GAA5BD,qBAAA,CAA8BqC,SAAS,cAAApC,sBAAA,uBAAvCA,sBAAA,CAAyCuE,GAAG,CAAC,CAAClC,IAAI,EAAEkD,KAAK,kBACxD1G,OAAA,CAAC3B,GAAG;gBAAiB8F,EAAE,EAAE;kBAAEG,OAAO,EAAE,MAAM;kBAAEE,cAAc,EAAE,eAAe;kBAAED,UAAU,EAAE;gBAAS,CAAE;gBAAAF,QAAA,gBAClGrE,OAAA,CAAC1B,UAAU;kBAAC2G,OAAO,EAAC,OAAO;kBAAAZ,QAAA,EACxBb,IAAI,CAACA;gBAAI;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACb7E,OAAA,CAAC3B,GAAG;kBAAC8F,EAAE,EAAE;oBAAEG,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAEO,GAAG,EAAE;kBAAE,CAAE;kBAAAT,QAAA,gBACzDrE,OAAA,CAACZ,cAAc;oBACb6F,OAAO,EAAC,aAAa;oBACrBjD,KAAK,EAAGwB,IAAI,CAACX,MAAM,GAAG,EAAE,GAAI,GAAI;oBAChCsB,EAAE,EAAE;sBAAEwC,KAAK,EAAE,GAAG;sBAAEvC,MAAM,EAAE,CAAC;sBAAEwC,YAAY,EAAE;oBAAE;kBAAE;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC,eACF7E,OAAA,CAAC1B,UAAU;oBAAC2G,OAAO,EAAC,WAAW;oBAACC,UAAU,EAAE,GAAI;oBAAAb,QAAA,EAC7Cb,IAAI,CAACX;kBAAM;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA,GAbErB,IAAI,CAACA,IAAI;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAcd,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP7E,OAAA,CAACvB,IAAI;QAACoH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA3B,QAAA,eACvBrE,OAAA,CAACzB,IAAI;UAAA8F,QAAA,eACHrE,OAAA,CAACxB,WAAW;YAAA6F,QAAA,gBACVrE,OAAA,CAAC1B,UAAU;cAAC2G,OAAO,EAAC,IAAI;cAACE,YAAY;cAAAd,QAAA,EAAC;YAEtC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb7E,OAAA,CAAC3B,GAAG;cAAC8F,EAAE,EAAE;gBAAEG,OAAO,EAAE,MAAM;gBAAEmC,aAAa,EAAE,QAAQ;gBAAE3B,GAAG,EAAE;cAAE,CAAE;cAAAT,QAAA,EAC3D3C,aAAa,aAAbA,aAAa,wBAAAN,sBAAA,GAAbM,aAAa,CAAE4B,aAAa,cAAAlC,sBAAA,wBAAAC,sBAAA,GAA5BD,sBAAA,CAA8BqC,QAAQ,cAAApC,sBAAA,uBAAtCA,sBAAA,CAAwCqE,GAAG,CAAC,CAAChC,GAAG,EAAEgD,KAAK,kBACtD1G,OAAA,CAAC3B,GAAG;gBAAe8F,EAAE,EAAE;kBAAEG,OAAO,EAAE,MAAM;kBAAEE,cAAc,EAAE,eAAe;kBAAED,UAAU,EAAE;gBAAS,CAAE;gBAAAF,QAAA,gBAChGrE,OAAA,CAAC1B,UAAU;kBAAC2G,OAAO,EAAC,OAAO;kBAAAZ,QAAA,EACxBX,GAAG,CAACA;gBAAG;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACb7E,OAAA,CAAC3B,GAAG;kBAAC8F,EAAE,EAAE;oBAAEG,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAEO,GAAG,EAAE;kBAAE,CAAE;kBAAAT,QAAA,gBACzDrE,OAAA,CAACZ,cAAc;oBACb6F,OAAO,EAAC,aAAa;oBACrBjD,KAAK,EAAG0B,GAAG,CAACb,MAAM,GAAG,GAAG,GAAI,GAAI;oBAChCsB,EAAE,EAAE;sBAAEwC,KAAK,EAAE,GAAG;sBAAEvC,MAAM,EAAE,CAAC;sBAAEwC,YAAY,EAAE;oBAAE,CAAE;oBAC/C3C,KAAK,EAAEP,GAAG,CAACb,MAAM,GAAG,GAAG,GAAG,SAAS,GAAGa,GAAG,CAACb,MAAM,GAAG,GAAG,GAAG,SAAS,GAAG;kBAAQ;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9E,CAAC,eACF7E,OAAA,CAAC1B,UAAU;oBAAC2G,OAAO,EAAC,WAAW;oBAACC,UAAU,EAAE,GAAI;oBAAAb,QAAA,EAC7CX,GAAG,CAACb;kBAAM;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA,GAdEnB,GAAG,CAACA,GAAG;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAeZ,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACzE,EAAA,CA9VID,cAAc;AAAA0G,EAAA,GAAd1G,cAAc;AAgWpB,eAAeA,cAAc;AAAC,IAAA0G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}