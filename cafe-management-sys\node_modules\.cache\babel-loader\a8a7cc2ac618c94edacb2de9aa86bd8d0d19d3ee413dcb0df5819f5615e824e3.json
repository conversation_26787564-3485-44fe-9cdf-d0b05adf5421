{"ast": null, "code": "var _jsxFileName = \"D:\\\\Github_Repos\\\\cafe-mangement-system-react\\\\cafe-management-sys\\\\src\\\\pages\\\\customer\\\\CartPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useContext, useState } from 'react';\nimport { Container, Typography, Box } from '@mui/material';\nimport CartContext from '../../components/CartContext';\nimport CheckoutStepper from '../../components/CartComponents/CheckoutStepper';\nimport CartItems from '../../components/CartComponents/CartItems';\nimport ShippingForm from '../../components/CartComponents/ShippingForm';\nimport PaymentForm from '../../components/CartComponents/PaymentForm';\nimport OrderReview from '../../components/CartComponents/OrderReview';\nimport OrderConfirmationDialog from '../../components/CartComponents/OrderConfirmationDialog';\nimport EmptyCart from '../../components/CartComponents/EmptyCart';\nimport { useCheckoutFlow } from '../../hooks/useCheckoutFlow';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CartPage = () => {\n  _s();\n  const {\n    cartItems,\n    updateCartItem,\n    removeCartItem,\n    clearCart,\n    total,\n    totalPrepTime,\n    createOrder,\n    orderConfirmation,\n    orderError,\n    isLoading,\n    cartCount\n  } = useContext(CartContext);\n  const {\n    activeStep,\n    shippingInfo,\n    paymentMethod,\n    openDialog,\n    handleNext,\n    handleBack,\n    handleShippingChange,\n    setPaymentMethod,\n    handleCheckout,\n    setOpenDialog\n  } = useCheckoutFlow(createOrder);\n  const steps = ['Cart', 'Shipping', 'Payment', 'Review'];\n  const handleQuantityChange = (cartItemId, newQuantity) => {\n    if (newQuantity < 1) return;\n    updateCartItem(cartItemId, {\n      quantity: newQuantity\n    });\n  };\n  const handleUpdateCustomization = (cartItemId, customizationUpdates) => {\n    updateCartItem(cartItemId, customizationUpdates);\n  };\n  if (cartItems.length === 0 && activeStep === 0) {\n    return /*#__PURE__*/_jsxDEV(EmptyCart, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 12\n    }, this);\n  }\n  const renderStepContent = () => {\n    switch (activeStep) {\n      case 0:\n        return /*#__PURE__*/_jsxDEV(CartItems, {\n          cartItems: cartItems,\n          onQuantityChange: handleQuantityChange,\n          onRemoveItem: removeCartItem,\n          onClearCart: clearCart,\n          onUpdateCustomization: handleUpdateCustomization,\n          total: total,\n          cartCount: cartCount,\n          totalPrepTime: totalPrepTime,\n          onNext: handleNext,\n          isLoading: isLoading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this);\n      case 1:\n        return /*#__PURE__*/_jsxDEV(ShippingForm, {\n          shippingInfo: shippingInfo,\n          onShippingChange: handleShippingChange,\n          total: total,\n          cartCount: cartCount,\n          totalPrepTime: totalPrepTime,\n          onBack: handleBack,\n          onNext: handleNext,\n          isLoading: isLoading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this);\n      case 2:\n        return /*#__PURE__*/_jsxDEV(PaymentForm, {\n          paymentMethod: paymentMethod,\n          onPaymentMethodChange: setPaymentMethod,\n          total: total,\n          cartCount: cartCount,\n          totalPrepTime: totalPrepTime,\n          onBack: handleBack,\n          onNext: handleCheckout,\n          isLoading: isLoading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this);\n      case 3:\n        return /*#__PURE__*/_jsxDEV(OrderReview, {\n          cartItems: cartItems,\n          shippingInfo: shippingInfo,\n          paymentMethod: paymentMethod,\n          total: total,\n          cartCount: cartCount,\n          totalPrepTime: totalPrepTime,\n          onBack: handleBack,\n          onNext: handleCheckout,\n          isLoading: isLoading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    sx: {\n      py: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      mb: 4,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h3\",\n        gutterBottom: true,\n        sx: {\n          textAlign: 'center',\n          color: 'text.primary',\n          fontWeight: 700,\n          mb: 1\n        },\n        children: \"\\u2615 Your Coffee Cart\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        sx: {\n          textAlign: 'center',\n          color: 'text.secondary',\n          mb: 4\n        },\n        children: \"Brewing the perfect order for you\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CheckoutStepper, {\n        activeStep: activeStep,\n        steps: steps\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this), renderStepContent(), /*#__PURE__*/_jsxDEV(OrderConfirmationDialog, {\n      open: openDialog,\n      onClose: () => setOpenDialog(false),\n      orderConfirmation: orderConfirmation,\n      orderError: orderError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 120,\n    columnNumber: 5\n  }, this);\n};\n_s(CartPage, \"eQ/kUF4apD4114plPSWkKzHDyss=\", false, function () {\n  return [useCheckoutFlow];\n});\n_c = CartPage;\nexport default CartPage;\nvar _c;\n$RefreshReg$(_c, \"CartPage\");", "map": {"version": 3, "names": ["React", "useContext", "useState", "Container", "Typography", "Box", "CartContext", "CheckoutStepper", "CartItems", "ShippingForm", "PaymentForm", "OrderReview", "OrderConfirmationDialog", "EmptyCart", "useCheckoutFlow", "jsxDEV", "_jsxDEV", "CartPage", "_s", "cartItems", "updateCartItem", "removeCartItem", "clearCart", "total", "totalPrepTime", "createOrder", "orderConfirmation", "orderError", "isLoading", "cartCount", "activeStep", "shippingInfo", "paymentMethod", "openDialog", "handleNext", "handleBack", "handleShippingChange", "setPaymentMethod", "handleCheckout", "setOpenDialog", "steps", "handleQuantityChange", "cartItemId", "newQuantity", "quantity", "handleUpdateCustomization", "customizationUpdates", "length", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "renderStepContent", "onQuantityChange", "onRemoveItem", "onClearCart", "onUpdateCustomization", "onNext", "onShippingChange", "onBack", "onPaymentMethodChange", "max<PERSON><PERSON><PERSON>", "sx", "py", "children", "mb", "variant", "gutterBottom", "textAlign", "color", "fontWeight", "open", "onClose", "_c", "$RefreshReg$"], "sources": ["D:/Github_Repos/cafe-mangement-system-react/cafe-management-sys/src/pages/customer/CartPage.jsx"], "sourcesContent": ["import React, { useContext, useState } from 'react';\r\nimport { Container, Typography, Box } from '@mui/material';\r\nimport CartContext from '../../components/CartContext';\r\nimport CheckoutStepper from '../../components/CartComponents/CheckoutStepper';\r\nimport CartItems from '../../components/CartComponents/CartItems';\r\nimport ShippingForm from '../../components/CartComponents/ShippingForm';\r\nimport PaymentForm from '../../components/CartComponents/PaymentForm';\r\nimport OrderReview from '../../components/CartComponents/OrderReview';\r\nimport OrderConfirmationDialog from '../../components/CartComponents/OrderConfirmationDialog';\r\nimport EmptyCart from '../../components/CartComponents/EmptyCart';\r\nimport { useCheckoutFlow } from '../../hooks/useCheckoutFlow';\r\n\r\n\r\nconst CartPage = () => {\r\n  const {\r\n    cartItems,\r\n    updateCartItem,\r\n    removeCartItem,\r\n    clearCart,\r\n    total,\r\n    totalPrepTime,\r\n    createOrder,\r\n    orderConfirmation,\r\n    orderError,\r\n    isLoading,\r\n    cartCount\r\n  } = useContext(CartContext);\r\n\r\n  const {\r\n    activeStep,\r\n    shippingInfo,\r\n    paymentMethod,\r\n    openDialog,\r\n    handleNext,\r\n    handleBack,\r\n    handleShippingChange,\r\n    setPaymentMethod,\r\n    handleCheckout,\r\n    setOpenDialog\r\n  } = useCheckoutFlow(createOrder);\r\n\r\n  const steps = ['Cart', 'Shipping', 'Payment', 'Review'];\r\n\r\n  const handleQuantityChange = (cartItemId, newQuantity) => {\r\n    if (newQuantity < 1) return;\r\n    updateCartItem(cartItemId, { quantity: newQuantity });\r\n  };\r\n\r\n  const handleUpdateCustomization = (cartItemId, customizationUpdates) => {\r\n    updateCartItem(cartItemId, customizationUpdates);\r\n  };\r\n\r\n  if (cartItems.length === 0 && activeStep === 0) {\r\n    return <EmptyCart />;\r\n  }\r\n\r\n  const renderStepContent = () => {\r\n    switch (activeStep) {\r\n      case 0:\r\n        return (\r\n          <CartItems\r\n            cartItems={cartItems}\r\n            onQuantityChange={handleQuantityChange}\r\n            onRemoveItem={removeCartItem}\r\n            onClearCart={clearCart}\r\n            onUpdateCustomization={handleUpdateCustomization}\r\n            total={total}\r\n            cartCount={cartCount}\r\n            totalPrepTime={totalPrepTime}\r\n            onNext={handleNext}\r\n            isLoading={isLoading}\r\n          />\r\n        );\r\n      case 1:\r\n        return (\r\n          <ShippingForm\r\n            shippingInfo={shippingInfo}\r\n            onShippingChange={handleShippingChange}\r\n            total={total}\r\n            cartCount={cartCount}\r\n            totalPrepTime={totalPrepTime}\r\n            onBack={handleBack}\r\n            onNext={handleNext}\r\n            isLoading={isLoading}\r\n          />\r\n        );\r\n      case 2:\r\n        return (\r\n          <PaymentForm\r\n            paymentMethod={paymentMethod}\r\n            onPaymentMethodChange={setPaymentMethod}\r\n            total={total}\r\n            cartCount={cartCount}\r\n            totalPrepTime={totalPrepTime}\r\n            onBack={handleBack}\r\n            onNext={handleCheckout}\r\n            isLoading={isLoading}\r\n          />\r\n        );\r\n      case 3:\r\n        return (\r\n          <OrderReview\r\n            cartItems={cartItems}\r\n            shippingInfo={shippingInfo}\r\n            paymentMethod={paymentMethod}\r\n            total={total}\r\n            cartCount={cartCount}\r\n            totalPrepTime={totalPrepTime}\r\n            onBack={handleBack}\r\n            onNext={handleCheckout}\r\n            isLoading={isLoading}\r\n          />\r\n        );\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Container maxWidth=\"lg\" sx={{ py: 4 }}>\r\n      <Box mb={4}>\r\n        <Typography \r\n          variant=\"h3\" \r\n          gutterBottom \r\n          sx={{ \r\n            textAlign: 'center',\r\n            color: 'text.primary',\r\n            fontWeight: 700,\r\n            mb: 1\r\n          }}\r\n        >\r\n          ☕ Your Coffee Cart\r\n        </Typography>\r\n        <Typography \r\n          variant=\"subtitle1\" \r\n          sx={{ \r\n            textAlign: 'center',\r\n            color: 'text.secondary',\r\n            mb: 4\r\n          }}\r\n        >\r\n          Brewing the perfect order for you\r\n        </Typography>\r\n        \r\n        <CheckoutStepper activeStep={activeStep} steps={steps} />\r\n      </Box>\r\n\r\n      {renderStepContent()}\r\n\r\n      <OrderConfirmationDialog\r\n        open={openDialog}\r\n        onClose={() => setOpenDialog(false)}\r\n        orderConfirmation={orderConfirmation}\r\n        orderError={orderError}\r\n      />\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default CartPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SAASC,SAAS,EAAEC,UAAU,EAAEC,GAAG,QAAQ,eAAe;AAC1D,OAAOC,WAAW,MAAM,8BAA8B;AACtD,OAAOC,eAAe,MAAM,iDAAiD;AAC7E,OAAOC,SAAS,MAAM,2CAA2C;AACjE,OAAOC,YAAY,MAAM,8CAA8C;AACvE,OAAOC,WAAW,MAAM,6CAA6C;AACrE,OAAOC,WAAW,MAAM,6CAA6C;AACrE,OAAOC,uBAAuB,MAAM,yDAAyD;AAC7F,OAAOC,SAAS,MAAM,2CAA2C;AACjE,SAASC,eAAe,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG9D,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IACJC,SAAS;IACTC,cAAc;IACdC,cAAc;IACdC,SAAS;IACTC,KAAK;IACLC,aAAa;IACbC,WAAW;IACXC,iBAAiB;IACjBC,UAAU;IACVC,SAAS;IACTC;EACF,CAAC,GAAG5B,UAAU,CAACK,WAAW,CAAC;EAE3B,MAAM;IACJwB,UAAU;IACVC,YAAY;IACZC,aAAa;IACbC,UAAU;IACVC,UAAU;IACVC,UAAU;IACVC,oBAAoB;IACpBC,gBAAgB;IAChBC,cAAc;IACdC;EACF,CAAC,GAAGzB,eAAe,CAACW,WAAW,CAAC;EAEhC,MAAMe,KAAK,GAAG,CAAC,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,CAAC;EAEvD,MAAMC,oBAAoB,GAAGA,CAACC,UAAU,EAAEC,WAAW,KAAK;IACxD,IAAIA,WAAW,GAAG,CAAC,EAAE;IACrBvB,cAAc,CAACsB,UAAU,EAAE;MAAEE,QAAQ,EAAED;IAAY,CAAC,CAAC;EACvD,CAAC;EAED,MAAME,yBAAyB,GAAGA,CAACH,UAAU,EAAEI,oBAAoB,KAAK;IACtE1B,cAAc,CAACsB,UAAU,EAAEI,oBAAoB,CAAC;EAClD,CAAC;EAED,IAAI3B,SAAS,CAAC4B,MAAM,KAAK,CAAC,IAAIjB,UAAU,KAAK,CAAC,EAAE;IAC9C,oBAAOd,OAAA,CAACH,SAAS;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACtB;EAEA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQtB,UAAU;MAChB,KAAK,CAAC;QACJ,oBACEd,OAAA,CAACR,SAAS;UACRW,SAAS,EAAEA,SAAU;UACrBkC,gBAAgB,EAAEZ,oBAAqB;UACvCa,YAAY,EAAEjC,cAAe;UAC7BkC,WAAW,EAAEjC,SAAU;UACvBkC,qBAAqB,EAAEX,yBAA0B;UACjDtB,KAAK,EAAEA,KAAM;UACbM,SAAS,EAAEA,SAAU;UACrBL,aAAa,EAAEA,aAAc;UAC7BiC,MAAM,EAAEvB,UAAW;UACnBN,SAAS,EAAEA;QAAU;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAEN,KAAK,CAAC;QACJ,oBACEnC,OAAA,CAACP,YAAY;UACXsB,YAAY,EAAEA,YAAa;UAC3B2B,gBAAgB,EAAEtB,oBAAqB;UACvCb,KAAK,EAAEA,KAAM;UACbM,SAAS,EAAEA,SAAU;UACrBL,aAAa,EAAEA,aAAc;UAC7BmC,MAAM,EAAExB,UAAW;UACnBsB,MAAM,EAAEvB,UAAW;UACnBN,SAAS,EAAEA;QAAU;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAEN,KAAK,CAAC;QACJ,oBACEnC,OAAA,CAACN,WAAW;UACVsB,aAAa,EAAEA,aAAc;UAC7B4B,qBAAqB,EAAEvB,gBAAiB;UACxCd,KAAK,EAAEA,KAAM;UACbM,SAAS,EAAEA,SAAU;UACrBL,aAAa,EAAEA,aAAc;UAC7BmC,MAAM,EAAExB,UAAW;UACnBsB,MAAM,EAAEnB,cAAe;UACvBV,SAAS,EAAEA;QAAU;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAEN,KAAK,CAAC;QACJ,oBACEnC,OAAA,CAACL,WAAW;UACVQ,SAAS,EAAEA,SAAU;UACrBY,YAAY,EAAEA,YAAa;UAC3BC,aAAa,EAAEA,aAAc;UAC7BT,KAAK,EAAEA,KAAM;UACbM,SAAS,EAAEA,SAAU;UACrBL,aAAa,EAAEA,aAAc;UAC7BmC,MAAM,EAAExB,UAAW;UACnBsB,MAAM,EAAEnB,cAAe;UACvBV,SAAS,EAAEA;QAAU;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAEN;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACEnC,OAAA,CAACb,SAAS;IAAC0D,QAAQ,EAAC,IAAI;IAACC,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACrChD,OAAA,CAACX,GAAG;MAAC4D,EAAE,EAAE,CAAE;MAAAD,QAAA,gBACThD,OAAA,CAACZ,UAAU;QACT8D,OAAO,EAAC,IAAI;QACZC,YAAY;QACZL,EAAE,EAAE;UACFM,SAAS,EAAE,QAAQ;UACnBC,KAAK,EAAE,cAAc;UACrBC,UAAU,EAAE,GAAG;UACfL,EAAE,EAAE;QACN,CAAE;QAAAD,QAAA,EACH;MAED;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbnC,OAAA,CAACZ,UAAU;QACT8D,OAAO,EAAC,WAAW;QACnBJ,EAAE,EAAE;UACFM,SAAS,EAAE,QAAQ;UACnBC,KAAK,EAAE,gBAAgB;UACvBJ,EAAE,EAAE;QACN,CAAE;QAAAD,QAAA,EACH;MAED;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbnC,OAAA,CAACT,eAAe;QAACuB,UAAU,EAAEA,UAAW;QAACU,KAAK,EAAEA;MAAM;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CAAC,EAELC,iBAAiB,CAAC,CAAC,eAEpBpC,OAAA,CAACJ,uBAAuB;MACtB2D,IAAI,EAAEtC,UAAW;MACjBuC,OAAO,EAAEA,CAAA,KAAMjC,aAAa,CAAC,KAAK,CAAE;MACpCb,iBAAiB,EAAEA,iBAAkB;MACrCC,UAAU,EAAEA;IAAW;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEhB,CAAC;AAACjC,EAAA,CAhJID,QAAQ;EAAA,QA0BRH,eAAe;AAAA;AAAA2D,EAAA,GA1BfxD,QAAQ;AAkJd,eAAeA,QAAQ;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}