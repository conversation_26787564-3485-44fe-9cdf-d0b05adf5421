{"ast": null, "code": "var _jsxFileName = \"D:\\\\Github_Repos\\\\cafe-mangement-system-react\\\\cafe-management-sys\\\\src\\\\pages\\\\admin\\\\AdminDashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Grid, Card, CardContent, Typography, Button, Chip, Paper, Avatar, IconButton, LinearProgress, Fade, Grow, useTheme, alpha } from '@mui/material';\nimport { TrendingUp, TrendingDown, ShoppingCart, People, AttachMoney, Inventory, Add, Coffee, PersonAdd, BarChart, Schedule, Warning, CheckCircle, Info, MoreVert } from '@mui/icons-material';\nimport { styled } from '@mui/material/styles';\nimport AdminLayout from './AdminLayout';\nimport { analyticsAPI, ordersAPI } from '../../services/api';\n\n// Styled components for enhanced UI\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StatsCard = styled(Card)(({\n  theme,\n  color = 'primary'\n}) => ({\n  position: 'relative',\n  overflow: 'visible',\n  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n  border: `1px solid ${alpha(theme.palette[color].main, 0.1)}`,\n  '&:hover': {\n    transform: 'translateY(-4px)',\n    boxShadow: `0 12px 24px ${alpha(theme.palette[color].main, 0.15)}`,\n    '& .stats-icon': {\n      transform: 'scale(1.1) rotate(5deg)'\n    }\n  },\n  '&::before': {\n    content: '\"\"',\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    right: 0,\n    height: 4,\n    background: `linear-gradient(90deg, ${theme.palette[color].main}, ${theme.palette[color].light})`,\n    borderRadius: '4px 4px 0 0'\n  }\n}));\n_c = StatsCard;\nconst IconContainer = styled(Box)(({\n  theme,\n  color = 'primary'\n}) => ({\n  width: 60,\n  height: 60,\n  borderRadius: 16,\n  display: 'flex',\n  alignItems: 'center',\n  justifyContent: 'center',\n  background: `linear-gradient(135deg, ${alpha(theme.palette[color].main, 0.1)}, ${alpha(theme.palette[color].main, 0.05)})`,\n  transition: 'all 0.3s ease'\n}));\n_c2 = IconContainer;\nconst QuickActionCard = styled(Card)(({\n  theme\n}) => ({\n  cursor: 'pointer',\n  transition: 'all 0.2s ease',\n  border: `1px solid ${theme.palette.divider}`,\n  '&:hover': {\n    transform: 'translateY(-2px)',\n    boxShadow: theme.shadows[8],\n    borderColor: theme.palette.primary.main\n  }\n}));\n_c3 = QuickActionCard;\nconst AlertCard = styled(Paper)(({\n  theme,\n  severity = 'info'\n}) => {\n  const colors = {\n    error: theme.palette.error,\n    warning: theme.palette.warning,\n    info: theme.palette.info,\n    success: theme.palette.success\n  };\n  return {\n    padding: theme.spacing(2),\n    border: `1px solid ${alpha(colors[severity].main, 0.2)}`,\n    backgroundColor: alpha(colors[severity].main, 0.05),\n    borderRadius: 12,\n    transition: 'all 0.2s ease',\n    '&:hover': {\n      backgroundColor: alpha(colors[severity].main, 0.1),\n      transform: 'translateX(4px)'\n    }\n  };\n});\n\n// Mock data\n_c4 = AlertCard;\nconst statsData = [{\n  title: 'Total Revenue',\n  value: '$12,426',\n  change: '+12.5%',\n  trend: 'up',\n  icon: AttachMoney,\n  color: 'success',\n  subtitle: 'vs last month'\n}, {\n  title: 'Orders Today',\n  value: '147',\n  change: '+8.2%',\n  trend: 'up',\n  icon: ShoppingCart,\n  color: 'primary',\n  subtitle: '12 pending'\n}, {\n  title: 'Menu Items',\n  value: '42',\n  change: '+3 new',\n  trend: 'up',\n  icon: Coffee,\n  color: 'info',\n  subtitle: 'active items'\n}, {\n  title: 'Inventory Items',\n  value: '156',\n  change: '3 low stock',\n  trend: 'warning',\n  icon: Inventory,\n  color: 'error',\n  subtitle: 'needs attention'\n}];\nconst quickActions = [{\n  title: 'Add Menu Item',\n  icon: Coffee,\n  color: 'primary'\n}, {\n  title: 'Process Orders',\n  icon: ShoppingCart,\n  color: 'secondary'\n}, {\n  title: 'View Reports',\n  icon: BarChart,\n  color: 'info'\n}, {\n  title: 'Manage Inventory',\n  icon: Inventory,\n  color: 'warning'\n}];\nconst alerts = [{\n  id: 1,\n  title: 'Low Stock Alert',\n  message: 'Coffee beans running low (5 lbs left)',\n  severity: 'error',\n  time: '5 min ago',\n  action: 'Reorder'\n}, {\n  id: 2,\n  title: 'New Orders',\n  message: '3 new orders waiting for confirmation',\n  severity: 'warning',\n  time: '10 min ago',\n  action: 'View Orders'\n}, {\n  id: 3,\n  title: 'Equipment Maintenance',\n  message: 'Espresso machine maintenance scheduled',\n  severity: 'info',\n  time: '1 hour ago',\n  action: 'Schedule'\n}];\nconst recentOrders = [{\n  id: '#1234',\n  customer: 'John Doe',\n  items: 'Latte, Croissant',\n  total: '$12.50',\n  status: 'completed',\n  time: '2 min ago'\n}, {\n  id: '#1235',\n  customer: 'Jane Smith',\n  items: 'Cappuccino, Muffin',\n  total: '$8.75',\n  status: 'preparing',\n  time: '5 min ago'\n}, {\n  id: '#1236',\n  customer: 'Bob Wilson',\n  items: 'Espresso, Sandwich',\n  total: '$15.25',\n  status: 'pending',\n  time: '8 min ago'\n}, {\n  id: '#1237',\n  customer: 'Alice Brown',\n  items: 'Americano, Cookie',\n  total: '$6.25',\n  status: 'completed',\n  time: '12 min ago'\n}, {\n  id: '#1238',\n  customer: 'David Lee',\n  items: 'Mocha, Bagel',\n  total: '$11.00',\n  status: 'preparing',\n  time: '15 min ago'\n}];\nexport default function AdminDashboard() {\n  _s();\n  var _dashboardData$totalR, _dashboardData$orders, _dashboardData$menuIt, _dashboardData$invent;\n  const theme = useTheme();\n  const [loading, setLoading] = useState(true);\n  const [dashboardData, setDashboardData] = useState(null);\n  const [recentOrders, setRecentOrders] = useState([]);\n  const [lastUpdated, setLastUpdated] = useState(new Date());\n  useEffect(() => {\n    fetchDashboardData();\n    // Set up auto-refresh every 30 seconds\n    const interval = setInterval(fetchDashboardData, 30000);\n    return () => clearInterval(interval);\n  }, []);\n  const fetchDashboardData = async () => {\n    try {\n      setLoading(true);\n      const [stats, orders] = await Promise.all([analyticsAPI.getDashboardStats(), ordersAPI.getRecent(5)]);\n      setDashboardData(stats);\n      setRecentOrders(orders);\n      setLastUpdated(new Date());\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'completed':\n        return 'success';\n      case 'preparing':\n        return 'warning';\n      case 'pending':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  const getSeverityIcon = severity => {\n    switch (severity) {\n      case 'error':\n        return /*#__PURE__*/_jsxDEV(Warning, {\n          color: \"error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 28\n        }, this);\n      case 'warning':\n        return /*#__PURE__*/_jsxDEV(Info, {\n          color: \"warning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 30\n        }, this);\n      case 'info':\n        return /*#__PURE__*/_jsxDEV(CheckCircle, {\n          color: \"info\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 27\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Info, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 23\n        }, this);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: '100%',\n        mt: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(LinearProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 9\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      flexGrow: 1\n    },\n    children: [/*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: (dashboardData ? [{\n        title: 'Total Revenue',\n        value: `$${((_dashboardData$totalR = dashboardData.totalRevenue) === null || _dashboardData$totalR === void 0 ? void 0 : _dashboardData$totalR.toLocaleString()) || '0'}`,\n        change: `${dashboardData.revenueChange > 0 ? '+' : ''}${dashboardData.revenueChange || 0}%`,\n        trend: dashboardData.revenueChange > 0 ? 'up' : 'down',\n        icon: AttachMoney,\n        color: 'success'\n      }, {\n        title: 'Orders Today',\n        value: ((_dashboardData$orders = dashboardData.ordersToday) === null || _dashboardData$orders === void 0 ? void 0 : _dashboardData$orders.toLocaleString()) || '0',\n        change: `${dashboardData.ordersChange > 0 ? '+' : ''}${dashboardData.ordersChange || 0}%`,\n        trend: dashboardData.ordersChange > 0 ? 'up' : 'down',\n        icon: ShoppingCart,\n        color: 'primary'\n      }, {\n        title: 'Menu Items',\n        value: ((_dashboardData$menuIt = dashboardData.menuItems) === null || _dashboardData$menuIt === void 0 ? void 0 : _dashboardData$menuIt.toLocaleString()) || '0',\n        change: '0%',\n        trend: 'neutral',\n        icon: Coffee,\n        color: 'info'\n      }, {\n        title: 'Inventory Items',\n        value: ((_dashboardData$invent = dashboardData.inventoryItems) === null || _dashboardData$invent === void 0 ? void 0 : _dashboardData$invent.toLocaleString()) || '0',\n        change: dashboardData.lowStockItems ? `-${dashboardData.lowStockItems}` : '0',\n        trend: dashboardData.lowStockItems > 0 ? 'down' : 'neutral',\n        icon: Inventory,\n        color: 'warning'\n      }] : statsData).map((stat, index) => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        lg: 3,\n        children: /*#__PURE__*/_jsxDEV(Grow, {\n          in: !loading,\n          timeout: 500 + index * 100,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(StatsCard, {\n              color: stat.color,\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 3\n                },\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'flex-start',\n                    justifyContent: 'space-between'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      gutterBottom: true,\n                      children: stat.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 298,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h4\",\n                      component: \"h2\",\n                      sx: {\n                        fontWeight: 700,\n                        mb: 1\n                      },\n                      children: stat.value\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 301,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Chip, {\n                        label: stat.change,\n                        size: \"small\",\n                        color: stat.trend === 'up' ? 'success' : stat.trend === 'down' ? 'error' : 'warning',\n                        icon: stat.trend === 'up' ? /*#__PURE__*/_jsxDEV(TrendingUp, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 309,\n                          columnNumber: 59\n                        }, this) : stat.trend === 'down' ? /*#__PURE__*/_jsxDEV(TrendingDown, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 309,\n                          columnNumber: 100\n                        }, this) : /*#__PURE__*/_jsxDEV(Warning, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 309,\n                          columnNumber: 119\n                        }, this),\n                        sx: {\n                          fontSize: '0.75rem'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 305,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: stat.subtitle\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 312,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 304,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 297,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(IconContainer, {\n                    color: stat.color,\n                    children: /*#__PURE__*/_jsxDEV(stat.icon, {\n                      className: \"stats-icon\",\n                      sx: {\n                        fontSize: 28,\n                        color: `${stat.color}.main`,\n                        transition: 'all 0.3s ease'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 318,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 15\n        }, this)\n      }, stat.title, false, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(Fade, {\n          in: !loading,\n          timeout: 800,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              height: '100%'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                p: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between',\n                  mb: 3\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  sx: {\n                    fontWeight: 600\n                  },\n                  children: \"Recent Orders\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  size: \"small\",\n                  color: \"primary\",\n                  children: \"View All\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: 2\n                },\n                children: (recentOrders.length > 0 ? recentOrders : [{\n                  _id: '1',\n                  orderNumber: '#1234',\n                  customer: {\n                    name: 'John Doe'\n                  },\n                  items: [{\n                    name: 'Latte'\n                  }, {\n                    name: 'Croissant'\n                  }],\n                  total: 12.50,\n                  status: 'completed',\n                  createdAt: new Date(Date.now() - 120000).toISOString()\n                }, {\n                  _id: '2',\n                  orderNumber: '#1235',\n                  customer: {\n                    name: 'Jane Smith'\n                  },\n                  items: [{\n                    name: 'Cappuccino'\n                  }, {\n                    name: 'Muffin'\n                  }],\n                  total: 8.75,\n                  status: 'preparing',\n                  createdAt: new Date(Date.now() - 300000).toISOString()\n                }]).map((order, index) => {\n                  var _order$customer, _order$items;\n                  return /*#__PURE__*/_jsxDEV(Grow, {\n                    in: !loading,\n                    timeout: 1000 + index * 100,\n                    children: /*#__PURE__*/_jsxDEV(Paper, {\n                      sx: {\n                        p: 2,\n                        border: '1px solid',\n                        borderColor: 'divider',\n                        borderRadius: 2,\n                        transition: 'all 0.2s ease',\n                        '&:hover': {\n                          borderColor: 'primary.main',\n                          transform: 'translateY(-1px)',\n                          boxShadow: 2\n                        }\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'space-between'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            flex: 1\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"subtitle2\",\n                            sx: {\n                              fontWeight: 600\n                            },\n                            children: [order.orderNumber || order.id, \" - \", ((_order$customer = order.customer) === null || _order$customer === void 0 ? void 0 : _order$customer.name) || order.customer]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 373,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"body2\",\n                            color: \"text.secondary\",\n                            children: ((_order$items = order.items) === null || _order$items === void 0 ? void 0 : _order$items.map(item => item.name).join(', ')) || order.items\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 376,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"caption\",\n                            color: \"text.secondary\",\n                            children: order.createdAt ? `${Math.floor((Date.now() - new Date(order.createdAt).getTime()) / 60000)} min ago` : order.time\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 379,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 372,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: 2\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"h6\",\n                            sx: {\n                              fontWeight: 600\n                            },\n                            children: typeof order.total === 'number' ? `$${order.total.toFixed(2)}` : order.total\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 386,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                            label: order.status,\n                            size: \"small\",\n                            color: getStatusColor(order.status),\n                            sx: {\n                              textTransform: 'capitalize'\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 389,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 385,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 371,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 357,\n                      columnNumber: 25\n                    }, this)\n                  }, order._id || order.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: 3,\n            height: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Fade, {\n            in: !loading,\n            timeout: 1000,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 3\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 2,\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Add, {\n                    sx: {\n                      mr: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 23\n                  }, this), \"Quick Actions\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 2,\n                  children: quickActions.map((action, index) => /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Grow, {\n                      in: !loading,\n                      timeout: 1200 + index * 100,\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: /*#__PURE__*/_jsxDEV(QuickActionCard, {\n                          sx: {\n                            textAlign: 'center',\n                            p: 2\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(IconContainer, {\n                            color: action.color,\n                            sx: {\n                              width: 48,\n                              height: 48,\n                              mx: 'auto',\n                              mb: 1\n                            },\n                            children: /*#__PURE__*/_jsxDEV(action.icon, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 424,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 423,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"body2\",\n                            sx: {\n                              fontWeight: 500\n                            },\n                            children: action.title\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 426,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 422,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 421,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 420,\n                      columnNumber: 27\n                    }, this)\n                  }, action.title, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 419,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 417,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Fade, {\n            in: !loading,\n            timeout: 1400,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 3\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 2,\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Warning, {\n                    sx: {\n                      mr: 1,\n                      color: 'warning.main'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 444,\n                    columnNumber: 23\n                  }, this), \"Alerts\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    flexDirection: 'column',\n                    gap: 2\n                  },\n                  children: alerts.map((alert, index) => /*#__PURE__*/_jsxDEV(Grow, {\n                    in: !loading,\n                    timeout: 1600 + index * 100,\n                    children: /*#__PURE__*/_jsxDEV(AlertCard, {\n                      severity: alert.severity,\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'flex',\n                          alignItems: 'flex-start',\n                          justifyContent: 'space-between'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            display: 'flex',\n                            alignItems: 'flex-start',\n                            gap: 1,\n                            flex: 1\n                          },\n                          children: [getSeverityIcon(alert.severity), /*#__PURE__*/_jsxDEV(Box, {\n                            children: [/*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"subtitle2\",\n                              sx: {\n                                fontWeight: 600\n                              },\n                              children: alert.title\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 455,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"body2\",\n                              color: \"text.secondary\",\n                              sx: {\n                                mb: 1\n                              },\n                              children: alert.message\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 458,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"caption\",\n                              color: \"text.secondary\",\n                              children: alert.time\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 461,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 454,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 452,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(Button, {\n                          size: \"small\",\n                          variant: \"outlined\",\n                          children: alert.action\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 466,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 451,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 450,\n                      columnNumber: 27\n                    }, this)\n                  }, alert.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 449,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 254,\n    columnNumber: 7\n  }, this);\n}\n_s(AdminDashboard, \"OGDjh7jkSjNlKTpfmpzmdMJcSl4=\", false, function () {\n  return [useTheme];\n});\n_c5 = AdminDashboard;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"StatsCard\");\n$RefreshReg$(_c2, \"IconContainer\");\n$RefreshReg$(_c3, \"QuickActionCard\");\n$RefreshReg$(_c4, \"AlertCard\");\n$RefreshReg$(_c5, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Chip", "Paper", "Avatar", "IconButton", "LinearProgress", "Fade", "Grow", "useTheme", "alpha", "TrendingUp", "TrendingDown", "ShoppingCart", "People", "AttachMoney", "Inventory", "Add", "Coffee", "PersonAdd", "<PERSON><PERSON><PERSON>", "Schedule", "Warning", "CheckCircle", "Info", "<PERSON><PERSON><PERSON>", "styled", "AdminLayout", "analyticsAPI", "ordersAPI", "jsxDEV", "_jsxDEV", "StatsCard", "theme", "color", "position", "overflow", "transition", "border", "palette", "main", "transform", "boxShadow", "content", "top", "left", "right", "height", "background", "light", "borderRadius", "_c", "IconContainer", "width", "display", "alignItems", "justifyContent", "_c2", "QuickActionCard", "cursor", "divider", "shadows", "borderColor", "primary", "_c3", "AlertCard", "severity", "colors", "error", "warning", "info", "success", "padding", "spacing", "backgroundColor", "_c4", "statsData", "title", "value", "change", "trend", "icon", "subtitle", "quickActions", "alerts", "id", "message", "time", "action", "recentOrders", "customer", "items", "total", "status", "AdminDashboard", "_s", "_dashboardData$totalR", "_dashboardData$orders", "_dashboardData$menuIt", "_dashboardData$invent", "loading", "setLoading", "dashboardData", "setDashboardData", "setRecentOrders", "lastUpdated", "setLastUpdated", "Date", "fetchDashboardData", "interval", "setInterval", "clearInterval", "stats", "orders", "Promise", "all", "getDashboardStats", "getRecent", "console", "getStatusColor", "getSeverityIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "mt", "children", "flexGrow", "container", "mb", "totalRevenue", "toLocaleString", "revenueChange", "ordersToday", "ordersChange", "menuItems", "inventoryItems", "lowStockItems", "map", "stat", "index", "item", "xs", "sm", "lg", "in", "timeout", "p", "flex", "variant", "gutterBottom", "component", "fontWeight", "gap", "label", "size", "fontSize", "className", "flexDirection", "length", "_id", "orderNumber", "name", "createdAt", "now", "toISOString", "order", "_order$customer", "_order$items", "join", "Math", "floor", "getTime", "toFixed", "textTransform", "mr", "textAlign", "mx", "alert", "_c5", "$RefreshReg$"], "sources": ["D:/Github_Repos/cafe-mangement-system-react/cafe-management-sys/src/pages/admin/AdminDashboard.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport {\r\n  Box,\r\n  Grid,\r\n  Card,\r\n  CardContent,\r\n  Typography,\r\n  Button,\r\n  Chip,\r\n  Paper,\r\n  Avatar,\r\n  IconButton,\r\n  LinearProgress,\r\n  Fade,\r\n  Grow,\r\n  useTheme,\r\n  alpha,\r\n} from '@mui/material';\r\nimport {\r\n  TrendingUp,\r\n  TrendingDown,\r\n  ShoppingCart,\r\n  People,\r\n  AttachMoney,\r\n  Inventory,\r\n  Add,\r\n  Coffee,\r\n  PersonAdd,\r\n  BarChart,\r\n  Schedule,\r\n  Warning,\r\n  CheckCircle,\r\n  Info,\r\n  MoreVert,\r\n} from '@mui/icons-material';\r\nimport { styled } from '@mui/material/styles';\r\nimport AdminLayout from './AdminLayout';\r\nimport { analyticsAPI, ordersAPI } from '../../services/api';\r\n\r\n// Styled components for enhanced UI\r\nconst StatsCard = styled(Card)(({ theme, color = 'primary' }) => ({\r\n  position: 'relative',\r\n  overflow: 'visible',\r\n  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\r\n  border: `1px solid ${alpha(theme.palette[color].main, 0.1)}`,\r\n  \r\n  '&:hover': {\r\n    transform: 'translateY(-4px)',\r\n    boxShadow: `0 12px 24px ${alpha(theme.palette[color].main, 0.15)}`,\r\n    \r\n    '& .stats-icon': {\r\n      transform: 'scale(1.1) rotate(5deg)',\r\n    },\r\n  },\r\n  \r\n  '&::before': {\r\n    content: '\"\"',\r\n    position: 'absolute',\r\n    top: 0,\r\n    left: 0,\r\n    right: 0,\r\n    height: 4,\r\n    background: `linear-gradient(90deg, ${theme.palette[color].main}, ${theme.palette[color].light})`,\r\n    borderRadius: '4px 4px 0 0',\r\n  },\r\n}));\r\n\r\nconst IconContainer = styled(Box)(({ theme, color = 'primary' }) => ({\r\n  width: 60,\r\n  height: 60,\r\n  borderRadius: 16,\r\n  display: 'flex',\r\n  alignItems: 'center',\r\n  justifyContent: 'center',\r\n  background: `linear-gradient(135deg, ${alpha(theme.palette[color].main, 0.1)}, ${alpha(theme.palette[color].main, 0.05)})`,\r\n  transition: 'all 0.3s ease',\r\n}));\r\n\r\nconst QuickActionCard = styled(Card)(({ theme }) => ({\r\n  cursor: 'pointer',\r\n  transition: 'all 0.2s ease',\r\n  border: `1px solid ${theme.palette.divider}`,\r\n  \r\n  '&:hover': {\r\n    transform: 'translateY(-2px)',\r\n    boxShadow: theme.shadows[8],\r\n    borderColor: theme.palette.primary.main,\r\n  },\r\n}));\r\n\r\nconst AlertCard = styled(Paper)(({ theme, severity = 'info' }) => {\r\n  const colors = {\r\n    error: theme.palette.error,\r\n    warning: theme.palette.warning,\r\n    info: theme.palette.info,\r\n    success: theme.palette.success,\r\n  };\r\n  \r\n  return {\r\n    padding: theme.spacing(2),\r\n    border: `1px solid ${alpha(colors[severity].main, 0.2)}`,\r\n    backgroundColor: alpha(colors[severity].main, 0.05),\r\n    borderRadius: 12,\r\n    transition: 'all 0.2s ease',\r\n    \r\n    '&:hover': {\r\n      backgroundColor: alpha(colors[severity].main, 0.1),\r\n      transform: 'translateX(4px)',\r\n    },\r\n  };\r\n});\r\n\r\n// Mock data\r\nconst statsData = [\r\n  {\r\n    title: 'Total Revenue',\r\n    value: '$12,426',\r\n    change: '+12.5%',\r\n    trend: 'up',\r\n    icon: AttachMoney,\r\n    color: 'success',\r\n    subtitle: 'vs last month',\r\n  },\r\n  {\r\n    title: 'Orders Today',\r\n    value: '147',\r\n    change: '+8.2%',\r\n    trend: 'up',\r\n    icon: ShoppingCart,\r\n    color: 'primary',\r\n    subtitle: '12 pending',\r\n  },\r\n  {\r\n    title: 'Menu Items',\r\n    value: '42',\r\n    change: '+3 new',\r\n    trend: 'up',\r\n    icon: Coffee,\r\n    color: 'info',\r\n    subtitle: 'active items',\r\n  },\r\n  {\r\n    title: 'Inventory Items',\r\n    value: '156',\r\n    change: '3 low stock',\r\n    trend: 'warning',\r\n    icon: Inventory,\r\n    color: 'error',\r\n    subtitle: 'needs attention',\r\n  },\r\n];\r\n\r\nconst quickActions = [\r\n  { title: 'Add Menu Item', icon: Coffee, color: 'primary' },\r\n  { title: 'Process Orders', icon: ShoppingCart, color: 'secondary' },\r\n  { title: 'View Reports', icon: BarChart, color: 'info' },\r\n  { title: 'Manage Inventory', icon: Inventory, color: 'warning' },\r\n];\r\n\r\nconst alerts = [\r\n  {\r\n    id: 1,\r\n    title: 'Low Stock Alert',\r\n    message: 'Coffee beans running low (5 lbs left)',\r\n    severity: 'error',\r\n    time: '5 min ago',\r\n    action: 'Reorder',\r\n  },\r\n  {\r\n    id: 2,\r\n    title: 'New Orders',\r\n    message: '3 new orders waiting for confirmation',\r\n    severity: 'warning',\r\n    time: '10 min ago',\r\n    action: 'View Orders',\r\n  },\r\n  {\r\n    id: 3,\r\n    title: 'Equipment Maintenance',\r\n    message: 'Espresso machine maintenance scheduled',\r\n    severity: 'info',\r\n    time: '1 hour ago',\r\n    action: 'Schedule',\r\n  },\r\n];\r\n\r\nconst recentOrders = [\r\n  { id: '#1234', customer: 'John Doe', items: 'Latte, Croissant', total: '$12.50', status: 'completed', time: '2 min ago' },\r\n  { id: '#1235', customer: 'Jane Smith', items: 'Cappuccino, Muffin', total: '$8.75', status: 'preparing', time: '5 min ago' },\r\n  { id: '#1236', customer: 'Bob Wilson', items: 'Espresso, Sandwich', total: '$15.25', status: 'pending', time: '8 min ago' },\r\n  { id: '#1237', customer: 'Alice Brown', items: 'Americano, Cookie', total: '$6.25', status: 'completed', time: '12 min ago' },\r\n  { id: '#1238', customer: 'David Lee', items: 'Mocha, Bagel', total: '$11.00', status: 'preparing', time: '15 min ago' },\r\n];\r\n\r\nexport default function AdminDashboard() {\r\n  const theme = useTheme();\r\n  const [loading, setLoading] = useState(true);\r\n  const [dashboardData, setDashboardData] = useState(null);\r\n  const [recentOrders, setRecentOrders] = useState([]);\r\n  const [lastUpdated, setLastUpdated] = useState(new Date());\r\n\r\n  useEffect(() => {\r\n    fetchDashboardData();\r\n    // Set up auto-refresh every 30 seconds\r\n    const interval = setInterval(fetchDashboardData, 30000);\r\n    return () => clearInterval(interval);\r\n  }, []);\r\n\r\n  const fetchDashboardData = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const [stats, orders] = await Promise.all([\r\n        analyticsAPI.getDashboardStats(),\r\n        ordersAPI.getRecent(5),\r\n      ]);\r\n\r\n      setDashboardData(stats);\r\n      setRecentOrders(orders);\r\n      setLastUpdated(new Date());\r\n    } catch (error) {\r\n      console.error('Error fetching dashboard data:', error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const getStatusColor = (status) => {\r\n    switch (status) {\r\n      case 'completed': return 'success';\r\n      case 'preparing': return 'warning';\r\n      case 'pending': return 'error';\r\n      default: return 'default';\r\n    }\r\n  };\r\n\r\n  const getSeverityIcon = (severity) => {\r\n    switch (severity) {\r\n      case 'error': return <Warning color=\"error\" />;\r\n      case 'warning': return <Info color=\"warning\" />;\r\n      case 'info': return <CheckCircle color=\"info\" />;\r\n      default: return <Info />;\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n        <Box sx={{ width: '100%', mt: 2 }}>\r\n          <LinearProgress />\r\n        </Box>\r\n    );\r\n  }\r\n\r\n  return (\r\n      <Box sx={{ flexGrow: 1 }}>\r\n        {/* Stats Cards */}\r\n        <Grid container spacing={3} sx={{ mb: 4 }}>\r\n          {(dashboardData ? [\r\n            {\r\n              title: 'Total Revenue',\r\n              value: `$${dashboardData.totalRevenue?.toLocaleString() || '0'}`,\r\n              change: `${dashboardData.revenueChange > 0 ? '+' : ''}${dashboardData.revenueChange || 0}%`,\r\n              trend: dashboardData.revenueChange > 0 ? 'up' : 'down',\r\n              icon: AttachMoney,\r\n              color: 'success'\r\n            },\r\n            {\r\n              title: 'Orders Today',\r\n              value: dashboardData.ordersToday?.toLocaleString() || '0',\r\n              change: `${dashboardData.ordersChange > 0 ? '+' : ''}${dashboardData.ordersChange || 0}%`,\r\n              trend: dashboardData.ordersChange > 0 ? 'up' : 'down',\r\n              icon: ShoppingCart,\r\n              color: 'primary'\r\n            },\r\n            {\r\n              title: 'Menu Items',\r\n              value: dashboardData.menuItems?.toLocaleString() || '0',\r\n              change: '0%',\r\n              trend: 'neutral',\r\n              icon: Coffee,\r\n              color: 'info'\r\n            },\r\n            {\r\n              title: 'Inventory Items',\r\n              value: dashboardData.inventoryItems?.toLocaleString() || '0',\r\n              change: dashboardData.lowStockItems ? `-${dashboardData.lowStockItems}` : '0',\r\n              trend: dashboardData.lowStockItems > 0 ? 'down' : 'neutral',\r\n              icon: Inventory,\r\n              color: 'warning'\r\n            }\r\n          ] : statsData).map((stat, index) => (\r\n            <Grid item xs={12} sm={6} lg={3} key={stat.title}>\r\n              <Grow in={!loading} timeout={500 + index * 100}>\r\n                <div>\r\n                  <StatsCard color={stat.color}>\r\n                    <CardContent sx={{ p: 3 }}>\r\n                      <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between' }}>\r\n                        <Box sx={{ flex: 1 }}>\r\n                          <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\r\n                            {stat.title}\r\n                          </Typography>\r\n                          <Typography variant=\"h4\" component=\"h2\" sx={{ fontWeight: 700, mb: 1 }}>\r\n                            {stat.value}\r\n                          </Typography>\r\n                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                            <Chip\r\n                              label={stat.change}\r\n                              size=\"small\"\r\n                              color={stat.trend === 'up' ? 'success' : stat.trend === 'down' ? 'error' : 'warning'}\r\n                              icon={stat.trend === 'up' ? <TrendingUp /> : stat.trend === 'down' ? <TrendingDown /> : <Warning />}\r\n                              sx={{ fontSize: '0.75rem' }}\r\n                            />\r\n                            <Typography variant=\"caption\" color=\"text.secondary\">\r\n                              {stat.subtitle}\r\n                            </Typography>\r\n                          </Box>\r\n                        </Box>\r\n                        <IconContainer color={stat.color}>\r\n                          <stat.icon \r\n                            className=\"stats-icon\"\r\n                            sx={{ \r\n                              fontSize: 28, \r\n                              color: `${stat.color}.main`,\r\n                              transition: 'all 0.3s ease',\r\n                            }} \r\n                          />\r\n                        </IconContainer>\r\n                      </Box>\r\n                    </CardContent>\r\n                  </StatsCard>\r\n                </div>\r\n              </Grow>\r\n            </Grid>\r\n          ))}\r\n        </Grid>\r\n\r\n        {/* Main Content Grid */}\r\n        <Grid container spacing={3}>\r\n          {/* Recent Orders */}\r\n          <Grid item xs={12} lg={8}>\r\n            <Fade in={!loading} timeout={800}>\r\n              <Card sx={{ height: '100%' }}>\r\n                <CardContent sx={{ p: 3 }}>\r\n                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>\r\n                    <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\r\n                      Recent Orders\r\n                    </Typography>\r\n                    <Button variant=\"outlined\" size=\"small\" color=\"primary\">\r\n                      View All\r\n                    </Button>\r\n                  </Box>\r\n                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>\r\n                    {(recentOrders.length > 0 ? recentOrders : [\r\n                      { _id: '1', orderNumber: '#1234', customer: { name: 'John Doe' }, items: [{ name: 'Latte' }, { name: 'Croissant' }], total: 12.50, status: 'completed', createdAt: new Date(Date.now() - 120000).toISOString() },\r\n                      { _id: '2', orderNumber: '#1235', customer: { name: 'Jane Smith' }, items: [{ name: 'Cappuccino' }, { name: 'Muffin' }], total: 8.75, status: 'preparing', createdAt: new Date(Date.now() - 300000).toISOString() },\r\n                    ]).map((order, index) => (\r\n                      <Grow in={!loading} timeout={1000 + index * 100} key={order._id || order.id}>\r\n                        <Paper\r\n                          sx={{\r\n                            p: 2,\r\n                            border: '1px solid',\r\n                            borderColor: 'divider',\r\n                            borderRadius: 2,\r\n                            transition: 'all 0.2s ease',\r\n                            '&:hover': {\r\n                              borderColor: 'primary.main',\r\n                              transform: 'translateY(-1px)',\r\n                              boxShadow: 2,\r\n                            }\r\n                          }}\r\n                        >\r\n                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\r\n                            <Box sx={{ flex: 1 }}>\r\n                              <Typography variant=\"subtitle2\" sx={{ fontWeight: 600 }}>\r\n                                {order.orderNumber || order.id} - {order.customer?.name || order.customer}\r\n                              </Typography>\r\n                              <Typography variant=\"body2\" color=\"text.secondary\">\r\n                                {order.items?.map(item => item.name).join(', ') || order.items}\r\n                              </Typography>\r\n                              <Typography variant=\"caption\" color=\"text.secondary\">\r\n                                {order.createdAt ?\r\n                                  `${Math.floor((Date.now() - new Date(order.createdAt).getTime()) / 60000)} min ago` :\r\n                                  order.time}\r\n                              </Typography>\r\n                            </Box>\r\n                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                              <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\r\n                                {typeof order.total === 'number' ? `$${order.total.toFixed(2)}` : order.total}\r\n                              </Typography>\r\n                              <Chip\r\n                                label={order.status}\r\n                                size=\"small\"\r\n                                color={getStatusColor(order.status)}\r\n                                sx={{ textTransform: 'capitalize' }}\r\n                              />\r\n                            </Box>\r\n                          </Box>\r\n                        </Paper>\r\n                      </Grow>\r\n                    ))}\r\n                  </Box>\r\n                </CardContent>\r\n              </Card>\r\n            </Fade>\r\n          </Grid>\r\n\r\n          {/* Quick Actions & Alerts */}\r\n          <Grid item xs={12} lg={4}>\r\n            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, height: '100%' }}>\r\n              {/* Quick Actions */}\r\n              <Fade in={!loading} timeout={1000}>\r\n                <Card>\r\n                  <CardContent sx={{ p: 3 }}>\r\n                    <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center' }}>\r\n                      <Add sx={{ mr: 1 }} />\r\n                      Quick Actions\r\n                    </Typography>\r\n                    <Grid container spacing={2}>\r\n                      {quickActions.map((action, index) => (\r\n                        <Grid item xs={6} key={action.title}>\r\n                          <Grow in={!loading} timeout={1200 + index * 100}>\r\n                            <div>\r\n                              <QuickActionCard sx={{ textAlign: 'center', p: 2 }}>\r\n                                <IconContainer color={action.color} sx={{ width: 48, height: 48, mx: 'auto', mb: 1 }}>\r\n                                  <action.icon />\r\n                                </IconContainer>\r\n                                <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\r\n                                  {action.title}\r\n                                </Typography>\r\n                              </QuickActionCard>\r\n                            </div>\r\n                          </Grow>\r\n                        </Grid>\r\n                      ))}\r\n                    </Grid>\r\n                  </CardContent>\r\n                </Card>\r\n              </Fade>\r\n\r\n              {/* Alerts */}\r\n              <Fade in={!loading} timeout={1400}>\r\n                <Card sx={{ flex: 1 }}>\r\n                  <CardContent sx={{ p: 3 }}>\r\n                    <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center' }}>\r\n                      <Warning sx={{ mr: 1, color: 'warning.main' }} />\r\n                      Alerts\r\n                    </Typography>\r\n                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>\r\n                      {alerts.map((alert, index) => (\r\n                        <Grow in={!loading} timeout={1600 + index * 100} key={alert.id}>\r\n                          <AlertCard severity={alert.severity}>\r\n                            <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between' }}>\r\n                              <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1, flex: 1 }}>\r\n                                {getSeverityIcon(alert.severity)}\r\n                                <Box>\r\n                                  <Typography variant=\"subtitle2\" sx={{ fontWeight: 600 }}>\r\n                                    {alert.title}\r\n                                  </Typography>\r\n                                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\r\n                                    {alert.message}\r\n                                  </Typography>\r\n                                  <Typography variant=\"caption\" color=\"text.secondary\">\r\n                                    {alert.time}\r\n                                  </Typography>\r\n                                </Box>\r\n                              </Box>\r\n                              <Button size=\"small\" variant=\"outlined\">\r\n                                {alert.action}\r\n                              </Button>\r\n                            </Box>\r\n                          </AlertCard>\r\n                        </Grow>\r\n                      ))}\r\n                    </Box>\r\n                  </CardContent>\r\n                </Card>\r\n              </Fade>\r\n            </Box>\r\n          </Grid>\r\n        </Grid>\r\n      </Box>\r\n  );\r\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,cAAc,EACdC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB,SACEC,UAAU,EACVC,YAAY,EACZC,YAAY,EACZC,MAAM,EACNC,WAAW,EACXC,SAAS,EACTC,GAAG,EACHC,MAAM,EACNC,SAAS,EACTC,QAAQ,EACRC,QAAQ,EACRC,OAAO,EACPC,WAAW,EACXC,IAAI,EACJC,QAAQ,QACH,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,YAAY,EAAEC,SAAS,QAAQ,oBAAoB;;AAE5D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,SAAS,GAAGN,MAAM,CAAC5B,IAAI,CAAC,CAAC,CAAC;EAAEmC,KAAK;EAAEC,KAAK,GAAG;AAAU,CAAC,MAAM;EAChEC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,SAAS;EACnBC,UAAU,EAAE,uCAAuC;EACnDC,MAAM,EAAE,aAAa5B,KAAK,CAACuB,KAAK,CAACM,OAAO,CAACL,KAAK,CAAC,CAACM,IAAI,EAAE,GAAG,CAAC,EAAE;EAE5D,SAAS,EAAE;IACTC,SAAS,EAAE,kBAAkB;IAC7BC,SAAS,EAAE,eAAehC,KAAK,CAACuB,KAAK,CAACM,OAAO,CAACL,KAAK,CAAC,CAACM,IAAI,EAAE,IAAI,CAAC,EAAE;IAElE,eAAe,EAAE;MACfC,SAAS,EAAE;IACb;EACF,CAAC;EAED,WAAW,EAAE;IACXE,OAAO,EAAE,IAAI;IACbR,QAAQ,EAAE,UAAU;IACpBS,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,UAAU,EAAE,0BAA0Bf,KAAK,CAACM,OAAO,CAACL,KAAK,CAAC,CAACM,IAAI,KAAKP,KAAK,CAACM,OAAO,CAACL,KAAK,CAAC,CAACe,KAAK,GAAG;IACjGC,YAAY,EAAE;EAChB;AACF,CAAC,CAAC,CAAC;AAACC,EAAA,GAzBEnB,SAAS;AA2Bf,MAAMoB,aAAa,GAAG1B,MAAM,CAAC9B,GAAG,CAAC,CAAC,CAAC;EAAEqC,KAAK;EAAEC,KAAK,GAAG;AAAU,CAAC,MAAM;EACnEmB,KAAK,EAAE,EAAE;EACTN,MAAM,EAAE,EAAE;EACVG,YAAY,EAAE,EAAE;EAChBI,OAAO,EAAE,MAAM;EACfC,UAAU,EAAE,QAAQ;EACpBC,cAAc,EAAE,QAAQ;EACxBR,UAAU,EAAE,2BAA2BtC,KAAK,CAACuB,KAAK,CAACM,OAAO,CAACL,KAAK,CAAC,CAACM,IAAI,EAAE,GAAG,CAAC,KAAK9B,KAAK,CAACuB,KAAK,CAACM,OAAO,CAACL,KAAK,CAAC,CAACM,IAAI,EAAE,IAAI,CAAC,GAAG;EAC1HH,UAAU,EAAE;AACd,CAAC,CAAC,CAAC;AAACoB,GAAA,GATEL,aAAa;AAWnB,MAAMM,eAAe,GAAGhC,MAAM,CAAC5B,IAAI,CAAC,CAAC,CAAC;EAAEmC;AAAM,CAAC,MAAM;EACnD0B,MAAM,EAAE,SAAS;EACjBtB,UAAU,EAAE,eAAe;EAC3BC,MAAM,EAAE,aAAaL,KAAK,CAACM,OAAO,CAACqB,OAAO,EAAE;EAE5C,SAAS,EAAE;IACTnB,SAAS,EAAE,kBAAkB;IAC7BC,SAAS,EAAET,KAAK,CAAC4B,OAAO,CAAC,CAAC,CAAC;IAC3BC,WAAW,EAAE7B,KAAK,CAACM,OAAO,CAACwB,OAAO,CAACvB;EACrC;AACF,CAAC,CAAC,CAAC;AAACwB,GAAA,GAVEN,eAAe;AAYrB,MAAMO,SAAS,GAAGvC,MAAM,CAACvB,KAAK,CAAC,CAAC,CAAC;EAAE8B,KAAK;EAAEiC,QAAQ,GAAG;AAAO,CAAC,KAAK;EAChE,MAAMC,MAAM,GAAG;IACbC,KAAK,EAAEnC,KAAK,CAACM,OAAO,CAAC6B,KAAK;IAC1BC,OAAO,EAAEpC,KAAK,CAACM,OAAO,CAAC8B,OAAO;IAC9BC,IAAI,EAAErC,KAAK,CAACM,OAAO,CAAC+B,IAAI;IACxBC,OAAO,EAAEtC,KAAK,CAACM,OAAO,CAACgC;EACzB,CAAC;EAED,OAAO;IACLC,OAAO,EAAEvC,KAAK,CAACwC,OAAO,CAAC,CAAC,CAAC;IACzBnC,MAAM,EAAE,aAAa5B,KAAK,CAACyD,MAAM,CAACD,QAAQ,CAAC,CAAC1B,IAAI,EAAE,GAAG,CAAC,EAAE;IACxDkC,eAAe,EAAEhE,KAAK,CAACyD,MAAM,CAACD,QAAQ,CAAC,CAAC1B,IAAI,EAAE,IAAI,CAAC;IACnDU,YAAY,EAAE,EAAE;IAChBb,UAAU,EAAE,eAAe;IAE3B,SAAS,EAAE;MACTqC,eAAe,EAAEhE,KAAK,CAACyD,MAAM,CAACD,QAAQ,CAAC,CAAC1B,IAAI,EAAE,GAAG,CAAC;MAClDC,SAAS,EAAE;IACb;EACF,CAAC;AACH,CAAC,CAAC;;AAEF;AAAAkC,GAAA,GAtBMV,SAAS;AAuBf,MAAMW,SAAS,GAAG,CAChB;EACEC,KAAK,EAAE,eAAe;EACtBC,KAAK,EAAE,SAAS;EAChBC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAElE,WAAW;EACjBmB,KAAK,EAAE,SAAS;EAChBgD,QAAQ,EAAE;AACZ,CAAC,EACD;EACEL,KAAK,EAAE,cAAc;EACrBC,KAAK,EAAE,KAAK;EACZC,MAAM,EAAE,OAAO;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAEpE,YAAY;EAClBqB,KAAK,EAAE,SAAS;EAChBgD,QAAQ,EAAE;AACZ,CAAC,EACD;EACEL,KAAK,EAAE,YAAY;EACnBC,KAAK,EAAE,IAAI;EACXC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE/D,MAAM;EACZgB,KAAK,EAAE,MAAM;EACbgD,QAAQ,EAAE;AACZ,CAAC,EACD;EACEL,KAAK,EAAE,iBAAiB;EACxBC,KAAK,EAAE,KAAK;EACZC,MAAM,EAAE,aAAa;EACrBC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAEjE,SAAS;EACfkB,KAAK,EAAE,OAAO;EACdgD,QAAQ,EAAE;AACZ,CAAC,CACF;AAED,MAAMC,YAAY,GAAG,CACnB;EAAEN,KAAK,EAAE,eAAe;EAAEI,IAAI,EAAE/D,MAAM;EAAEgB,KAAK,EAAE;AAAU,CAAC,EAC1D;EAAE2C,KAAK,EAAE,gBAAgB;EAAEI,IAAI,EAAEpE,YAAY;EAAEqB,KAAK,EAAE;AAAY,CAAC,EACnE;EAAE2C,KAAK,EAAE,cAAc;EAAEI,IAAI,EAAE7D,QAAQ;EAAEc,KAAK,EAAE;AAAO,CAAC,EACxD;EAAE2C,KAAK,EAAE,kBAAkB;EAAEI,IAAI,EAAEjE,SAAS;EAAEkB,KAAK,EAAE;AAAU,CAAC,CACjE;AAED,MAAMkD,MAAM,GAAG,CACb;EACEC,EAAE,EAAE,CAAC;EACLR,KAAK,EAAE,iBAAiB;EACxBS,OAAO,EAAE,uCAAuC;EAChDpB,QAAQ,EAAE,OAAO;EACjBqB,IAAI,EAAE,WAAW;EACjBC,MAAM,EAAE;AACV,CAAC,EACD;EACEH,EAAE,EAAE,CAAC;EACLR,KAAK,EAAE,YAAY;EACnBS,OAAO,EAAE,uCAAuC;EAChDpB,QAAQ,EAAE,SAAS;EACnBqB,IAAI,EAAE,YAAY;EAClBC,MAAM,EAAE;AACV,CAAC,EACD;EACEH,EAAE,EAAE,CAAC;EACLR,KAAK,EAAE,uBAAuB;EAC9BS,OAAO,EAAE,wCAAwC;EACjDpB,QAAQ,EAAE,MAAM;EAChBqB,IAAI,EAAE,YAAY;EAClBC,MAAM,EAAE;AACV,CAAC,CACF;AAED,MAAMC,YAAY,GAAG,CACnB;EAAEJ,EAAE,EAAE,OAAO;EAAEK,QAAQ,EAAE,UAAU;EAAEC,KAAK,EAAE,kBAAkB;EAAEC,KAAK,EAAE,QAAQ;EAAEC,MAAM,EAAE,WAAW;EAAEN,IAAI,EAAE;AAAY,CAAC,EACzH;EAAEF,EAAE,EAAE,OAAO;EAAEK,QAAQ,EAAE,YAAY;EAAEC,KAAK,EAAE,oBAAoB;EAAEC,KAAK,EAAE,OAAO;EAAEC,MAAM,EAAE,WAAW;EAAEN,IAAI,EAAE;AAAY,CAAC,EAC5H;EAAEF,EAAE,EAAE,OAAO;EAAEK,QAAQ,EAAE,YAAY;EAAEC,KAAK,EAAE,oBAAoB;EAAEC,KAAK,EAAE,QAAQ;EAAEC,MAAM,EAAE,SAAS;EAAEN,IAAI,EAAE;AAAY,CAAC,EAC3H;EAAEF,EAAE,EAAE,OAAO;EAAEK,QAAQ,EAAE,aAAa;EAAEC,KAAK,EAAE,mBAAmB;EAAEC,KAAK,EAAE,OAAO;EAAEC,MAAM,EAAE,WAAW;EAAEN,IAAI,EAAE;AAAa,CAAC,EAC7H;EAAEF,EAAE,EAAE,OAAO;EAAEK,QAAQ,EAAE,WAAW;EAAEC,KAAK,EAAE,cAAc;EAAEC,KAAK,EAAE,QAAQ;EAAEC,MAAM,EAAE,WAAW;EAAEN,IAAI,EAAE;AAAa,CAAC,CACxH;AAED,eAAe,SAASO,cAAcA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EACvC,MAAMlE,KAAK,GAAGxB,QAAQ,CAAC,CAAC;EACxB,MAAM,CAAC2F,OAAO,EAAEC,UAAU,CAAC,GAAG3G,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC4G,aAAa,EAAEC,gBAAgB,CAAC,GAAG7G,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC+F,YAAY,EAAEe,eAAe,CAAC,GAAG9G,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC+G,WAAW,EAAEC,cAAc,CAAC,GAAGhH,QAAQ,CAAC,IAAIiH,IAAI,CAAC,CAAC,CAAC;EAE1DhH,SAAS,CAAC,MAAM;IACdiH,kBAAkB,CAAC,CAAC;IACpB;IACA,MAAMC,QAAQ,GAAGC,WAAW,CAACF,kBAAkB,EAAE,KAAK,CAAC;IACvD,OAAO,MAAMG,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM,CAACW,KAAK,EAAEC,MAAM,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACxCvF,YAAY,CAACwF,iBAAiB,CAAC,CAAC,EAChCvF,SAAS,CAACwF,SAAS,CAAC,CAAC,CAAC,CACvB,CAAC;MAEFd,gBAAgB,CAACS,KAAK,CAAC;MACvBR,eAAe,CAACS,MAAM,CAAC;MACvBP,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,OAAOvC,KAAK,EAAE;MACdkD,OAAO,CAAClD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD,CAAC,SAAS;MACRiC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkB,cAAc,GAAI1B,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAM2B,eAAe,GAAItD,QAAQ,IAAK;IACpC,QAAQA,QAAQ;MACd,KAAK,OAAO;QAAE,oBAAOnC,OAAA,CAACT,OAAO;UAACY,KAAK,EAAC;QAAO;UAAAuF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9C,KAAK,SAAS;QAAE,oBAAO7F,OAAA,CAACP,IAAI;UAACU,KAAK,EAAC;QAAS;UAAAuF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/C,KAAK,MAAM;QAAE,oBAAO7F,OAAA,CAACR,WAAW;UAACW,KAAK,EAAC;QAAM;UAAAuF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAChD;QAAS,oBAAO7F,OAAA,CAACP,IAAI;UAAAiG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC1B;EACF,CAAC;EAED,IAAIxB,OAAO,EAAE;IACX,oBACIrE,OAAA,CAACnC,GAAG;MAACiI,EAAE,EAAE;QAAExE,KAAK,EAAE,MAAM;QAAEyE,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,eAChChG,OAAA,CAACzB,cAAc;QAAAmH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC;EAEZ;EAEA,oBACI7F,OAAA,CAACnC,GAAG;IAACiI,EAAE,EAAE;MAAEG,QAAQ,EAAE;IAAE,CAAE;IAAAD,QAAA,gBAEvBhG,OAAA,CAAClC,IAAI;MAACoI,SAAS;MAACxD,OAAO,EAAE,CAAE;MAACoD,EAAE,EAAE;QAAEK,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,EACvC,CAACzB,aAAa,GAAG,CAChB;QACEzB,KAAK,EAAE,eAAe;QACtBC,KAAK,EAAE,IAAI,EAAAkB,qBAAA,GAAAM,aAAa,CAAC6B,YAAY,cAAAnC,qBAAA,uBAA1BA,qBAAA,CAA4BoC,cAAc,CAAC,CAAC,KAAI,GAAG,EAAE;QAChErD,MAAM,EAAE,GAAGuB,aAAa,CAAC+B,aAAa,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG/B,aAAa,CAAC+B,aAAa,IAAI,CAAC,GAAG;QAC3FrD,KAAK,EAAEsB,aAAa,CAAC+B,aAAa,GAAG,CAAC,GAAG,IAAI,GAAG,MAAM;QACtDpD,IAAI,EAAElE,WAAW;QACjBmB,KAAK,EAAE;MACT,CAAC,EACD;QACE2C,KAAK,EAAE,cAAc;QACrBC,KAAK,EAAE,EAAAmB,qBAAA,GAAAK,aAAa,CAACgC,WAAW,cAAArC,qBAAA,uBAAzBA,qBAAA,CAA2BmC,cAAc,CAAC,CAAC,KAAI,GAAG;QACzDrD,MAAM,EAAE,GAAGuB,aAAa,CAACiC,YAAY,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAGjC,aAAa,CAACiC,YAAY,IAAI,CAAC,GAAG;QACzFvD,KAAK,EAAEsB,aAAa,CAACiC,YAAY,GAAG,CAAC,GAAG,IAAI,GAAG,MAAM;QACrDtD,IAAI,EAAEpE,YAAY;QAClBqB,KAAK,EAAE;MACT,CAAC,EACD;QACE2C,KAAK,EAAE,YAAY;QACnBC,KAAK,EAAE,EAAAoB,qBAAA,GAAAI,aAAa,CAACkC,SAAS,cAAAtC,qBAAA,uBAAvBA,qBAAA,CAAyBkC,cAAc,CAAC,CAAC,KAAI,GAAG;QACvDrD,MAAM,EAAE,IAAI;QACZC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE/D,MAAM;QACZgB,KAAK,EAAE;MACT,CAAC,EACD;QACE2C,KAAK,EAAE,iBAAiB;QACxBC,KAAK,EAAE,EAAAqB,qBAAA,GAAAG,aAAa,CAACmC,cAAc,cAAAtC,qBAAA,uBAA5BA,qBAAA,CAA8BiC,cAAc,CAAC,CAAC,KAAI,GAAG;QAC5DrD,MAAM,EAAEuB,aAAa,CAACoC,aAAa,GAAG,IAAIpC,aAAa,CAACoC,aAAa,EAAE,GAAG,GAAG;QAC7E1D,KAAK,EAAEsB,aAAa,CAACoC,aAAa,GAAG,CAAC,GAAG,MAAM,GAAG,SAAS;QAC3DzD,IAAI,EAAEjE,SAAS;QACfkB,KAAK,EAAE;MACT,CAAC,CACF,GAAG0C,SAAS,EAAE+D,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC7B9G,OAAA,CAAClC,IAAI;QAACiJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAlB,QAAA,eAC9BhG,OAAA,CAACvB,IAAI;UAAC0I,EAAE,EAAE,CAAC9C,OAAQ;UAAC+C,OAAO,EAAE,GAAG,GAAGN,KAAK,GAAG,GAAI;UAAAd,QAAA,eAC7ChG,OAAA;YAAAgG,QAAA,eACEhG,OAAA,CAACC,SAAS;cAACE,KAAK,EAAE0G,IAAI,CAAC1G,KAAM;cAAA6F,QAAA,eAC3BhG,OAAA,CAAChC,WAAW;gBAAC8H,EAAE,EAAE;kBAAEuB,CAAC,EAAE;gBAAE,CAAE;gBAAArB,QAAA,eACxBhG,OAAA,CAACnC,GAAG;kBAACiI,EAAE,EAAE;oBAAEvE,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,YAAY;oBAAEC,cAAc,EAAE;kBAAgB,CAAE;kBAAAuE,QAAA,gBACtFhG,OAAA,CAACnC,GAAG;oBAACiI,EAAE,EAAE;sBAAEwB,IAAI,EAAE;oBAAE,CAAE;oBAAAtB,QAAA,gBACnBhG,OAAA,CAAC/B,UAAU;sBAACsJ,OAAO,EAAC,OAAO;sBAACpH,KAAK,EAAC,gBAAgB;sBAACqH,YAAY;sBAAAxB,QAAA,EAC5Da,IAAI,CAAC/D;oBAAK;sBAAA4C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC,eACb7F,OAAA,CAAC/B,UAAU;sBAACsJ,OAAO,EAAC,IAAI;sBAACE,SAAS,EAAC,IAAI;sBAAC3B,EAAE,EAAE;wBAAE4B,UAAU,EAAE,GAAG;wBAAEvB,EAAE,EAAE;sBAAE,CAAE;sBAAAH,QAAA,EACpEa,IAAI,CAAC9D;oBAAK;sBAAA2C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC,eACb7F,OAAA,CAACnC,GAAG;sBAACiI,EAAE,EAAE;wBAAEvE,OAAO,EAAE,MAAM;wBAAEC,UAAU,EAAE,QAAQ;wBAAEmG,GAAG,EAAE;sBAAE,CAAE;sBAAA3B,QAAA,gBACzDhG,OAAA,CAAC7B,IAAI;wBACHyJ,KAAK,EAAEf,IAAI,CAAC7D,MAAO;wBACnB6E,IAAI,EAAC,OAAO;wBACZ1H,KAAK,EAAE0G,IAAI,CAAC5D,KAAK,KAAK,IAAI,GAAG,SAAS,GAAG4D,IAAI,CAAC5D,KAAK,KAAK,MAAM,GAAG,OAAO,GAAG,SAAU;wBACrFC,IAAI,EAAE2D,IAAI,CAAC5D,KAAK,KAAK,IAAI,gBAAGjD,OAAA,CAACpB,UAAU;0BAAA8G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,GAAGgB,IAAI,CAAC5D,KAAK,KAAK,MAAM,gBAAGjD,OAAA,CAACnB,YAAY;0BAAA6G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAAG7F,OAAA,CAACT,OAAO;0BAAAmG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBACpGC,EAAE,EAAE;0BAAEgC,QAAQ,EAAE;wBAAU;sBAAE;wBAAApC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B,CAAC,eACF7F,OAAA,CAAC/B,UAAU;wBAACsJ,OAAO,EAAC,SAAS;wBAACpH,KAAK,EAAC,gBAAgB;wBAAA6F,QAAA,EACjDa,IAAI,CAAC1D;sBAAQ;wBAAAuC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN7F,OAAA,CAACqB,aAAa;oBAAClB,KAAK,EAAE0G,IAAI,CAAC1G,KAAM;oBAAA6F,QAAA,eAC/BhG,OAAA,CAAC6G,IAAI,CAAC3D,IAAI;sBACR6E,SAAS,EAAC,YAAY;sBACtBjC,EAAE,EAAE;wBACFgC,QAAQ,EAAE,EAAE;wBACZ3H,KAAK,EAAE,GAAG0G,IAAI,CAAC1G,KAAK,OAAO;wBAC3BG,UAAU,EAAE;sBACd;oBAAE;sBAAAoF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC,GAxC6BgB,IAAI,CAAC/D,KAAK;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAyC1C,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP7F,OAAA,CAAClC,IAAI;MAACoI,SAAS;MAACxD,OAAO,EAAE,CAAE;MAAAsD,QAAA,gBAEzBhG,OAAA,CAAClC,IAAI;QAACiJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAAlB,QAAA,eACvBhG,OAAA,CAACxB,IAAI;UAAC2I,EAAE,EAAE,CAAC9C,OAAQ;UAAC+C,OAAO,EAAE,GAAI;UAAApB,QAAA,eAC/BhG,OAAA,CAACjC,IAAI;YAAC+H,EAAE,EAAE;cAAE9E,MAAM,EAAE;YAAO,CAAE;YAAAgF,QAAA,eAC3BhG,OAAA,CAAChC,WAAW;cAAC8H,EAAE,EAAE;gBAAEuB,CAAC,EAAE;cAAE,CAAE;cAAArB,QAAA,gBACxBhG,OAAA,CAACnC,GAAG;gBAACiI,EAAE,EAAE;kBAAEvE,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEC,cAAc,EAAE,eAAe;kBAAE0E,EAAE,EAAE;gBAAE,CAAE;gBAAAH,QAAA,gBACzFhG,OAAA,CAAC/B,UAAU;kBAACsJ,OAAO,EAAC,IAAI;kBAACzB,EAAE,EAAE;oBAAE4B,UAAU,EAAE;kBAAI,CAAE;kBAAA1B,QAAA,EAAC;gBAElD;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb7F,OAAA,CAAC9B,MAAM;kBAACqJ,OAAO,EAAC,UAAU;kBAACM,IAAI,EAAC,OAAO;kBAAC1H,KAAK,EAAC,SAAS;kBAAA6F,QAAA,EAAC;gBAExD;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACN7F,OAAA,CAACnC,GAAG;gBAACiI,EAAE,EAAE;kBAAEvE,OAAO,EAAE,MAAM;kBAAEyG,aAAa,EAAE,QAAQ;kBAAEL,GAAG,EAAE;gBAAE,CAAE;gBAAA3B,QAAA,EAC3D,CAACtC,YAAY,CAACuE,MAAM,GAAG,CAAC,GAAGvE,YAAY,GAAG,CACzC;kBAAEwE,GAAG,EAAE,GAAG;kBAAEC,WAAW,EAAE,OAAO;kBAAExE,QAAQ,EAAE;oBAAEyE,IAAI,EAAE;kBAAW,CAAC;kBAAExE,KAAK,EAAE,CAAC;oBAAEwE,IAAI,EAAE;kBAAQ,CAAC,EAAE;oBAAEA,IAAI,EAAE;kBAAY,CAAC,CAAC;kBAAEvE,KAAK,EAAE,KAAK;kBAAEC,MAAM,EAAE,WAAW;kBAAEuE,SAAS,EAAE,IAAIzD,IAAI,CAACA,IAAI,CAAC0D,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAACC,WAAW,CAAC;gBAAE,CAAC,EAChN;kBAAEL,GAAG,EAAE,GAAG;kBAAEC,WAAW,EAAE,OAAO;kBAAExE,QAAQ,EAAE;oBAAEyE,IAAI,EAAE;kBAAa,CAAC;kBAAExE,KAAK,EAAE,CAAC;oBAAEwE,IAAI,EAAE;kBAAa,CAAC,EAAE;oBAAEA,IAAI,EAAE;kBAAS,CAAC,CAAC;kBAAEvE,KAAK,EAAE,IAAI;kBAAEC,MAAM,EAAE,WAAW;kBAAEuE,SAAS,EAAE,IAAIzD,IAAI,CAACA,IAAI,CAAC0D,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAACC,WAAW,CAAC;gBAAE,CAAC,CACpN,EAAE3B,GAAG,CAAC,CAAC4B,KAAK,EAAE1B,KAAK;kBAAA,IAAA2B,eAAA,EAAAC,YAAA;kBAAA,oBAClB1I,OAAA,CAACvB,IAAI;oBAAC0I,EAAE,EAAE,CAAC9C,OAAQ;oBAAC+C,OAAO,EAAE,IAAI,GAAGN,KAAK,GAAG,GAAI;oBAAAd,QAAA,eAC9ChG,OAAA,CAAC5B,KAAK;sBACJ0H,EAAE,EAAE;wBACFuB,CAAC,EAAE,CAAC;wBACJ9G,MAAM,EAAE,WAAW;wBACnBwB,WAAW,EAAE,SAAS;wBACtBZ,YAAY,EAAE,CAAC;wBACfb,UAAU,EAAE,eAAe;wBAC3B,SAAS,EAAE;0BACTyB,WAAW,EAAE,cAAc;0BAC3BrB,SAAS,EAAE,kBAAkB;0BAC7BC,SAAS,EAAE;wBACb;sBACF,CAAE;sBAAAqF,QAAA,eAEFhG,OAAA,CAACnC,GAAG;wBAACiI,EAAE,EAAE;0BAAEvE,OAAO,EAAE,MAAM;0BAAEC,UAAU,EAAE,QAAQ;0BAAEC,cAAc,EAAE;wBAAgB,CAAE;wBAAAuE,QAAA,gBAClFhG,OAAA,CAACnC,GAAG;0BAACiI,EAAE,EAAE;4BAAEwB,IAAI,EAAE;0BAAE,CAAE;0BAAAtB,QAAA,gBACnBhG,OAAA,CAAC/B,UAAU;4BAACsJ,OAAO,EAAC,WAAW;4BAACzB,EAAE,EAAE;8BAAE4B,UAAU,EAAE;4BAAI,CAAE;4BAAA1B,QAAA,GACrDwC,KAAK,CAACL,WAAW,IAAIK,KAAK,CAAClF,EAAE,EAAC,KAAG,EAAC,EAAAmF,eAAA,GAAAD,KAAK,CAAC7E,QAAQ,cAAA8E,eAAA,uBAAdA,eAAA,CAAgBL,IAAI,KAAII,KAAK,CAAC7E,QAAQ;0BAAA;4BAAA+B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC/D,CAAC,eACb7F,OAAA,CAAC/B,UAAU;4BAACsJ,OAAO,EAAC,OAAO;4BAACpH,KAAK,EAAC,gBAAgB;4BAAA6F,QAAA,EAC/C,EAAA0C,YAAA,GAAAF,KAAK,CAAC5E,KAAK,cAAA8E,YAAA,uBAAXA,YAAA,CAAa9B,GAAG,CAACG,IAAI,IAAIA,IAAI,CAACqB,IAAI,CAAC,CAACO,IAAI,CAAC,IAAI,CAAC,KAAIH,KAAK,CAAC5E;0BAAK;4BAAA8B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CAAC,eACb7F,OAAA,CAAC/B,UAAU;4BAACsJ,OAAO,EAAC,SAAS;4BAACpH,KAAK,EAAC,gBAAgB;4BAAA6F,QAAA,EACjDwC,KAAK,CAACH,SAAS,GACd,GAAGO,IAAI,CAACC,KAAK,CAAC,CAACjE,IAAI,CAAC0D,GAAG,CAAC,CAAC,GAAG,IAAI1D,IAAI,CAAC4D,KAAK,CAACH,SAAS,CAAC,CAACS,OAAO,CAAC,CAAC,IAAI,KAAK,CAAC,UAAU,GACnFN,KAAK,CAAChF;0BAAI;4BAAAkC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACV,CAAC,eACN7F,OAAA,CAACnC,GAAG;0BAACiI,EAAE,EAAE;4BAAEvE,OAAO,EAAE,MAAM;4BAAEC,UAAU,EAAE,QAAQ;4BAAEmG,GAAG,EAAE;0BAAE,CAAE;0BAAA3B,QAAA,gBACzDhG,OAAA,CAAC/B,UAAU;4BAACsJ,OAAO,EAAC,IAAI;4BAACzB,EAAE,EAAE;8BAAE4B,UAAU,EAAE;4BAAI,CAAE;4BAAA1B,QAAA,EAC9C,OAAOwC,KAAK,CAAC3E,KAAK,KAAK,QAAQ,GAAG,IAAI2E,KAAK,CAAC3E,KAAK,CAACkF,OAAO,CAAC,CAAC,CAAC,EAAE,GAAGP,KAAK,CAAC3E;0BAAK;4BAAA6B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnE,CAAC,eACb7F,OAAA,CAAC7B,IAAI;4BACHyJ,KAAK,EAAEY,KAAK,CAAC1E,MAAO;4BACpB+D,IAAI,EAAC,OAAO;4BACZ1H,KAAK,EAAEqF,cAAc,CAACgD,KAAK,CAAC1E,MAAM,CAAE;4BACpCgC,EAAE,EAAE;8BAAEkD,aAAa,EAAE;4BAAa;0BAAE;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC,GAzC4C2C,KAAK,CAACN,GAAG,IAAIM,KAAK,CAAClF,EAAE;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA0CrE,CAAC;gBAAA,CACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGP7F,OAAA,CAAClC,IAAI;QAACiJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAAlB,QAAA,eACvBhG,OAAA,CAACnC,GAAG;UAACiI,EAAE,EAAE;YAAEvE,OAAO,EAAE,MAAM;YAAEyG,aAAa,EAAE,QAAQ;YAAEL,GAAG,EAAE,CAAC;YAAE3G,MAAM,EAAE;UAAO,CAAE;UAAAgF,QAAA,gBAE5EhG,OAAA,CAACxB,IAAI;YAAC2I,EAAE,EAAE,CAAC9C,OAAQ;YAAC+C,OAAO,EAAE,IAAK;YAAApB,QAAA,eAChChG,OAAA,CAACjC,IAAI;cAAAiI,QAAA,eACHhG,OAAA,CAAChC,WAAW;gBAAC8H,EAAE,EAAE;kBAAEuB,CAAC,EAAE;gBAAE,CAAE;gBAAArB,QAAA,gBACxBhG,OAAA,CAAC/B,UAAU;kBAACsJ,OAAO,EAAC,IAAI;kBAACzB,EAAE,EAAE;oBAAE4B,UAAU,EAAE,GAAG;oBAAEvB,EAAE,EAAE,CAAC;oBAAE5E,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE;kBAAS,CAAE;kBAAAwE,QAAA,gBAC7FhG,OAAA,CAACd,GAAG;oBAAC4G,EAAE,EAAE;sBAAEmD,EAAE,EAAE;oBAAE;kBAAE;oBAAAvD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,iBAExB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb7F,OAAA,CAAClC,IAAI;kBAACoI,SAAS;kBAACxD,OAAO,EAAE,CAAE;kBAAAsD,QAAA,EACxB5C,YAAY,CAACwD,GAAG,CAAC,CAACnD,MAAM,EAAEqD,KAAK,kBAC9B9G,OAAA,CAAClC,IAAI;oBAACiJ,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAhB,QAAA,eACfhG,OAAA,CAACvB,IAAI;sBAAC0I,EAAE,EAAE,CAAC9C,OAAQ;sBAAC+C,OAAO,EAAE,IAAI,GAAGN,KAAK,GAAG,GAAI;sBAAAd,QAAA,eAC9ChG,OAAA;wBAAAgG,QAAA,eACEhG,OAAA,CAAC2B,eAAe;0BAACmE,EAAE,EAAE;4BAAEoD,SAAS,EAAE,QAAQ;4BAAE7B,CAAC,EAAE;0BAAE,CAAE;0BAAArB,QAAA,gBACjDhG,OAAA,CAACqB,aAAa;4BAAClB,KAAK,EAAEsD,MAAM,CAACtD,KAAM;4BAAC2F,EAAE,EAAE;8BAAExE,KAAK,EAAE,EAAE;8BAAEN,MAAM,EAAE,EAAE;8BAAEmI,EAAE,EAAE,MAAM;8BAAEhD,EAAE,EAAE;4BAAE,CAAE;4BAAAH,QAAA,eACnFhG,OAAA,CAACyD,MAAM,CAACP,IAAI;8BAAAwC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eAChB7F,OAAA,CAAC/B,UAAU;4BAACsJ,OAAO,EAAC,OAAO;4BAACzB,EAAE,EAAE;8BAAE4B,UAAU,EAAE;4BAAI,CAAE;4BAAA1B,QAAA,EACjDvC,MAAM,CAACX;0BAAK;4BAAA4C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACf;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC,GAZcpC,MAAM,CAACX,KAAK;oBAAA4C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAa7B,CACP;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGP7F,OAAA,CAACxB,IAAI;YAAC2I,EAAE,EAAE,CAAC9C,OAAQ;YAAC+C,OAAO,EAAE,IAAK;YAAApB,QAAA,eAChChG,OAAA,CAACjC,IAAI;cAAC+H,EAAE,EAAE;gBAAEwB,IAAI,EAAE;cAAE,CAAE;cAAAtB,QAAA,eACpBhG,OAAA,CAAChC,WAAW;gBAAC8H,EAAE,EAAE;kBAAEuB,CAAC,EAAE;gBAAE,CAAE;gBAAArB,QAAA,gBACxBhG,OAAA,CAAC/B,UAAU;kBAACsJ,OAAO,EAAC,IAAI;kBAACzB,EAAE,EAAE;oBAAE4B,UAAU,EAAE,GAAG;oBAAEvB,EAAE,EAAE,CAAC;oBAAE5E,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE;kBAAS,CAAE;kBAAAwE,QAAA,gBAC7FhG,OAAA,CAACT,OAAO;oBAACuG,EAAE,EAAE;sBAAEmD,EAAE,EAAE,CAAC;sBAAE9I,KAAK,EAAE;oBAAe;kBAAE;oBAAAuF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,UAEnD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb7F,OAAA,CAACnC,GAAG;kBAACiI,EAAE,EAAE;oBAAEvE,OAAO,EAAE,MAAM;oBAAEyG,aAAa,EAAE,QAAQ;oBAAEL,GAAG,EAAE;kBAAE,CAAE;kBAAA3B,QAAA,EAC3D3C,MAAM,CAACuD,GAAG,CAAC,CAACwC,KAAK,EAAEtC,KAAK,kBACvB9G,OAAA,CAACvB,IAAI;oBAAC0I,EAAE,EAAE,CAAC9C,OAAQ;oBAAC+C,OAAO,EAAE,IAAI,GAAGN,KAAK,GAAG,GAAI;oBAAAd,QAAA,eAC9ChG,OAAA,CAACkC,SAAS;sBAACC,QAAQ,EAAEiH,KAAK,CAACjH,QAAS;sBAAA6D,QAAA,eAClChG,OAAA,CAACnC,GAAG;wBAACiI,EAAE,EAAE;0BAAEvE,OAAO,EAAE,MAAM;0BAAEC,UAAU,EAAE,YAAY;0BAAEC,cAAc,EAAE;wBAAgB,CAAE;wBAAAuE,QAAA,gBACtFhG,OAAA,CAACnC,GAAG;0BAACiI,EAAE,EAAE;4BAAEvE,OAAO,EAAE,MAAM;4BAAEC,UAAU,EAAE,YAAY;4BAAEmG,GAAG,EAAE,CAAC;4BAAEL,IAAI,EAAE;0BAAE,CAAE;0BAAAtB,QAAA,GACrEP,eAAe,CAAC2D,KAAK,CAACjH,QAAQ,CAAC,eAChCnC,OAAA,CAACnC,GAAG;4BAAAmI,QAAA,gBACFhG,OAAA,CAAC/B,UAAU;8BAACsJ,OAAO,EAAC,WAAW;8BAACzB,EAAE,EAAE;gCAAE4B,UAAU,EAAE;8BAAI,CAAE;8BAAA1B,QAAA,EACrDoD,KAAK,CAACtG;4BAAK;8BAAA4C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACF,CAAC,eACb7F,OAAA,CAAC/B,UAAU;8BAACsJ,OAAO,EAAC,OAAO;8BAACpH,KAAK,EAAC,gBAAgB;8BAAC2F,EAAE,EAAE;gCAAEK,EAAE,EAAE;8BAAE,CAAE;8BAAAH,QAAA,EAC9DoD,KAAK,CAAC7F;4BAAO;8BAAAmC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC,eACb7F,OAAA,CAAC/B,UAAU;8BAACsJ,OAAO,EAAC,SAAS;8BAACpH,KAAK,EAAC,gBAAgB;8BAAA6F,QAAA,EACjDoD,KAAK,CAAC5F;4BAAI;8BAAAkC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACD,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACN7F,OAAA,CAAC9B,MAAM;0BAAC2J,IAAI,EAAC,OAAO;0BAACN,OAAO,EAAC,UAAU;0BAAAvB,QAAA,EACpCoD,KAAK,CAAC3F;wBAAM;0BAAAiC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACG;kBAAC,GArBwCuD,KAAK,CAAC9F,EAAE;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAsBxD,CACP;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEZ;AAAC7B,EAAA,CA/RuBD,cAAc;EAAA,QACtBrF,QAAQ;AAAA;AAAA2K,GAAA,GADAtF,cAAc;AAAA,IAAA3C,EAAA,EAAAM,GAAA,EAAAO,GAAA,EAAAW,GAAA,EAAAyG,GAAA;AAAAC,YAAA,CAAAlI,EAAA;AAAAkI,YAAA,CAAA5H,GAAA;AAAA4H,YAAA,CAAArH,GAAA;AAAAqH,YAAA,CAAA1G,GAAA;AAAA0G,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}