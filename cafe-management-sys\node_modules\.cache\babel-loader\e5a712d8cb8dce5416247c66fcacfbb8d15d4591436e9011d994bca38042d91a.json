{"ast": null, "code": "var _jsxFileName = \"D:\\\\Github_Repos\\\\cafe-mangement-system-react\\\\cafe-management-sys\\\\src\\\\contexts\\\\ThemeContext.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { createTheme, ThemeProvider as MuiThemeProvider } from '@mui/material/styles';\nimport { useLocation } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ThemeContext = /*#__PURE__*/createContext();\nexport const useTheme = () => {\n  _s();\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n\n// Customer Theme\n_s(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst customerTheme = createTheme({\n  palette: {\n    mode: 'light',\n    primary: {\n      main: '#8B4513',\n      // Coffee brown\n      light: '#A67B5B',\n      dark: '#6A3400',\n      contrastText: '#FFFFFF'\n    },\n    secondary: {\n      main: '#D4A574',\n      // Warm golden brown\n      light: '#E6C2A6',\n      dark: '#B8956A',\n      contrastText: '#333333'\n    },\n    accent: {\n      main: '#F4A460',\n      // Sandy brown accent\n      light: '#F7B885',\n      dark: '#E0944A'\n    },\n    background: {\n      default: '#FBF8F5',\n      // Warm off-white\n      paper: '#FFFFFF',\n      secondary: '#F5F2ED',\n      // Light coffee cream\n      tertiary: '#FAF7F2' // Even lighter cream\n    },\n    text: {\n      primary: '#2C1810',\n      secondary: '#5D4E37'\n    }\n  },\n  typography: {\n    fontFamily: '\"Open Sans\", \"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    h1: {\n      fontFamily: '\"Merriweather\", serif'\n    },\n    h2: {\n      fontFamily: '\"Merriweather\", serif'\n    },\n    h3: {\n      fontFamily: '\"Merriweather\", serif'\n    },\n    h4: {\n      fontFamily: '\"Merriweather\", serif'\n    }\n  },\n  shape: {\n    borderRadius: 12\n  }\n});\n\n// Admin Theme\nconst adminTheme = createTheme({\n  palette: {\n    mode: 'light',\n    primary: {\n      main: '#1976d2',\n      light: '#42a5f5',\n      dark: '#1565c0',\n      contrastText: '#ffffff'\n    },\n    secondary: {\n      main: '#dc004e',\n      light: '#ff5983',\n      dark: '#9a0036',\n      contrastText: '#ffffff'\n    },\n    background: {\n      default: '#f5f5f5',\n      paper: '#ffffff'\n    },\n    text: {\n      primary: '#212121',\n      secondary: '#757575'\n    }\n  },\n  typography: {\n    fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif'\n  },\n  shape: {\n    borderRadius: 8\n  },\n  components: {\n    MuiDrawer: {\n      styleOverrides: {\n        paper: {\n          backgroundColor: '#ffffff',\n          borderRight: '1px solid #e0e0e0'\n        }\n      }\n    },\n    MuiAppBar: {\n      styleOverrides: {\n        root: {\n          backgroundColor: '#ffffff',\n          color: '#212121',\n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n        }\n      }\n    }\n  }\n});\n\n// Dark Admin Theme\nconst adminDarkTheme = createTheme({\n  palette: {\n    mode: 'dark',\n    primary: {\n      main: '#90caf9',\n      light: '#e3f2fd',\n      dark: '#42a5f5',\n      contrastText: '#000000'\n    },\n    secondary: {\n      main: '#f48fb1',\n      light: '#fce4ec',\n      dark: '#ad1457',\n      contrastText: '#000000'\n    },\n    background: {\n      default: '#121212',\n      paper: '#1e1e1e'\n    },\n    text: {\n      primary: '#ffffff',\n      secondary: '#b0b0b0'\n    }\n  },\n  typography: {\n    fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif'\n  },\n  shape: {\n    borderRadius: 8\n  },\n  components: {\n    MuiDrawer: {\n      styleOverrides: {\n        paper: {\n          backgroundColor: '#1e1e1e',\n          borderRight: '1px solid #333333'\n        }\n      }\n    },\n    MuiAppBar: {\n      styleOverrides: {\n        root: {\n          backgroundColor: '#1e1e1e',\n          color: '#ffffff',\n          boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n        }\n      }\n    }\n  }\n});\nexport const ThemeProvider = ({\n  children\n}) => {\n  _s2();\n  const [isDarkMode, setIsDarkMode] = useState(false);\n  const location = useLocation();\n\n  // Determine if we're in admin area\n  const isAdminArea = location.pathname.startsWith('/admin');\n  useEffect(() => {\n    // Load theme preference from localStorage\n    const savedTheme = localStorage.getItem('themeMode');\n    if (savedTheme === 'dark') {\n      setIsDarkMode(true);\n    }\n  }, []);\n  const toggleTheme = () => {\n    const newMode = !isDarkMode;\n    setIsDarkMode(newMode);\n    localStorage.setItem('themeMode', newMode ? 'dark' : 'light');\n  };\n\n  // Select appropriate theme based on area and mode\n  const getTheme = () => {\n    if (isAdminArea) {\n      return isDarkMode ? adminDarkTheme : adminTheme;\n    }\n    return customerTheme; // Customer area always uses light theme for now\n  };\n  const value = {\n    isDarkMode,\n    toggleTheme,\n    isAdminArea,\n    theme: getTheme()\n  };\n  return /*#__PURE__*/_jsxDEV(ThemeContext.Provider, {\n    value: value,\n    children: /*#__PURE__*/_jsxDEV(MuiThemeProvider, {\n      theme: getTheme(),\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 200,\n    columnNumber: 5\n  }, this);\n};\n_s2(ThemeProvider, \"ZZY+hOvbsPoTZkUVtGQR3Udzh60=\", false, function () {\n  return [useLocation];\n});\n_c = ThemeProvider;\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "createTheme", "ThemeProvider", "MuiThemeProvider", "useLocation", "jsxDEV", "_jsxDEV", "ThemeContext", "useTheme", "_s", "context", "Error", "customerTheme", "palette", "mode", "primary", "main", "light", "dark", "contrastText", "secondary", "accent", "background", "default", "paper", "tertiary", "text", "typography", "fontFamily", "h1", "h2", "h3", "h4", "shape", "borderRadius", "adminTheme", "components", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styleOverrides", "backgroundColor", "borderRight", "MuiAppBar", "root", "color", "boxShadow", "adminDarkTheme", "children", "_s2", "isDarkMode", "setIsDarkMode", "location", "isAdminArea", "pathname", "startsWith", "savedTheme", "localStorage", "getItem", "toggleTheme", "newMode", "setItem", "getTheme", "value", "theme", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Github_Repos/cafe-mangement-system-react/cafe-management-sys/src/contexts/ThemeContext.jsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport { createTheme, ThemeProvider as MuiThemeProvider } from '@mui/material/styles';\nimport { useLocation } from 'react-router-dom';\n\nconst ThemeContext = createContext();\n\nexport const useTheme = () => {\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n\n// Customer Theme\nconst customerTheme = createTheme({\n  palette: {\n    mode: 'light',\n    primary: {\n      main: '#8B4513', // Coffee brown\n      light: '#A67B5B',\n      dark: '#6A3400',\n      contrastText: '#FFFFFF',\n    },\n    secondary: {\n      main: '#D4A574', // Warm golden brown\n      light: '#E6C2A6',\n      dark: '#B8956A',\n      contrastText: '#333333',\n    },\n    accent: {\n      main: '#F4A460', // Sandy brown accent\n      light: '#F7B885',\n      dark: '#E0944A',\n    },\n    background: {\n      default: '#FBF8F5', // Warm off-white\n      paper: '#FFFFFF',\n      secondary: '#F5F2ED', // Light coffee cream\n      tertiary: '#FAF7F2', // Even lighter cream\n    },\n    text: {\n      primary: '#2C1810',\n      secondary: '#5D4E37',\n    },\n  },\n  typography: {\n    fontFamily: '\"Open Sans\", \"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    h1: { fontFamily: '\"Merriweather\", serif' },\n    h2: { fontFamily: '\"Merriweather\", serif' },\n    h3: { fontFamily: '\"Merriweather\", serif' },\n    h4: { fontFamily: '\"Merriweather\", serif' },\n  },\n  shape: {\n    borderRadius: 12,\n  },\n});\n\n// Admin Theme\nconst adminTheme = createTheme({\n  palette: {\n    mode: 'light',\n    primary: {\n      main: '#1976d2',\n      light: '#42a5f5',\n      dark: '#1565c0',\n      contrastText: '#ffffff',\n    },\n    secondary: {\n      main: '#dc004e',\n      light: '#ff5983',\n      dark: '#9a0036',\n      contrastText: '#ffffff',\n    },\n    background: {\n      default: '#f5f5f5',\n      paper: '#ffffff',\n    },\n    text: {\n      primary: '#212121',\n      secondary: '#757575',\n    },\n  },\n  typography: {\n    fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n  },\n  shape: {\n    borderRadius: 8,\n  },\n  components: {\n    MuiDrawer: {\n      styleOverrides: {\n        paper: {\n          backgroundColor: '#ffffff',\n          borderRight: '1px solid #e0e0e0',\n        },\n      },\n    },\n    MuiAppBar: {\n      styleOverrides: {\n        root: {\n          backgroundColor: '#ffffff',\n          color: '#212121',\n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n        },\n      },\n    },\n  },\n});\n\n// Dark Admin Theme\nconst adminDarkTheme = createTheme({\n  palette: {\n    mode: 'dark',\n    primary: {\n      main: '#90caf9',\n      light: '#e3f2fd',\n      dark: '#42a5f5',\n      contrastText: '#000000',\n    },\n    secondary: {\n      main: '#f48fb1',\n      light: '#fce4ec',\n      dark: '#ad1457',\n      contrastText: '#000000',\n    },\n    background: {\n      default: '#121212',\n      paper: '#1e1e1e',\n    },\n    text: {\n      primary: '#ffffff',\n      secondary: '#b0b0b0',\n    },\n  },\n  typography: {\n    fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n  },\n  shape: {\n    borderRadius: 8,\n  },\n  components: {\n    MuiDrawer: {\n      styleOverrides: {\n        paper: {\n          backgroundColor: '#1e1e1e',\n          borderRight: '1px solid #333333',\n        },\n      },\n    },\n    MuiAppBar: {\n      styleOverrides: {\n        root: {\n          backgroundColor: '#1e1e1e',\n          color: '#ffffff',\n          boxShadow: '0 2px 8px rgba(0,0,0,0.3)',\n        },\n      },\n    },\n  },\n});\n\nexport const ThemeProvider = ({ children }) => {\n  const [isDarkMode, setIsDarkMode] = useState(false);\n  const location = useLocation();\n  \n  // Determine if we're in admin area\n  const isAdminArea = location.pathname.startsWith('/admin');\n  \n  useEffect(() => {\n    // Load theme preference from localStorage\n    const savedTheme = localStorage.getItem('themeMode');\n    if (savedTheme === 'dark') {\n      setIsDarkMode(true);\n    }\n  }, []);\n\n  const toggleTheme = () => {\n    const newMode = !isDarkMode;\n    setIsDarkMode(newMode);\n    localStorage.setItem('themeMode', newMode ? 'dark' : 'light');\n  };\n\n  // Select appropriate theme based on area and mode\n  const getTheme = () => {\n    if (isAdminArea) {\n      return isDarkMode ? adminDarkTheme : adminTheme;\n    }\n    return customerTheme; // Customer area always uses light theme for now\n  };\n\n  const value = {\n    isDarkMode,\n    toggleTheme,\n    isAdminArea,\n    theme: getTheme(),\n  };\n\n  return (\n    <ThemeContext.Provider value={value}>\n      <MuiThemeProvider theme={getTheme()}>\n        {children}\n      </MuiThemeProvider>\n    </ThemeContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,WAAW,EAAEC,aAAa,IAAIC,gBAAgB,QAAQ,sBAAsB;AACrF,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,YAAY,gBAAGV,aAAa,CAAC,CAAC;AAEpC,OAAO,MAAMW,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,OAAO,GAAGZ,UAAU,CAACS,YAAY,CAAC;EACxC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,8CAA8C,CAAC;EACjE;EACA,OAAOD,OAAO;AAChB,CAAC;;AAED;AAAAD,EAAA,CARaD,QAAQ;AASrB,MAAMI,aAAa,GAAGX,WAAW,CAAC;EAChCY,OAAO,EAAE;IACPC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE;MACPC,IAAI,EAAE,SAAS;MAAE;MACjBC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,SAAS;MACfC,YAAY,EAAE;IAChB,CAAC;IACDC,SAAS,EAAE;MACTJ,IAAI,EAAE,SAAS;MAAE;MACjBC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,SAAS;MACfC,YAAY,EAAE;IAChB,CAAC;IACDE,MAAM,EAAE;MACNL,IAAI,EAAE,SAAS;MAAE;MACjBC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDI,UAAU,EAAE;MACVC,OAAO,EAAE,SAAS;MAAE;MACpBC,KAAK,EAAE,SAAS;MAChBJ,SAAS,EAAE,SAAS;MAAE;MACtBK,QAAQ,EAAE,SAAS,CAAE;IACvB,CAAC;IACDC,IAAI,EAAE;MACJX,OAAO,EAAE,SAAS;MAClBK,SAAS,EAAE;IACb;EACF,CAAC;EACDO,UAAU,EAAE;IACVC,UAAU,EAAE,yDAAyD;IACrEC,EAAE,EAAE;MAAED,UAAU,EAAE;IAAwB,CAAC;IAC3CE,EAAE,EAAE;MAAEF,UAAU,EAAE;IAAwB,CAAC;IAC3CG,EAAE,EAAE;MAAEH,UAAU,EAAE;IAAwB,CAAC;IAC3CI,EAAE,EAAE;MAAEJ,UAAU,EAAE;IAAwB;EAC5C,CAAC;EACDK,KAAK,EAAE;IACLC,YAAY,EAAE;EAChB;AACF,CAAC,CAAC;;AAEF;AACA,MAAMC,UAAU,GAAGlC,WAAW,CAAC;EAC7BY,OAAO,EAAE;IACPC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE;MACPC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,SAAS;MACfC,YAAY,EAAE;IAChB,CAAC;IACDC,SAAS,EAAE;MACTJ,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,SAAS;MACfC,YAAY,EAAE;IAChB,CAAC;IACDG,UAAU,EAAE;MACVC,OAAO,EAAE,SAAS;MAClBC,KAAK,EAAE;IACT,CAAC;IACDE,IAAI,EAAE;MACJX,OAAO,EAAE,SAAS;MAClBK,SAAS,EAAE;IACb;EACF,CAAC;EACDO,UAAU,EAAE;IACVC,UAAU,EAAE;EACd,CAAC;EACDK,KAAK,EAAE;IACLC,YAAY,EAAE;EAChB,CAAC;EACDE,UAAU,EAAE;IACVC,SAAS,EAAE;MACTC,cAAc,EAAE;QACdd,KAAK,EAAE;UACLe,eAAe,EAAE,SAAS;UAC1BC,WAAW,EAAE;QACf;MACF;IACF,CAAC;IACDC,SAAS,EAAE;MACTH,cAAc,EAAE;QACdI,IAAI,EAAE;UACJH,eAAe,EAAE,SAAS;UAC1BI,KAAK,EAAE,SAAS;UAChBC,SAAS,EAAE;QACb;MACF;IACF;EACF;AACF,CAAC,CAAC;;AAEF;AACA,MAAMC,cAAc,GAAG5C,WAAW,CAAC;EACjCY,OAAO,EAAE;IACPC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE;MACPC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,SAAS;MACfC,YAAY,EAAE;IAChB,CAAC;IACDC,SAAS,EAAE;MACTJ,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,SAAS;MACfC,YAAY,EAAE;IAChB,CAAC;IACDG,UAAU,EAAE;MACVC,OAAO,EAAE,SAAS;MAClBC,KAAK,EAAE;IACT,CAAC;IACDE,IAAI,EAAE;MACJX,OAAO,EAAE,SAAS;MAClBK,SAAS,EAAE;IACb;EACF,CAAC;EACDO,UAAU,EAAE;IACVC,UAAU,EAAE;EACd,CAAC;EACDK,KAAK,EAAE;IACLC,YAAY,EAAE;EAChB,CAAC;EACDE,UAAU,EAAE;IACVC,SAAS,EAAE;MACTC,cAAc,EAAE;QACdd,KAAK,EAAE;UACLe,eAAe,EAAE,SAAS;UAC1BC,WAAW,EAAE;QACf;MACF;IACF,CAAC;IACDC,SAAS,EAAE;MACTH,cAAc,EAAE;QACdI,IAAI,EAAE;UACJH,eAAe,EAAE,SAAS;UAC1BI,KAAK,EAAE,SAAS;UAChBC,SAAS,EAAE;QACb;MACF;IACF;EACF;AACF,CAAC,CAAC;AAEF,OAAO,MAAM1C,aAAa,GAAGA,CAAC;EAAE4C;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC7C,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMmD,QAAQ,GAAG9C,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM+C,WAAW,GAAGD,QAAQ,CAACE,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAAC;EAE1DrD,SAAS,CAAC,MAAM;IACd;IACA,MAAMsD,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACpD,IAAIF,UAAU,KAAK,MAAM,EAAE;MACzBL,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,OAAO,GAAG,CAACV,UAAU;IAC3BC,aAAa,CAACS,OAAO,CAAC;IACtBH,YAAY,CAACI,OAAO,CAAC,WAAW,EAAED,OAAO,GAAG,MAAM,GAAG,OAAO,CAAC;EAC/D,CAAC;;EAED;EACA,MAAME,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAIT,WAAW,EAAE;MACf,OAAOH,UAAU,GAAGH,cAAc,GAAGV,UAAU;IACjD;IACA,OAAOvB,aAAa,CAAC,CAAC;EACxB,CAAC;EAED,MAAMiD,KAAK,GAAG;IACZb,UAAU;IACVS,WAAW;IACXN,WAAW;IACXW,KAAK,EAAEF,QAAQ,CAAC;EAClB,CAAC;EAED,oBACEtD,OAAA,CAACC,YAAY,CAACwD,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAAf,QAAA,eAClCxC,OAAA,CAACH,gBAAgB;MAAC2D,KAAK,EAAEF,QAAQ,CAAC,CAAE;MAAAd,QAAA,EACjCA;IAAQ;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAE5B,CAAC;AAACpB,GAAA,CA3CW7C,aAAa;EAAA,QAEPE,WAAW;AAAA;AAAAgE,EAAA,GAFjBlE,aAAa;AAAA,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}