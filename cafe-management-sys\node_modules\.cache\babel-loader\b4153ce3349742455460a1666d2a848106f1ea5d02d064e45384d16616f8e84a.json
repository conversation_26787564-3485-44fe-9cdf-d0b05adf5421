{"ast": null, "code": "var _jsxFileName = \"D:\\\\Github_Repos\\\\cafe-mangement-system-react\\\\cafe-management-sys\\\\src\\\\pages\\\\admin\\\\AdminDashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Grid, Card, CardContent, Typography, Button, Chip, Paper, Avatar, IconButton, LinearProgress, Fade, Grow, useTheme, alpha } from '@mui/material';\nimport { TrendingUp, TrendingDown, ShoppingCart, People, AttachMoney, Inventory, Add, Coffee, PersonAdd, BarChart, Schedule, Warning, CheckCircle, Info, MoreVert } from '@mui/icons-material';\nimport { styled } from '@mui/material/styles';\nimport AdminLayout from './AdminLayout';\nimport { analyticsAPI, ordersAPI, menuAPI } from '../../services/api';\n\n// Styled components for enhanced UI\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StatsCard = styled(Card)(({\n  theme,\n  color = 'primary'\n}) => ({\n  position: 'relative',\n  overflow: 'visible',\n  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n  border: `1px solid ${alpha(theme.palette[color].main, 0.1)}`,\n  '&:hover': {\n    transform: 'translateY(-4px)',\n    boxShadow: `0 12px 24px ${alpha(theme.palette[color].main, 0.15)}`,\n    '& .stats-icon': {\n      transform: 'scale(1.1) rotate(5deg)'\n    }\n  },\n  '&::before': {\n    content: '\"\"',\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    right: 0,\n    height: 4,\n    background: `linear-gradient(90deg, ${theme.palette[color].main}, ${theme.palette[color].light})`,\n    borderRadius: '4px 4px 0 0'\n  }\n}));\n_c = StatsCard;\nconst IconContainer = styled(Box)(({\n  theme,\n  color = 'primary'\n}) => ({\n  width: 60,\n  height: 60,\n  borderRadius: 16,\n  display: 'flex',\n  alignItems: 'center',\n  justifyContent: 'center',\n  background: `linear-gradient(135deg, ${alpha(theme.palette[color].main, 0.1)}, ${alpha(theme.palette[color].main, 0.05)})`,\n  transition: 'all 0.3s ease'\n}));\n_c2 = IconContainer;\nconst QuickActionCard = styled(Card)(({\n  theme\n}) => ({\n  cursor: 'pointer',\n  transition: 'all 0.2s ease',\n  border: `1px solid ${theme.palette.divider}`,\n  '&:hover': {\n    transform: 'translateY(-2px)',\n    boxShadow: theme.shadows[8],\n    borderColor: theme.palette.primary.main\n  }\n}));\n_c3 = QuickActionCard;\nconst AlertCard = styled(Paper)(({\n  theme,\n  severity = 'info'\n}) => {\n  const colors = {\n    error: theme.palette.error,\n    warning: theme.palette.warning,\n    info: theme.palette.info,\n    success: theme.palette.success\n  };\n  return {\n    padding: theme.spacing(2),\n    border: `1px solid ${alpha(colors[severity].main, 0.2)}`,\n    backgroundColor: alpha(colors[severity].main, 0.05),\n    borderRadius: 12,\n    transition: 'all 0.2s ease',\n    '&:hover': {\n      backgroundColor: alpha(colors[severity].main, 0.1),\n      transform: 'translateX(4px)'\n    }\n  };\n});\n\n// Mock data\n_c4 = AlertCard;\nconst statsData = [{\n  title: 'Total Revenue',\n  value: '$12,426',\n  change: '+12.5%',\n  trend: 'up',\n  icon: AttachMoney,\n  color: 'success',\n  subtitle: 'vs last month'\n}, {\n  title: 'Orders Today',\n  value: '147',\n  change: '+8.2%',\n  trend: 'up',\n  icon: ShoppingCart,\n  color: 'primary',\n  subtitle: '12 pending'\n}, {\n  title: 'Menu Items',\n  value: '42',\n  change: '+3 new',\n  trend: 'up',\n  icon: Coffee,\n  color: 'info',\n  subtitle: 'active items'\n}, {\n  title: 'Inventory Items',\n  value: '156',\n  change: '3 low stock',\n  trend: 'warning',\n  icon: Inventory,\n  color: 'error',\n  subtitle: 'needs attention'\n}];\nconst quickActions = [{\n  title: 'Add Menu Item',\n  icon: Coffee,\n  color: 'primary'\n}, {\n  title: 'Process Orders',\n  icon: ShoppingCart,\n  color: 'secondary'\n}, {\n  title: 'View Reports',\n  icon: BarChart,\n  color: 'info'\n}, {\n  title: 'Manage Inventory',\n  icon: Inventory,\n  color: 'warning'\n}];\nconst alerts = [{\n  id: 1,\n  title: 'Low Stock Alert',\n  message: 'Coffee beans running low (5 lbs left)',\n  severity: 'error',\n  time: '5 min ago',\n  action: 'Reorder'\n}, {\n  id: 2,\n  title: 'New Orders',\n  message: '3 new orders waiting for confirmation',\n  severity: 'warning',\n  time: '10 min ago',\n  action: 'View Orders'\n}, {\n  id: 3,\n  title: 'Equipment Maintenance',\n  message: 'Espresso machine maintenance scheduled',\n  severity: 'info',\n  time: '1 hour ago',\n  action: 'Schedule'\n}];\nexport default function AdminDashboard() {\n  _s();\n  var _dashboardData$totalR, _dashboardData$orders, _dashboardData$menuIt, _dashboardData$invent;\n  const theme = useTheme();\n  const [loading, setLoading] = useState(true);\n  const [dashboardData, setDashboardData] = useState(null);\n  const [recentOrders, setRecentOrders] = useState([]);\n  const [lastUpdated, setLastUpdated] = useState(new Date());\n  useEffect(() => {\n    fetchDashboardData();\n    // Set up auto-refresh every 30 seconds\n    const interval = setInterval(fetchDashboardData, 30000);\n    return () => clearInterval(interval);\n  }, []);\n  const fetchDashboardData = async () => {\n    try {\n      setLoading(true);\n      const [stats, orders] = await Promise.all([analyticsAPI.getDashboardStats(), ordersAPI.getRecent(5)]);\n      setDashboardData(stats);\n      setRecentOrders(orders);\n      setLastUpdated(new Date());\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'completed':\n        return 'success';\n      case 'preparing':\n        return 'warning';\n      case 'pending':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  const getSeverityIcon = severity => {\n    switch (severity) {\n      case 'error':\n        return /*#__PURE__*/_jsxDEV(Warning, {\n          color: \"error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 28\n        }, this);\n      case 'warning':\n        return /*#__PURE__*/_jsxDEV(Info, {\n          color: \"warning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 30\n        }, this);\n      case 'info':\n        return /*#__PURE__*/_jsxDEV(CheckCircle, {\n          color: \"info\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 27\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Info, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 23\n        }, this);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: '100%',\n        mt: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(LinearProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 9\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      flexGrow: 1\n    },\n    children: [/*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: (dashboardData ? [{\n        title: 'Total Revenue',\n        value: `$${((_dashboardData$totalR = dashboardData.totalRevenue) === null || _dashboardData$totalR === void 0 ? void 0 : _dashboardData$totalR.toLocaleString()) || '0'}`,\n        change: `${dashboardData.revenueChange > 0 ? '+' : ''}${dashboardData.revenueChange || 0}%`,\n        trend: dashboardData.revenueChange > 0 ? 'up' : 'down',\n        icon: AttachMoney,\n        color: 'success'\n      }, {\n        title: 'Orders Today',\n        value: ((_dashboardData$orders = dashboardData.ordersToday) === null || _dashboardData$orders === void 0 ? void 0 : _dashboardData$orders.toLocaleString()) || '0',\n        change: `${dashboardData.ordersChange > 0 ? '+' : ''}${dashboardData.ordersChange || 0}%`,\n        trend: dashboardData.ordersChange > 0 ? 'up' : 'down',\n        icon: ShoppingCart,\n        color: 'primary'\n      }, {\n        title: 'Menu Items',\n        value: ((_dashboardData$menuIt = dashboardData.menuItems) === null || _dashboardData$menuIt === void 0 ? void 0 : _dashboardData$menuIt.toLocaleString()) || '0',\n        change: '0%',\n        trend: 'neutral',\n        icon: Coffee,\n        color: 'info'\n      }, {\n        title: 'Inventory Items',\n        value: ((_dashboardData$invent = dashboardData.inventoryItems) === null || _dashboardData$invent === void 0 ? void 0 : _dashboardData$invent.toLocaleString()) || '0',\n        change: dashboardData.lowStockItems ? `-${dashboardData.lowStockItems}` : '0',\n        trend: dashboardData.lowStockItems > 0 ? 'down' : 'neutral',\n        icon: Inventory,\n        color: 'warning'\n      }] : statsData).map((stat, index) => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        lg: 3,\n        children: /*#__PURE__*/_jsxDEV(Grow, {\n          in: !loading,\n          timeout: 500 + index * 100,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(StatsCard, {\n              color: stat.color,\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 3\n                },\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'flex-start',\n                    justifyContent: 'space-between'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      gutterBottom: true,\n                      children: stat.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 292,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h4\",\n                      component: \"h2\",\n                      sx: {\n                        fontWeight: 700,\n                        mb: 1\n                      },\n                      children: stat.value\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 295,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Chip, {\n                        label: stat.change,\n                        size: \"small\",\n                        color: stat.trend === 'up' ? 'success' : stat.trend === 'down' ? 'error' : 'warning',\n                        icon: stat.trend === 'up' ? /*#__PURE__*/_jsxDEV(TrendingUp, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 303,\n                          columnNumber: 59\n                        }, this) : stat.trend === 'down' ? /*#__PURE__*/_jsxDEV(TrendingDown, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 303,\n                          columnNumber: 100\n                        }, this) : /*#__PURE__*/_jsxDEV(Warning, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 303,\n                          columnNumber: 119\n                        }, this),\n                        sx: {\n                          fontSize: '0.75rem'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 299,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: stat.subtitle\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 306,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 298,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 291,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(IconContainer, {\n                    color: stat.color,\n                    children: /*#__PURE__*/_jsxDEV(stat.icon, {\n                      className: \"stats-icon\",\n                      sx: {\n                        fontSize: 28,\n                        color: `${stat.color}.main`,\n                        transition: 'all 0.3s ease'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 312,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 311,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 15\n        }, this)\n      }, stat.title, false, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(Fade, {\n          in: !loading,\n          timeout: 800,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              height: '100%'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                p: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between',\n                  mb: 3\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  sx: {\n                    fontWeight: 600\n                  },\n                  children: \"Recent Orders\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  size: \"small\",\n                  color: \"primary\",\n                  children: \"View All\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: 2\n                },\n                children: (recentOrders.length > 0 ? recentOrders : [{\n                  _id: '1',\n                  orderNumber: '#1234',\n                  customer: {\n                    name: 'John Doe'\n                  },\n                  items: [{\n                    name: 'Latte'\n                  }, {\n                    name: 'Croissant'\n                  }],\n                  total: 12.50,\n                  status: 'completed',\n                  createdAt: new Date(Date.now() - 120000).toISOString()\n                }, {\n                  _id: '2',\n                  orderNumber: '#1235',\n                  customer: {\n                    name: 'Jane Smith'\n                  },\n                  items: [{\n                    name: 'Cappuccino'\n                  }, {\n                    name: 'Muffin'\n                  }],\n                  total: 8.75,\n                  status: 'preparing',\n                  createdAt: new Date(Date.now() - 300000).toISOString()\n                }]).map((order, index) => {\n                  var _order$customer, _order$items;\n                  return /*#__PURE__*/_jsxDEV(Grow, {\n                    in: !loading,\n                    timeout: 1000 + index * 100,\n                    children: /*#__PURE__*/_jsxDEV(Paper, {\n                      sx: {\n                        p: 2,\n                        border: '1px solid',\n                        borderColor: 'divider',\n                        borderRadius: 2,\n                        transition: 'all 0.2s ease',\n                        '&:hover': {\n                          borderColor: 'primary.main',\n                          transform: 'translateY(-1px)',\n                          boxShadow: 2\n                        }\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'space-between'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            flex: 1\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"subtitle2\",\n                            sx: {\n                              fontWeight: 600\n                            },\n                            children: [order.orderNumber || order.id, \" - \", ((_order$customer = order.customer) === null || _order$customer === void 0 ? void 0 : _order$customer.name) || order.customer]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 367,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"body2\",\n                            color: \"text.secondary\",\n                            children: ((_order$items = order.items) === null || _order$items === void 0 ? void 0 : _order$items.map(item => item.name).join(', ')) || order.items\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 370,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"caption\",\n                            color: \"text.secondary\",\n                            children: order.createdAt ? `${Math.floor((Date.now() - new Date(order.createdAt).getTime()) / 60000)} min ago` : order.time\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 373,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 366,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: 2\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"h6\",\n                            sx: {\n                              fontWeight: 600\n                            },\n                            children: typeof order.total === 'number' ? `$${order.total.toFixed(2)}` : order.total\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 380,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                            label: order.status,\n                            size: \"small\",\n                            color: getStatusColor(order.status),\n                            sx: {\n                              textTransform: 'capitalize'\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 383,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 379,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 365,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 351,\n                      columnNumber: 25\n                    }, this)\n                  }, order._id || order.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 350,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: 3,\n            height: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Fade, {\n            in: !loading,\n            timeout: 1000,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 3\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 2,\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Add, {\n                    sx: {\n                      mr: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 408,\n                    columnNumber: 23\n                  }, this), \"Quick Actions\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 2,\n                  children: quickActions.map((action, index) => /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Grow, {\n                      in: !loading,\n                      timeout: 1200 + index * 100,\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: /*#__PURE__*/_jsxDEV(QuickActionCard, {\n                          sx: {\n                            textAlign: 'center',\n                            p: 2\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(IconContainer, {\n                            color: action.color,\n                            sx: {\n                              width: 48,\n                              height: 48,\n                              mx: 'auto',\n                              mb: 1\n                            },\n                            children: /*#__PURE__*/_jsxDEV(action.icon, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 418,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 417,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"body2\",\n                            sx: {\n                              fontWeight: 500\n                            },\n                            children: action.title\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 420,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 416,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 415,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 414,\n                      columnNumber: 27\n                    }, this)\n                  }, action.title, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Fade, {\n            in: !loading,\n            timeout: 1400,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 3\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 2,\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Warning, {\n                    sx: {\n                      mr: 1,\n                      color: 'warning.main'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 438,\n                    columnNumber: 23\n                  }, this), \"Alerts\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    flexDirection: 'column',\n                    gap: 2\n                  },\n                  children: alerts.map((alert, index) => /*#__PURE__*/_jsxDEV(Grow, {\n                    in: !loading,\n                    timeout: 1600 + index * 100,\n                    children: /*#__PURE__*/_jsxDEV(AlertCard, {\n                      severity: alert.severity,\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'flex',\n                          alignItems: 'flex-start',\n                          justifyContent: 'space-between'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            display: 'flex',\n                            alignItems: 'flex-start',\n                            gap: 1,\n                            flex: 1\n                          },\n                          children: [getSeverityIcon(alert.severity), /*#__PURE__*/_jsxDEV(Box, {\n                            children: [/*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"subtitle2\",\n                              sx: {\n                                fontWeight: 600\n                              },\n                              children: alert.title\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 449,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"body2\",\n                              color: \"text.secondary\",\n                              sx: {\n                                mb: 1\n                              },\n                              children: alert.message\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 452,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"caption\",\n                              color: \"text.secondary\",\n                              children: alert.time\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 455,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 448,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 446,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(Button, {\n                          size: \"small\",\n                          variant: \"outlined\",\n                          children: alert.action\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 460,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 445,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 444,\n                      columnNumber: 27\n                    }, this)\n                  }, alert.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 443,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 331,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 248,\n    columnNumber: 7\n  }, this);\n}\n_s(AdminDashboard, \"OGDjh7jkSjNlKTpfmpzmdMJcSl4=\", false, function () {\n  return [useTheme];\n});\n_c5 = AdminDashboard;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"StatsCard\");\n$RefreshReg$(_c2, \"IconContainer\");\n$RefreshReg$(_c3, \"QuickActionCard\");\n$RefreshReg$(_c4, \"AlertCard\");\n$RefreshReg$(_c5, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Chip", "Paper", "Avatar", "IconButton", "LinearProgress", "Fade", "Grow", "useTheme", "alpha", "TrendingUp", "TrendingDown", "ShoppingCart", "People", "AttachMoney", "Inventory", "Add", "Coffee", "PersonAdd", "<PERSON><PERSON><PERSON>", "Schedule", "Warning", "CheckCircle", "Info", "<PERSON><PERSON><PERSON>", "styled", "AdminLayout", "analyticsAPI", "ordersAPI", "menuAPI", "jsxDEV", "_jsxDEV", "StatsCard", "theme", "color", "position", "overflow", "transition", "border", "palette", "main", "transform", "boxShadow", "content", "top", "left", "right", "height", "background", "light", "borderRadius", "_c", "IconContainer", "width", "display", "alignItems", "justifyContent", "_c2", "QuickActionCard", "cursor", "divider", "shadows", "borderColor", "primary", "_c3", "AlertCard", "severity", "colors", "error", "warning", "info", "success", "padding", "spacing", "backgroundColor", "_c4", "statsData", "title", "value", "change", "trend", "icon", "subtitle", "quickActions", "alerts", "id", "message", "time", "action", "AdminDashboard", "_s", "_dashboardData$totalR", "_dashboardData$orders", "_dashboardData$menuIt", "_dashboardData$invent", "loading", "setLoading", "dashboardData", "setDashboardData", "recentOrders", "setRecentOrders", "lastUpdated", "setLastUpdated", "Date", "fetchDashboardData", "interval", "setInterval", "clearInterval", "stats", "orders", "Promise", "all", "getDashboardStats", "getRecent", "console", "getStatusColor", "status", "getSeverityIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "mt", "children", "flexGrow", "container", "mb", "totalRevenue", "toLocaleString", "revenueChange", "ordersToday", "ordersChange", "menuItems", "inventoryItems", "lowStockItems", "map", "stat", "index", "item", "xs", "sm", "lg", "in", "timeout", "p", "flex", "variant", "gutterBottom", "component", "fontWeight", "gap", "label", "size", "fontSize", "className", "flexDirection", "length", "_id", "orderNumber", "customer", "name", "items", "total", "createdAt", "now", "toISOString", "order", "_order$customer", "_order$items", "join", "Math", "floor", "getTime", "toFixed", "textTransform", "mr", "textAlign", "mx", "alert", "_c5", "$RefreshReg$"], "sources": ["D:/Github_Repos/cafe-mangement-system-react/cafe-management-sys/src/pages/admin/AdminDashboard.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport {\r\n  Box,\r\n  Grid,\r\n  Card,\r\n  CardContent,\r\n  Typography,\r\n  Button,\r\n  Chip,\r\n  Paper,\r\n  Avatar,\r\n  IconButton,\r\n  LinearProgress,\r\n  Fade,\r\n  Grow,\r\n  useTheme,\r\n  alpha,\r\n} from '@mui/material';\r\nimport {\r\n  TrendingUp,\r\n  TrendingDown,\r\n  ShoppingCart,\r\n  People,\r\n  AttachMoney,\r\n  Inventory,\r\n  Add,\r\n  Coffee,\r\n  PersonAdd,\r\n  BarChart,\r\n  Schedule,\r\n  Warning,\r\n  CheckCircle,\r\n  Info,\r\n  MoreVert,\r\n} from '@mui/icons-material';\r\nimport { styled } from '@mui/material/styles';\r\nimport AdminLayout from './AdminLayout';\r\nimport { analyticsAPI, ordersAPI, menuAPI } from '../../services/api';\r\n\r\n// Styled components for enhanced UI\r\nconst StatsCard = styled(Card)(({ theme, color = 'primary' }) => ({\r\n  position: 'relative',\r\n  overflow: 'visible',\r\n  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\r\n  border: `1px solid ${alpha(theme.palette[color].main, 0.1)}`,\r\n  \r\n  '&:hover': {\r\n    transform: 'translateY(-4px)',\r\n    boxShadow: `0 12px 24px ${alpha(theme.palette[color].main, 0.15)}`,\r\n    \r\n    '& .stats-icon': {\r\n      transform: 'scale(1.1) rotate(5deg)',\r\n    },\r\n  },\r\n  \r\n  '&::before': {\r\n    content: '\"\"',\r\n    position: 'absolute',\r\n    top: 0,\r\n    left: 0,\r\n    right: 0,\r\n    height: 4,\r\n    background: `linear-gradient(90deg, ${theme.palette[color].main}, ${theme.palette[color].light})`,\r\n    borderRadius: '4px 4px 0 0',\r\n  },\r\n}));\r\n\r\nconst IconContainer = styled(Box)(({ theme, color = 'primary' }) => ({\r\n  width: 60,\r\n  height: 60,\r\n  borderRadius: 16,\r\n  display: 'flex',\r\n  alignItems: 'center',\r\n  justifyContent: 'center',\r\n  background: `linear-gradient(135deg, ${alpha(theme.palette[color].main, 0.1)}, ${alpha(theme.palette[color].main, 0.05)})`,\r\n  transition: 'all 0.3s ease',\r\n}));\r\n\r\nconst QuickActionCard = styled(Card)(({ theme }) => ({\r\n  cursor: 'pointer',\r\n  transition: 'all 0.2s ease',\r\n  border: `1px solid ${theme.palette.divider}`,\r\n  \r\n  '&:hover': {\r\n    transform: 'translateY(-2px)',\r\n    boxShadow: theme.shadows[8],\r\n    borderColor: theme.palette.primary.main,\r\n  },\r\n}));\r\n\r\nconst AlertCard = styled(Paper)(({ theme, severity = 'info' }) => {\r\n  const colors = {\r\n    error: theme.palette.error,\r\n    warning: theme.palette.warning,\r\n    info: theme.palette.info,\r\n    success: theme.palette.success,\r\n  };\r\n  \r\n  return {\r\n    padding: theme.spacing(2),\r\n    border: `1px solid ${alpha(colors[severity].main, 0.2)}`,\r\n    backgroundColor: alpha(colors[severity].main, 0.05),\r\n    borderRadius: 12,\r\n    transition: 'all 0.2s ease',\r\n    \r\n    '&:hover': {\r\n      backgroundColor: alpha(colors[severity].main, 0.1),\r\n      transform: 'translateX(4px)',\r\n    },\r\n  };\r\n});\r\n\r\n// Mock data\r\nconst statsData = [\r\n  {\r\n    title: 'Total Revenue',\r\n    value: '$12,426',\r\n    change: '+12.5%',\r\n    trend: 'up',\r\n    icon: AttachMoney,\r\n    color: 'success',\r\n    subtitle: 'vs last month',\r\n  },\r\n  {\r\n    title: 'Orders Today',\r\n    value: '147',\r\n    change: '+8.2%',\r\n    trend: 'up',\r\n    icon: ShoppingCart,\r\n    color: 'primary',\r\n    subtitle: '12 pending',\r\n  },\r\n  {\r\n    title: 'Menu Items',\r\n    value: '42',\r\n    change: '+3 new',\r\n    trend: 'up',\r\n    icon: Coffee,\r\n    color: 'info',\r\n    subtitle: 'active items',\r\n  },\r\n  {\r\n    title: 'Inventory Items',\r\n    value: '156',\r\n    change: '3 low stock',\r\n    trend: 'warning',\r\n    icon: Inventory,\r\n    color: 'error',\r\n    subtitle: 'needs attention',\r\n  },\r\n];\r\n\r\nconst quickActions = [\r\n  { title: 'Add Menu Item', icon: Coffee, color: 'primary' },\r\n  { title: 'Process Orders', icon: ShoppingCart, color: 'secondary' },\r\n  { title: 'View Reports', icon: BarChart, color: 'info' },\r\n  { title: 'Manage Inventory', icon: Inventory, color: 'warning' },\r\n];\r\n\r\nconst alerts = [\r\n  {\r\n    id: 1,\r\n    title: 'Low Stock Alert',\r\n    message: 'Coffee beans running low (5 lbs left)',\r\n    severity: 'error',\r\n    time: '5 min ago',\r\n    action: 'Reorder',\r\n  },\r\n  {\r\n    id: 2,\r\n    title: 'New Orders',\r\n    message: '3 new orders waiting for confirmation',\r\n    severity: 'warning',\r\n    time: '10 min ago',\r\n    action: 'View Orders',\r\n  },\r\n  {\r\n    id: 3,\r\n    title: 'Equipment Maintenance',\r\n    message: 'Espresso machine maintenance scheduled',\r\n    severity: 'info',\r\n    time: '1 hour ago',\r\n    action: 'Schedule',\r\n  },\r\n];\r\n\r\n\r\n\r\nexport default function AdminDashboard() {\r\n  const theme = useTheme();\r\n  const [loading, setLoading] = useState(true);\r\n  const [dashboardData, setDashboardData] = useState(null);\r\n  const [recentOrders, setRecentOrders] = useState([]);\r\n  const [lastUpdated, setLastUpdated] = useState(new Date());\r\n\r\n  useEffect(() => {\r\n    fetchDashboardData();\r\n    // Set up auto-refresh every 30 seconds\r\n    const interval = setInterval(fetchDashboardData, 30000);\r\n    return () => clearInterval(interval);\r\n  }, []);\r\n\r\n  const fetchDashboardData = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const [stats, orders] = await Promise.all([\r\n        analyticsAPI.getDashboardStats(),\r\n        ordersAPI.getRecent(5),\r\n      ]);\r\n\r\n      setDashboardData(stats);\r\n      setRecentOrders(orders);\r\n      setLastUpdated(new Date());\r\n    } catch (error) {\r\n      console.error('Error fetching dashboard data:', error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const getStatusColor = (status) => {\r\n    switch (status) {\r\n      case 'completed': return 'success';\r\n      case 'preparing': return 'warning';\r\n      case 'pending': return 'error';\r\n      default: return 'default';\r\n    }\r\n  };\r\n\r\n  const getSeverityIcon = (severity) => {\r\n    switch (severity) {\r\n      case 'error': return <Warning color=\"error\" />;\r\n      case 'warning': return <Info color=\"warning\" />;\r\n      case 'info': return <CheckCircle color=\"info\" />;\r\n      default: return <Info />;\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n        <Box sx={{ width: '100%', mt: 2 }}>\r\n          <LinearProgress />\r\n        </Box>\r\n    );\r\n  }\r\n\r\n  return (\r\n      <Box sx={{ flexGrow: 1 }}>\r\n        {/* Stats Cards */}\r\n        <Grid container spacing={3} sx={{ mb: 4 }}>\r\n          {(dashboardData ? [\r\n            {\r\n              title: 'Total Revenue',\r\n              value: `$${dashboardData.totalRevenue?.toLocaleString() || '0'}`,\r\n              change: `${dashboardData.revenueChange > 0 ? '+' : ''}${dashboardData.revenueChange || 0}%`,\r\n              trend: dashboardData.revenueChange > 0 ? 'up' : 'down',\r\n              icon: AttachMoney,\r\n              color: 'success'\r\n            },\r\n            {\r\n              title: 'Orders Today',\r\n              value: dashboardData.ordersToday?.toLocaleString() || '0',\r\n              change: `${dashboardData.ordersChange > 0 ? '+' : ''}${dashboardData.ordersChange || 0}%`,\r\n              trend: dashboardData.ordersChange > 0 ? 'up' : 'down',\r\n              icon: ShoppingCart,\r\n              color: 'primary'\r\n            },\r\n            {\r\n              title: 'Menu Items',\r\n              value: dashboardData.menuItems?.toLocaleString() || '0',\r\n              change: '0%',\r\n              trend: 'neutral',\r\n              icon: Coffee,\r\n              color: 'info'\r\n            },\r\n            {\r\n              title: 'Inventory Items',\r\n              value: dashboardData.inventoryItems?.toLocaleString() || '0',\r\n              change: dashboardData.lowStockItems ? `-${dashboardData.lowStockItems}` : '0',\r\n              trend: dashboardData.lowStockItems > 0 ? 'down' : 'neutral',\r\n              icon: Inventory,\r\n              color: 'warning'\r\n            }\r\n          ] : statsData).map((stat, index) => (\r\n            <Grid item xs={12} sm={6} lg={3} key={stat.title}>\r\n              <Grow in={!loading} timeout={500 + index * 100}>\r\n                <div>\r\n                  <StatsCard color={stat.color}>\r\n                    <CardContent sx={{ p: 3 }}>\r\n                      <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between' }}>\r\n                        <Box sx={{ flex: 1 }}>\r\n                          <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\r\n                            {stat.title}\r\n                          </Typography>\r\n                          <Typography variant=\"h4\" component=\"h2\" sx={{ fontWeight: 700, mb: 1 }}>\r\n                            {stat.value}\r\n                          </Typography>\r\n                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                            <Chip\r\n                              label={stat.change}\r\n                              size=\"small\"\r\n                              color={stat.trend === 'up' ? 'success' : stat.trend === 'down' ? 'error' : 'warning'}\r\n                              icon={stat.trend === 'up' ? <TrendingUp /> : stat.trend === 'down' ? <TrendingDown /> : <Warning />}\r\n                              sx={{ fontSize: '0.75rem' }}\r\n                            />\r\n                            <Typography variant=\"caption\" color=\"text.secondary\">\r\n                              {stat.subtitle}\r\n                            </Typography>\r\n                          </Box>\r\n                        </Box>\r\n                        <IconContainer color={stat.color}>\r\n                          <stat.icon \r\n                            className=\"stats-icon\"\r\n                            sx={{ \r\n                              fontSize: 28, \r\n                              color: `${stat.color}.main`,\r\n                              transition: 'all 0.3s ease',\r\n                            }} \r\n                          />\r\n                        </IconContainer>\r\n                      </Box>\r\n                    </CardContent>\r\n                  </StatsCard>\r\n                </div>\r\n              </Grow>\r\n            </Grid>\r\n          ))}\r\n        </Grid>\r\n\r\n        {/* Main Content Grid */}\r\n        <Grid container spacing={3}>\r\n          {/* Recent Orders */}\r\n          <Grid item xs={12} lg={8}>\r\n            <Fade in={!loading} timeout={800}>\r\n              <Card sx={{ height: '100%' }}>\r\n                <CardContent sx={{ p: 3 }}>\r\n                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>\r\n                    <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\r\n                      Recent Orders\r\n                    </Typography>\r\n                    <Button variant=\"outlined\" size=\"small\" color=\"primary\">\r\n                      View All\r\n                    </Button>\r\n                  </Box>\r\n                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>\r\n                    {(recentOrders.length > 0 ? recentOrders : [\r\n                      { _id: '1', orderNumber: '#1234', customer: { name: 'John Doe' }, items: [{ name: 'Latte' }, { name: 'Croissant' }], total: 12.50, status: 'completed', createdAt: new Date(Date.now() - 120000).toISOString() },\r\n                      { _id: '2', orderNumber: '#1235', customer: { name: 'Jane Smith' }, items: [{ name: 'Cappuccino' }, { name: 'Muffin' }], total: 8.75, status: 'preparing', createdAt: new Date(Date.now() - 300000).toISOString() },\r\n                    ]).map((order, index) => (\r\n                      <Grow in={!loading} timeout={1000 + index * 100} key={order._id || order.id}>\r\n                        <Paper\r\n                          sx={{\r\n                            p: 2,\r\n                            border: '1px solid',\r\n                            borderColor: 'divider',\r\n                            borderRadius: 2,\r\n                            transition: 'all 0.2s ease',\r\n                            '&:hover': {\r\n                              borderColor: 'primary.main',\r\n                              transform: 'translateY(-1px)',\r\n                              boxShadow: 2,\r\n                            }\r\n                          }}\r\n                        >\r\n                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\r\n                            <Box sx={{ flex: 1 }}>\r\n                              <Typography variant=\"subtitle2\" sx={{ fontWeight: 600 }}>\r\n                                {order.orderNumber || order.id} - {order.customer?.name || order.customer}\r\n                              </Typography>\r\n                              <Typography variant=\"body2\" color=\"text.secondary\">\r\n                                {order.items?.map(item => item.name).join(', ') || order.items}\r\n                              </Typography>\r\n                              <Typography variant=\"caption\" color=\"text.secondary\">\r\n                                {order.createdAt ?\r\n                                  `${Math.floor((Date.now() - new Date(order.createdAt).getTime()) / 60000)} min ago` :\r\n                                  order.time}\r\n                              </Typography>\r\n                            </Box>\r\n                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                              <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\r\n                                {typeof order.total === 'number' ? `$${order.total.toFixed(2)}` : order.total}\r\n                              </Typography>\r\n                              <Chip\r\n                                label={order.status}\r\n                                size=\"small\"\r\n                                color={getStatusColor(order.status)}\r\n                                sx={{ textTransform: 'capitalize' }}\r\n                              />\r\n                            </Box>\r\n                          </Box>\r\n                        </Paper>\r\n                      </Grow>\r\n                    ))}\r\n                  </Box>\r\n                </CardContent>\r\n              </Card>\r\n            </Fade>\r\n          </Grid>\r\n\r\n          {/* Quick Actions & Alerts */}\r\n          <Grid item xs={12} lg={4}>\r\n            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, height: '100%' }}>\r\n              {/* Quick Actions */}\r\n              <Fade in={!loading} timeout={1000}>\r\n                <Card>\r\n                  <CardContent sx={{ p: 3 }}>\r\n                    <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center' }}>\r\n                      <Add sx={{ mr: 1 }} />\r\n                      Quick Actions\r\n                    </Typography>\r\n                    <Grid container spacing={2}>\r\n                      {quickActions.map((action, index) => (\r\n                        <Grid item xs={6} key={action.title}>\r\n                          <Grow in={!loading} timeout={1200 + index * 100}>\r\n                            <div>\r\n                              <QuickActionCard sx={{ textAlign: 'center', p: 2 }}>\r\n                                <IconContainer color={action.color} sx={{ width: 48, height: 48, mx: 'auto', mb: 1 }}>\r\n                                  <action.icon />\r\n                                </IconContainer>\r\n                                <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\r\n                                  {action.title}\r\n                                </Typography>\r\n                              </QuickActionCard>\r\n                            </div>\r\n                          </Grow>\r\n                        </Grid>\r\n                      ))}\r\n                    </Grid>\r\n                  </CardContent>\r\n                </Card>\r\n              </Fade>\r\n\r\n              {/* Alerts */}\r\n              <Fade in={!loading} timeout={1400}>\r\n                <Card sx={{ flex: 1 }}>\r\n                  <CardContent sx={{ p: 3 }}>\r\n                    <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center' }}>\r\n                      <Warning sx={{ mr: 1, color: 'warning.main' }} />\r\n                      Alerts\r\n                    </Typography>\r\n                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>\r\n                      {alerts.map((alert, index) => (\r\n                        <Grow in={!loading} timeout={1600 + index * 100} key={alert.id}>\r\n                          <AlertCard severity={alert.severity}>\r\n                            <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between' }}>\r\n                              <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1, flex: 1 }}>\r\n                                {getSeverityIcon(alert.severity)}\r\n                                <Box>\r\n                                  <Typography variant=\"subtitle2\" sx={{ fontWeight: 600 }}>\r\n                                    {alert.title}\r\n                                  </Typography>\r\n                                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\r\n                                    {alert.message}\r\n                                  </Typography>\r\n                                  <Typography variant=\"caption\" color=\"text.secondary\">\r\n                                    {alert.time}\r\n                                  </Typography>\r\n                                </Box>\r\n                              </Box>\r\n                              <Button size=\"small\" variant=\"outlined\">\r\n                                {alert.action}\r\n                              </Button>\r\n                            </Box>\r\n                          </AlertCard>\r\n                        </Grow>\r\n                      ))}\r\n                    </Box>\r\n                  </CardContent>\r\n                </Card>\r\n              </Fade>\r\n            </Box>\r\n          </Grid>\r\n        </Grid>\r\n      </Box>\r\n  );\r\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,cAAc,EACdC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB,SACEC,UAAU,EACVC,YAAY,EACZC,YAAY,EACZC,MAAM,EACNC,WAAW,EACXC,SAAS,EACTC,GAAG,EACHC,MAAM,EACNC,SAAS,EACTC,QAAQ,EACRC,QAAQ,EACRC,OAAO,EACPC,WAAW,EACXC,IAAI,EACJC,QAAQ,QACH,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,YAAY,EAAEC,SAAS,EAAEC,OAAO,QAAQ,oBAAoB;;AAErE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,SAAS,GAAGP,MAAM,CAAC5B,IAAI,CAAC,CAAC,CAAC;EAAEoC,KAAK;EAAEC,KAAK,GAAG;AAAU,CAAC,MAAM;EAChEC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,SAAS;EACnBC,UAAU,EAAE,uCAAuC;EACnDC,MAAM,EAAE,aAAa7B,KAAK,CAACwB,KAAK,CAACM,OAAO,CAACL,KAAK,CAAC,CAACM,IAAI,EAAE,GAAG,CAAC,EAAE;EAE5D,SAAS,EAAE;IACTC,SAAS,EAAE,kBAAkB;IAC7BC,SAAS,EAAE,eAAejC,KAAK,CAACwB,KAAK,CAACM,OAAO,CAACL,KAAK,CAAC,CAACM,IAAI,EAAE,IAAI,CAAC,EAAE;IAElE,eAAe,EAAE;MACfC,SAAS,EAAE;IACb;EACF,CAAC;EAED,WAAW,EAAE;IACXE,OAAO,EAAE,IAAI;IACbR,QAAQ,EAAE,UAAU;IACpBS,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,UAAU,EAAE,0BAA0Bf,KAAK,CAACM,OAAO,CAACL,KAAK,CAAC,CAACM,IAAI,KAAKP,KAAK,CAACM,OAAO,CAACL,KAAK,CAAC,CAACe,KAAK,GAAG;IACjGC,YAAY,EAAE;EAChB;AACF,CAAC,CAAC,CAAC;AAACC,EAAA,GAzBEnB,SAAS;AA2Bf,MAAMoB,aAAa,GAAG3B,MAAM,CAAC9B,GAAG,CAAC,CAAC,CAAC;EAAEsC,KAAK;EAAEC,KAAK,GAAG;AAAU,CAAC,MAAM;EACnEmB,KAAK,EAAE,EAAE;EACTN,MAAM,EAAE,EAAE;EACVG,YAAY,EAAE,EAAE;EAChBI,OAAO,EAAE,MAAM;EACfC,UAAU,EAAE,QAAQ;EACpBC,cAAc,EAAE,QAAQ;EACxBR,UAAU,EAAE,2BAA2BvC,KAAK,CAACwB,KAAK,CAACM,OAAO,CAACL,KAAK,CAAC,CAACM,IAAI,EAAE,GAAG,CAAC,KAAK/B,KAAK,CAACwB,KAAK,CAACM,OAAO,CAACL,KAAK,CAAC,CAACM,IAAI,EAAE,IAAI,CAAC,GAAG;EAC1HH,UAAU,EAAE;AACd,CAAC,CAAC,CAAC;AAACoB,GAAA,GATEL,aAAa;AAWnB,MAAMM,eAAe,GAAGjC,MAAM,CAAC5B,IAAI,CAAC,CAAC,CAAC;EAAEoC;AAAM,CAAC,MAAM;EACnD0B,MAAM,EAAE,SAAS;EACjBtB,UAAU,EAAE,eAAe;EAC3BC,MAAM,EAAE,aAAaL,KAAK,CAACM,OAAO,CAACqB,OAAO,EAAE;EAE5C,SAAS,EAAE;IACTnB,SAAS,EAAE,kBAAkB;IAC7BC,SAAS,EAAET,KAAK,CAAC4B,OAAO,CAAC,CAAC,CAAC;IAC3BC,WAAW,EAAE7B,KAAK,CAACM,OAAO,CAACwB,OAAO,CAACvB;EACrC;AACF,CAAC,CAAC,CAAC;AAACwB,GAAA,GAVEN,eAAe;AAYrB,MAAMO,SAAS,GAAGxC,MAAM,CAACvB,KAAK,CAAC,CAAC,CAAC;EAAE+B,KAAK;EAAEiC,QAAQ,GAAG;AAAO,CAAC,KAAK;EAChE,MAAMC,MAAM,GAAG;IACbC,KAAK,EAAEnC,KAAK,CAACM,OAAO,CAAC6B,KAAK;IAC1BC,OAAO,EAAEpC,KAAK,CAACM,OAAO,CAAC8B,OAAO;IAC9BC,IAAI,EAAErC,KAAK,CAACM,OAAO,CAAC+B,IAAI;IACxBC,OAAO,EAAEtC,KAAK,CAACM,OAAO,CAACgC;EACzB,CAAC;EAED,OAAO;IACLC,OAAO,EAAEvC,KAAK,CAACwC,OAAO,CAAC,CAAC,CAAC;IACzBnC,MAAM,EAAE,aAAa7B,KAAK,CAAC0D,MAAM,CAACD,QAAQ,CAAC,CAAC1B,IAAI,EAAE,GAAG,CAAC,EAAE;IACxDkC,eAAe,EAAEjE,KAAK,CAAC0D,MAAM,CAACD,QAAQ,CAAC,CAAC1B,IAAI,EAAE,IAAI,CAAC;IACnDU,YAAY,EAAE,EAAE;IAChBb,UAAU,EAAE,eAAe;IAE3B,SAAS,EAAE;MACTqC,eAAe,EAAEjE,KAAK,CAAC0D,MAAM,CAACD,QAAQ,CAAC,CAAC1B,IAAI,EAAE,GAAG,CAAC;MAClDC,SAAS,EAAE;IACb;EACF,CAAC;AACH,CAAC,CAAC;;AAEF;AAAAkC,GAAA,GAtBMV,SAAS;AAuBf,MAAMW,SAAS,GAAG,CAChB;EACEC,KAAK,EAAE,eAAe;EACtBC,KAAK,EAAE,SAAS;EAChBC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAEnE,WAAW;EACjBoB,KAAK,EAAE,SAAS;EAChBgD,QAAQ,EAAE;AACZ,CAAC,EACD;EACEL,KAAK,EAAE,cAAc;EACrBC,KAAK,EAAE,KAAK;EACZC,MAAM,EAAE,OAAO;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAErE,YAAY;EAClBsB,KAAK,EAAE,SAAS;EAChBgD,QAAQ,EAAE;AACZ,CAAC,EACD;EACEL,KAAK,EAAE,YAAY;EACnBC,KAAK,EAAE,IAAI;EACXC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAEhE,MAAM;EACZiB,KAAK,EAAE,MAAM;EACbgD,QAAQ,EAAE;AACZ,CAAC,EACD;EACEL,KAAK,EAAE,iBAAiB;EACxBC,KAAK,EAAE,KAAK;EACZC,MAAM,EAAE,aAAa;EACrBC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAElE,SAAS;EACfmB,KAAK,EAAE,OAAO;EACdgD,QAAQ,EAAE;AACZ,CAAC,CACF;AAED,MAAMC,YAAY,GAAG,CACnB;EAAEN,KAAK,EAAE,eAAe;EAAEI,IAAI,EAAEhE,MAAM;EAAEiB,KAAK,EAAE;AAAU,CAAC,EAC1D;EAAE2C,KAAK,EAAE,gBAAgB;EAAEI,IAAI,EAAErE,YAAY;EAAEsB,KAAK,EAAE;AAAY,CAAC,EACnE;EAAE2C,KAAK,EAAE,cAAc;EAAEI,IAAI,EAAE9D,QAAQ;EAAEe,KAAK,EAAE;AAAO,CAAC,EACxD;EAAE2C,KAAK,EAAE,kBAAkB;EAAEI,IAAI,EAAElE,SAAS;EAAEmB,KAAK,EAAE;AAAU,CAAC,CACjE;AAED,MAAMkD,MAAM,GAAG,CACb;EACEC,EAAE,EAAE,CAAC;EACLR,KAAK,EAAE,iBAAiB;EACxBS,OAAO,EAAE,uCAAuC;EAChDpB,QAAQ,EAAE,OAAO;EACjBqB,IAAI,EAAE,WAAW;EACjBC,MAAM,EAAE;AACV,CAAC,EACD;EACEH,EAAE,EAAE,CAAC;EACLR,KAAK,EAAE,YAAY;EACnBS,OAAO,EAAE,uCAAuC;EAChDpB,QAAQ,EAAE,SAAS;EACnBqB,IAAI,EAAE,YAAY;EAClBC,MAAM,EAAE;AACV,CAAC,EACD;EACEH,EAAE,EAAE,CAAC;EACLR,KAAK,EAAE,uBAAuB;EAC9BS,OAAO,EAAE,wCAAwC;EACjDpB,QAAQ,EAAE,MAAM;EAChBqB,IAAI,EAAE,YAAY;EAClBC,MAAM,EAAE;AACV,CAAC,CACF;AAID,eAAe,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EACvC,MAAM7D,KAAK,GAAGzB,QAAQ,CAAC,CAAC;EACxB,MAAM,CAACuF,OAAO,EAAEC,UAAU,CAAC,GAAGvG,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwG,aAAa,EAAEC,gBAAgB,CAAC,GAAGzG,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC0G,YAAY,EAAEC,eAAe,CAAC,GAAG3G,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC4G,WAAW,EAAEC,cAAc,CAAC,GAAG7G,QAAQ,CAAC,IAAI8G,IAAI,CAAC,CAAC,CAAC;EAE1D7G,SAAS,CAAC,MAAM;IACd8G,kBAAkB,CAAC,CAAC;IACpB;IACA,MAAMC,QAAQ,GAAGC,WAAW,CAACF,kBAAkB,EAAE,KAAK,CAAC;IACvD,OAAO,MAAMG,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFR,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM,CAACY,KAAK,EAAEC,MAAM,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACxCpF,YAAY,CAACqF,iBAAiB,CAAC,CAAC,EAChCpF,SAAS,CAACqF,SAAS,CAAC,CAAC,CAAC,CACvB,CAAC;MAEFf,gBAAgB,CAACU,KAAK,CAAC;MACvBR,eAAe,CAACS,MAAM,CAAC;MACvBP,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,OAAOnC,KAAK,EAAE;MACd8C,OAAO,CAAC9C,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD,CAAC,SAAS;MACR4B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmB,cAAc,GAAIC,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMC,eAAe,GAAInD,QAAQ,IAAK;IACpC,QAAQA,QAAQ;MACd,KAAK,OAAO;QAAE,oBAAOnC,OAAA,CAACV,OAAO;UAACa,KAAK,EAAC;QAAO;UAAAoF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9C,KAAK,SAAS;QAAE,oBAAO1F,OAAA,CAACR,IAAI;UAACW,KAAK,EAAC;QAAS;UAAAoF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/C,KAAK,MAAM;QAAE,oBAAO1F,OAAA,CAACT,WAAW;UAACY,KAAK,EAAC;QAAM;UAAAoF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAChD;QAAS,oBAAO1F,OAAA,CAACR,IAAI;UAAA+F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC1B;EACF,CAAC;EAED,IAAI1B,OAAO,EAAE;IACX,oBACIhE,OAAA,CAACpC,GAAG;MAAC+H,EAAE,EAAE;QAAErE,KAAK,EAAE,MAAM;QAAEsE,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,eAChC7F,OAAA,CAAC1B,cAAc;QAAAiH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC;EAEZ;EAEA,oBACI1F,OAAA,CAACpC,GAAG;IAAC+H,EAAE,EAAE;MAAEG,QAAQ,EAAE;IAAE,CAAE;IAAAD,QAAA,gBAEvB7F,OAAA,CAACnC,IAAI;MAACkI,SAAS;MAACrD,OAAO,EAAE,CAAE;MAACiD,EAAE,EAAE;QAAEK,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,EACvC,CAAC3B,aAAa,GAAG,CAChB;QACEpB,KAAK,EAAE,eAAe;QACtBC,KAAK,EAAE,IAAI,EAAAa,qBAAA,GAAAM,aAAa,CAAC+B,YAAY,cAAArC,qBAAA,uBAA1BA,qBAAA,CAA4BsC,cAAc,CAAC,CAAC,KAAI,GAAG,EAAE;QAChElD,MAAM,EAAE,GAAGkB,aAAa,CAACiC,aAAa,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAGjC,aAAa,CAACiC,aAAa,IAAI,CAAC,GAAG;QAC3FlD,KAAK,EAAEiB,aAAa,CAACiC,aAAa,GAAG,CAAC,GAAG,IAAI,GAAG,MAAM;QACtDjD,IAAI,EAAEnE,WAAW;QACjBoB,KAAK,EAAE;MACT,CAAC,EACD;QACE2C,KAAK,EAAE,cAAc;QACrBC,KAAK,EAAE,EAAAc,qBAAA,GAAAK,aAAa,CAACkC,WAAW,cAAAvC,qBAAA,uBAAzBA,qBAAA,CAA2BqC,cAAc,CAAC,CAAC,KAAI,GAAG;QACzDlD,MAAM,EAAE,GAAGkB,aAAa,CAACmC,YAAY,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAGnC,aAAa,CAACmC,YAAY,IAAI,CAAC,GAAG;QACzFpD,KAAK,EAAEiB,aAAa,CAACmC,YAAY,GAAG,CAAC,GAAG,IAAI,GAAG,MAAM;QACrDnD,IAAI,EAAErE,YAAY;QAClBsB,KAAK,EAAE;MACT,CAAC,EACD;QACE2C,KAAK,EAAE,YAAY;QACnBC,KAAK,EAAE,EAAAe,qBAAA,GAAAI,aAAa,CAACoC,SAAS,cAAAxC,qBAAA,uBAAvBA,qBAAA,CAAyBoC,cAAc,CAAC,CAAC,KAAI,GAAG;QACvDlD,MAAM,EAAE,IAAI;QACZC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAEhE,MAAM;QACZiB,KAAK,EAAE;MACT,CAAC,EACD;QACE2C,KAAK,EAAE,iBAAiB;QACxBC,KAAK,EAAE,EAAAgB,qBAAA,GAAAG,aAAa,CAACqC,cAAc,cAAAxC,qBAAA,uBAA5BA,qBAAA,CAA8BmC,cAAc,CAAC,CAAC,KAAI,GAAG;QAC5DlD,MAAM,EAAEkB,aAAa,CAACsC,aAAa,GAAG,IAAItC,aAAa,CAACsC,aAAa,EAAE,GAAG,GAAG;QAC7EvD,KAAK,EAAEiB,aAAa,CAACsC,aAAa,GAAG,CAAC,GAAG,MAAM,GAAG,SAAS;QAC3DtD,IAAI,EAAElE,SAAS;QACfmB,KAAK,EAAE;MACT,CAAC,CACF,GAAG0C,SAAS,EAAE4D,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC7B3G,OAAA,CAACnC,IAAI;QAAC+I,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAlB,QAAA,eAC9B7F,OAAA,CAACxB,IAAI;UAACwI,EAAE,EAAE,CAAChD,OAAQ;UAACiD,OAAO,EAAE,GAAG,GAAGN,KAAK,GAAG,GAAI;UAAAd,QAAA,eAC7C7F,OAAA;YAAA6F,QAAA,eACE7F,OAAA,CAACC,SAAS;cAACE,KAAK,EAAEuG,IAAI,CAACvG,KAAM;cAAA0F,QAAA,eAC3B7F,OAAA,CAACjC,WAAW;gBAAC4H,EAAE,EAAE;kBAAEuB,CAAC,EAAE;gBAAE,CAAE;gBAAArB,QAAA,eACxB7F,OAAA,CAACpC,GAAG;kBAAC+H,EAAE,EAAE;oBAAEpE,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,YAAY;oBAAEC,cAAc,EAAE;kBAAgB,CAAE;kBAAAoE,QAAA,gBACtF7F,OAAA,CAACpC,GAAG;oBAAC+H,EAAE,EAAE;sBAAEwB,IAAI,EAAE;oBAAE,CAAE;oBAAAtB,QAAA,gBACnB7F,OAAA,CAAChC,UAAU;sBAACoJ,OAAO,EAAC,OAAO;sBAACjH,KAAK,EAAC,gBAAgB;sBAACkH,YAAY;sBAAAxB,QAAA,EAC5Da,IAAI,CAAC5D;oBAAK;sBAAAyC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC,eACb1F,OAAA,CAAChC,UAAU;sBAACoJ,OAAO,EAAC,IAAI;sBAACE,SAAS,EAAC,IAAI;sBAAC3B,EAAE,EAAE;wBAAE4B,UAAU,EAAE,GAAG;wBAAEvB,EAAE,EAAE;sBAAE,CAAE;sBAAAH,QAAA,EACpEa,IAAI,CAAC3D;oBAAK;sBAAAwC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC,eACb1F,OAAA,CAACpC,GAAG;sBAAC+H,EAAE,EAAE;wBAAEpE,OAAO,EAAE,MAAM;wBAAEC,UAAU,EAAE,QAAQ;wBAAEgG,GAAG,EAAE;sBAAE,CAAE;sBAAA3B,QAAA,gBACzD7F,OAAA,CAAC9B,IAAI;wBACHuJ,KAAK,EAAEf,IAAI,CAAC1D,MAAO;wBACnB0E,IAAI,EAAC,OAAO;wBACZvH,KAAK,EAAEuG,IAAI,CAACzD,KAAK,KAAK,IAAI,GAAG,SAAS,GAAGyD,IAAI,CAACzD,KAAK,KAAK,MAAM,GAAG,OAAO,GAAG,SAAU;wBACrFC,IAAI,EAAEwD,IAAI,CAACzD,KAAK,KAAK,IAAI,gBAAGjD,OAAA,CAACrB,UAAU;0BAAA4G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,GAAGgB,IAAI,CAACzD,KAAK,KAAK,MAAM,gBAAGjD,OAAA,CAACpB,YAAY;0BAAA2G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAAG1F,OAAA,CAACV,OAAO;0BAAAiG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBACpGC,EAAE,EAAE;0BAAEgC,QAAQ,EAAE;wBAAU;sBAAE;wBAAApC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B,CAAC,eACF1F,OAAA,CAAChC,UAAU;wBAACoJ,OAAO,EAAC,SAAS;wBAACjH,KAAK,EAAC,gBAAgB;wBAAA0F,QAAA,EACjDa,IAAI,CAACvD;sBAAQ;wBAAAoC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN1F,OAAA,CAACqB,aAAa;oBAAClB,KAAK,EAAEuG,IAAI,CAACvG,KAAM;oBAAA0F,QAAA,eAC/B7F,OAAA,CAAC0G,IAAI,CAACxD,IAAI;sBACR0E,SAAS,EAAC,YAAY;sBACtBjC,EAAE,EAAE;wBACFgC,QAAQ,EAAE,EAAE;wBACZxH,KAAK,EAAE,GAAGuG,IAAI,CAACvG,KAAK,OAAO;wBAC3BG,UAAU,EAAE;sBACd;oBAAE;sBAAAiF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC,GAxC6BgB,IAAI,CAAC5D,KAAK;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAyC1C,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP1F,OAAA,CAACnC,IAAI;MAACkI,SAAS;MAACrD,OAAO,EAAE,CAAE;MAAAmD,QAAA,gBAEzB7F,OAAA,CAACnC,IAAI;QAAC+I,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAAlB,QAAA,eACvB7F,OAAA,CAACzB,IAAI;UAACyI,EAAE,EAAE,CAAChD,OAAQ;UAACiD,OAAO,EAAE,GAAI;UAAApB,QAAA,eAC/B7F,OAAA,CAAClC,IAAI;YAAC6H,EAAE,EAAE;cAAE3E,MAAM,EAAE;YAAO,CAAE;YAAA6E,QAAA,eAC3B7F,OAAA,CAACjC,WAAW;cAAC4H,EAAE,EAAE;gBAAEuB,CAAC,EAAE;cAAE,CAAE;cAAArB,QAAA,gBACxB7F,OAAA,CAACpC,GAAG;gBAAC+H,EAAE,EAAE;kBAAEpE,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEC,cAAc,EAAE,eAAe;kBAAEuE,EAAE,EAAE;gBAAE,CAAE;gBAAAH,QAAA,gBACzF7F,OAAA,CAAChC,UAAU;kBAACoJ,OAAO,EAAC,IAAI;kBAACzB,EAAE,EAAE;oBAAE4B,UAAU,EAAE;kBAAI,CAAE;kBAAA1B,QAAA,EAAC;gBAElD;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb1F,OAAA,CAAC/B,MAAM;kBAACmJ,OAAO,EAAC,UAAU;kBAACM,IAAI,EAAC,OAAO;kBAACvH,KAAK,EAAC,SAAS;kBAAA0F,QAAA,EAAC;gBAExD;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACN1F,OAAA,CAACpC,GAAG;gBAAC+H,EAAE,EAAE;kBAAEpE,OAAO,EAAE,MAAM;kBAAEsG,aAAa,EAAE,QAAQ;kBAAEL,GAAG,EAAE;gBAAE,CAAE;gBAAA3B,QAAA,EAC3D,CAACzB,YAAY,CAAC0D,MAAM,GAAG,CAAC,GAAG1D,YAAY,GAAG,CACzC;kBAAE2D,GAAG,EAAE,GAAG;kBAAEC,WAAW,EAAE,OAAO;kBAAEC,QAAQ,EAAE;oBAAEC,IAAI,EAAE;kBAAW,CAAC;kBAAEC,KAAK,EAAE,CAAC;oBAAED,IAAI,EAAE;kBAAQ,CAAC,EAAE;oBAAEA,IAAI,EAAE;kBAAY,CAAC,CAAC;kBAAEE,KAAK,EAAE,KAAK;kBAAE/C,MAAM,EAAE,WAAW;kBAAEgD,SAAS,EAAE,IAAI7D,IAAI,CAACA,IAAI,CAAC8D,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAACC,WAAW,CAAC;gBAAE,CAAC,EAChN;kBAAER,GAAG,EAAE,GAAG;kBAAEC,WAAW,EAAE,OAAO;kBAAEC,QAAQ,EAAE;oBAAEC,IAAI,EAAE;kBAAa,CAAC;kBAAEC,KAAK,EAAE,CAAC;oBAAED,IAAI,EAAE;kBAAa,CAAC,EAAE;oBAAEA,IAAI,EAAE;kBAAS,CAAC,CAAC;kBAAEE,KAAK,EAAE,IAAI;kBAAE/C,MAAM,EAAE,WAAW;kBAAEgD,SAAS,EAAE,IAAI7D,IAAI,CAACA,IAAI,CAAC8D,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAACC,WAAW,CAAC;gBAAE,CAAC,CACpN,EAAE9B,GAAG,CAAC,CAAC+B,KAAK,EAAE7B,KAAK;kBAAA,IAAA8B,eAAA,EAAAC,YAAA;kBAAA,oBAClB1I,OAAA,CAACxB,IAAI;oBAACwI,EAAE,EAAE,CAAChD,OAAQ;oBAACiD,OAAO,EAAE,IAAI,GAAGN,KAAK,GAAG,GAAI;oBAAAd,QAAA,eAC9C7F,OAAA,CAAC7B,KAAK;sBACJwH,EAAE,EAAE;wBACFuB,CAAC,EAAE,CAAC;wBACJ3G,MAAM,EAAE,WAAW;wBACnBwB,WAAW,EAAE,SAAS;wBACtBZ,YAAY,EAAE,CAAC;wBACfb,UAAU,EAAE,eAAe;wBAC3B,SAAS,EAAE;0BACTyB,WAAW,EAAE,cAAc;0BAC3BrB,SAAS,EAAE,kBAAkB;0BAC7BC,SAAS,EAAE;wBACb;sBACF,CAAE;sBAAAkF,QAAA,eAEF7F,OAAA,CAACpC,GAAG;wBAAC+H,EAAE,EAAE;0BAAEpE,OAAO,EAAE,MAAM;0BAAEC,UAAU,EAAE,QAAQ;0BAAEC,cAAc,EAAE;wBAAgB,CAAE;wBAAAoE,QAAA,gBAClF7F,OAAA,CAACpC,GAAG;0BAAC+H,EAAE,EAAE;4BAAEwB,IAAI,EAAE;0BAAE,CAAE;0BAAAtB,QAAA,gBACnB7F,OAAA,CAAChC,UAAU;4BAACoJ,OAAO,EAAC,WAAW;4BAACzB,EAAE,EAAE;8BAAE4B,UAAU,EAAE;4BAAI,CAAE;4BAAA1B,QAAA,GACrD2C,KAAK,CAACR,WAAW,IAAIQ,KAAK,CAAClF,EAAE,EAAC,KAAG,EAAC,EAAAmF,eAAA,GAAAD,KAAK,CAACP,QAAQ,cAAAQ,eAAA,uBAAdA,eAAA,CAAgBP,IAAI,KAAIM,KAAK,CAACP,QAAQ;0BAAA;4BAAA1C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC/D,CAAC,eACb1F,OAAA,CAAChC,UAAU;4BAACoJ,OAAO,EAAC,OAAO;4BAACjH,KAAK,EAAC,gBAAgB;4BAAA0F,QAAA,EAC/C,EAAA6C,YAAA,GAAAF,KAAK,CAACL,KAAK,cAAAO,YAAA,uBAAXA,YAAA,CAAajC,GAAG,CAACG,IAAI,IAAIA,IAAI,CAACsB,IAAI,CAAC,CAACS,IAAI,CAAC,IAAI,CAAC,KAAIH,KAAK,CAACL;0BAAK;4BAAA5C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CAAC,eACb1F,OAAA,CAAChC,UAAU;4BAACoJ,OAAO,EAAC,SAAS;4BAACjH,KAAK,EAAC,gBAAgB;4BAAA0F,QAAA,EACjD2C,KAAK,CAACH,SAAS,GACd,GAAGO,IAAI,CAACC,KAAK,CAAC,CAACrE,IAAI,CAAC8D,GAAG,CAAC,CAAC,GAAG,IAAI9D,IAAI,CAACgE,KAAK,CAACH,SAAS,CAAC,CAACS,OAAO,CAAC,CAAC,IAAI,KAAK,CAAC,UAAU,GACnFN,KAAK,CAAChF;0BAAI;4BAAA+B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACV,CAAC,eACN1F,OAAA,CAACpC,GAAG;0BAAC+H,EAAE,EAAE;4BAAEpE,OAAO,EAAE,MAAM;4BAAEC,UAAU,EAAE,QAAQ;4BAAEgG,GAAG,EAAE;0BAAE,CAAE;0BAAA3B,QAAA,gBACzD7F,OAAA,CAAChC,UAAU;4BAACoJ,OAAO,EAAC,IAAI;4BAACzB,EAAE,EAAE;8BAAE4B,UAAU,EAAE;4BAAI,CAAE;4BAAA1B,QAAA,EAC9C,OAAO2C,KAAK,CAACJ,KAAK,KAAK,QAAQ,GAAG,IAAII,KAAK,CAACJ,KAAK,CAACW,OAAO,CAAC,CAAC,CAAC,EAAE,GAAGP,KAAK,CAACJ;0BAAK;4BAAA7C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnE,CAAC,eACb1F,OAAA,CAAC9B,IAAI;4BACHuJ,KAAK,EAAEe,KAAK,CAACnD,MAAO;4BACpBqC,IAAI,EAAC,OAAO;4BACZvH,KAAK,EAAEiF,cAAc,CAACoD,KAAK,CAACnD,MAAM,CAAE;4BACpCM,EAAE,EAAE;8BAAEqD,aAAa,EAAE;4BAAa;0BAAE;4BAAAzD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC,GAzC4C8C,KAAK,CAACT,GAAG,IAAIS,KAAK,CAAClF,EAAE;oBAAAiC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA0CrE,CAAC;gBAAA,CACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGP1F,OAAA,CAACnC,IAAI;QAAC+I,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAAlB,QAAA,eACvB7F,OAAA,CAACpC,GAAG;UAAC+H,EAAE,EAAE;YAAEpE,OAAO,EAAE,MAAM;YAAEsG,aAAa,EAAE,QAAQ;YAAEL,GAAG,EAAE,CAAC;YAAExG,MAAM,EAAE;UAAO,CAAE;UAAA6E,QAAA,gBAE5E7F,OAAA,CAACzB,IAAI;YAACyI,EAAE,EAAE,CAAChD,OAAQ;YAACiD,OAAO,EAAE,IAAK;YAAApB,QAAA,eAChC7F,OAAA,CAAClC,IAAI;cAAA+H,QAAA,eACH7F,OAAA,CAACjC,WAAW;gBAAC4H,EAAE,EAAE;kBAAEuB,CAAC,EAAE;gBAAE,CAAE;gBAAArB,QAAA,gBACxB7F,OAAA,CAAChC,UAAU;kBAACoJ,OAAO,EAAC,IAAI;kBAACzB,EAAE,EAAE;oBAAE4B,UAAU,EAAE,GAAG;oBAAEvB,EAAE,EAAE,CAAC;oBAAEzE,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE;kBAAS,CAAE;kBAAAqE,QAAA,gBAC7F7F,OAAA,CAACf,GAAG;oBAAC0G,EAAE,EAAE;sBAAEsD,EAAE,EAAE;oBAAE;kBAAE;oBAAA1D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,iBAExB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb1F,OAAA,CAACnC,IAAI;kBAACkI,SAAS;kBAACrD,OAAO,EAAE,CAAE;kBAAAmD,QAAA,EACxBzC,YAAY,CAACqD,GAAG,CAAC,CAAChD,MAAM,EAAEkD,KAAK,kBAC9B3G,OAAA,CAACnC,IAAI;oBAAC+I,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAhB,QAAA,eACf7F,OAAA,CAACxB,IAAI;sBAACwI,EAAE,EAAE,CAAChD,OAAQ;sBAACiD,OAAO,EAAE,IAAI,GAAGN,KAAK,GAAG,GAAI;sBAAAd,QAAA,eAC9C7F,OAAA;wBAAA6F,QAAA,eACE7F,OAAA,CAAC2B,eAAe;0BAACgE,EAAE,EAAE;4BAAEuD,SAAS,EAAE,QAAQ;4BAAEhC,CAAC,EAAE;0BAAE,CAAE;0BAAArB,QAAA,gBACjD7F,OAAA,CAACqB,aAAa;4BAAClB,KAAK,EAAEsD,MAAM,CAACtD,KAAM;4BAACwF,EAAE,EAAE;8BAAErE,KAAK,EAAE,EAAE;8BAAEN,MAAM,EAAE,EAAE;8BAAEmI,EAAE,EAAE,MAAM;8BAAEnD,EAAE,EAAE;4BAAE,CAAE;4BAAAH,QAAA,eACnF7F,OAAA,CAACyD,MAAM,CAACP,IAAI;8BAAAqC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eAChB1F,OAAA,CAAChC,UAAU;4BAACoJ,OAAO,EAAC,OAAO;4BAACzB,EAAE,EAAE;8BAAE4B,UAAU,EAAE;4BAAI,CAAE;4BAAA1B,QAAA,EACjDpC,MAAM,CAACX;0BAAK;4BAAAyC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACf;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC,GAZcjC,MAAM,CAACX,KAAK;oBAAAyC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAa7B,CACP;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGP1F,OAAA,CAACzB,IAAI;YAACyI,EAAE,EAAE,CAAChD,OAAQ;YAACiD,OAAO,EAAE,IAAK;YAAApB,QAAA,eAChC7F,OAAA,CAAClC,IAAI;cAAC6H,EAAE,EAAE;gBAAEwB,IAAI,EAAE;cAAE,CAAE;cAAAtB,QAAA,eACpB7F,OAAA,CAACjC,WAAW;gBAAC4H,EAAE,EAAE;kBAAEuB,CAAC,EAAE;gBAAE,CAAE;gBAAArB,QAAA,gBACxB7F,OAAA,CAAChC,UAAU;kBAACoJ,OAAO,EAAC,IAAI;kBAACzB,EAAE,EAAE;oBAAE4B,UAAU,EAAE,GAAG;oBAAEvB,EAAE,EAAE,CAAC;oBAAEzE,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE;kBAAS,CAAE;kBAAAqE,QAAA,gBAC7F7F,OAAA,CAACV,OAAO;oBAACqG,EAAE,EAAE;sBAAEsD,EAAE,EAAE,CAAC;sBAAE9I,KAAK,EAAE;oBAAe;kBAAE;oBAAAoF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,UAEnD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb1F,OAAA,CAACpC,GAAG;kBAAC+H,EAAE,EAAE;oBAAEpE,OAAO,EAAE,MAAM;oBAAEsG,aAAa,EAAE,QAAQ;oBAAEL,GAAG,EAAE;kBAAE,CAAE;kBAAA3B,QAAA,EAC3DxC,MAAM,CAACoD,GAAG,CAAC,CAAC2C,KAAK,EAAEzC,KAAK,kBACvB3G,OAAA,CAACxB,IAAI;oBAACwI,EAAE,EAAE,CAAChD,OAAQ;oBAACiD,OAAO,EAAE,IAAI,GAAGN,KAAK,GAAG,GAAI;oBAAAd,QAAA,eAC9C7F,OAAA,CAACkC,SAAS;sBAACC,QAAQ,EAAEiH,KAAK,CAACjH,QAAS;sBAAA0D,QAAA,eAClC7F,OAAA,CAACpC,GAAG;wBAAC+H,EAAE,EAAE;0BAAEpE,OAAO,EAAE,MAAM;0BAAEC,UAAU,EAAE,YAAY;0BAAEC,cAAc,EAAE;wBAAgB,CAAE;wBAAAoE,QAAA,gBACtF7F,OAAA,CAACpC,GAAG;0BAAC+H,EAAE,EAAE;4BAAEpE,OAAO,EAAE,MAAM;4BAAEC,UAAU,EAAE,YAAY;4BAAEgG,GAAG,EAAE,CAAC;4BAAEL,IAAI,EAAE;0BAAE,CAAE;0BAAAtB,QAAA,GACrEP,eAAe,CAAC8D,KAAK,CAACjH,QAAQ,CAAC,eAChCnC,OAAA,CAACpC,GAAG;4BAAAiI,QAAA,gBACF7F,OAAA,CAAChC,UAAU;8BAACoJ,OAAO,EAAC,WAAW;8BAACzB,EAAE,EAAE;gCAAE4B,UAAU,EAAE;8BAAI,CAAE;8BAAA1B,QAAA,EACrDuD,KAAK,CAACtG;4BAAK;8BAAAyC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACF,CAAC,eACb1F,OAAA,CAAChC,UAAU;8BAACoJ,OAAO,EAAC,OAAO;8BAACjH,KAAK,EAAC,gBAAgB;8BAACwF,EAAE,EAAE;gCAAEK,EAAE,EAAE;8BAAE,CAAE;8BAAAH,QAAA,EAC9DuD,KAAK,CAAC7F;4BAAO;8BAAAgC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC,eACb1F,OAAA,CAAChC,UAAU;8BAACoJ,OAAO,EAAC,SAAS;8BAACjH,KAAK,EAAC,gBAAgB;8BAAA0F,QAAA,EACjDuD,KAAK,CAAC5F;4BAAI;8BAAA+B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACD,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACN1F,OAAA,CAAC/B,MAAM;0BAACyJ,IAAI,EAAC,OAAO;0BAACN,OAAO,EAAC,UAAU;0BAAAvB,QAAA,EACpCuD,KAAK,CAAC3F;wBAAM;0BAAA8B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACG;kBAAC,GArBwC0D,KAAK,CAAC9F,EAAE;oBAAAiC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAsBxD,CACP;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEZ;AAAC/B,EAAA,CA/RuBD,cAAc;EAAA,QACtBjF,QAAQ;AAAA;AAAA4K,GAAA,GADA3F,cAAc;AAAA,IAAAtC,EAAA,EAAAM,GAAA,EAAAO,GAAA,EAAAW,GAAA,EAAAyG,GAAA;AAAAC,YAAA,CAAAlI,EAAA;AAAAkI,YAAA,CAAA5H,GAAA;AAAA4H,YAAA,CAAArH,GAAA;AAAAqH,YAAA,CAAA1G,GAAA;AAAA0G,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}