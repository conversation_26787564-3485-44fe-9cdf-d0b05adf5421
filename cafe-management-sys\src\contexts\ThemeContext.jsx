import React, { createContext, useContext, useState, useEffect } from 'react';
import { createTheme, ThemeProvider as MuiThemeProvider } from '@mui/material/styles';
import { useLocation } from 'react-router-dom';

const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// Customer Theme
const customerTheme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#8B4513', // Coffee brown
      light: '#A67B5B',
      dark: '#6A3400',
      contrastText: '#FFFFFF',
    },
    secondary: {
      main: '#D4A574', // Warm golden brown
      light: '#E6C2A6',
      dark: '#B8956A',
      contrastText: '#333333',
    },
    accent: {
      main: '#F4A460', // Sandy brown accent
      light: '#F7B885',
      dark: '#E0944A',
    },
    background: {
      default: '#FBF8F5', // Warm off-white
      paper: '#FFFFFF',
      secondary: '#F5F2ED', // Light coffee cream
      tertiary: '#FAF7F2', // Even lighter cream
    },
    text: {
      primary: '#2C1810',
      secondary: '#5D4E37',
    },
  },
  typography: {
    fontFamily: '"Open Sans", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: { fontFamily: '"Merriweather", serif' },
    h2: { fontFamily: '"Merriweather", serif' },
    h3: { fontFamily: '"Merriweather", serif' },
    h4: { fontFamily: '"Merriweather", serif' },
  },
  shape: {
    borderRadius: 12,
  },
});

// Admin Theme
const adminTheme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
      light: '#42a5f5',
      dark: '#1565c0',
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#dc004e',
      light: '#ff5983',
      dark: '#9a0036',
      contrastText: '#ffffff',
    },
    background: {
      default: '#f5f5f5',
      paper: '#ffffff',
    },
    text: {
      primary: '#212121',
      secondary: '#757575',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
  },
  shape: {
    borderRadius: 8,
  },
  components: {
    MuiDrawer: {
      styleOverrides: {
        paper: {
          backgroundColor: '#ffffff',
          borderRight: '1px solid #e0e0e0',
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundColor: '#ffffff',
          color: '#212121',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        },
      },
    },
  },
});

// Dark Admin Theme
const adminDarkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#90caf9',
      light: '#e3f2fd',
      dark: '#42a5f5',
      contrastText: '#000000',
    },
    secondary: {
      main: '#f48fb1',
      light: '#fce4ec',
      dark: '#ad1457',
      contrastText: '#000000',
    },
    background: {
      default: '#121212',
      paper: '#1e1e1e',
    },
    text: {
      primary: '#ffffff',
      secondary: '#b0b0b0',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
  },
  shape: {
    borderRadius: 8,
  },
  components: {
    MuiDrawer: {
      styleOverrides: {
        paper: {
          backgroundColor: '#1e1e1e',
          borderRight: '1px solid #333333',
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundColor: '#1e1e1e',
          color: '#ffffff',
          boxShadow: '0 2px 8px rgba(0,0,0,0.3)',
        },
      },
    },
  },
});

export const ThemeProvider = ({ children }) => {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const location = useLocation();
  
  // Determine if we're in admin area
  const isAdminArea = location.pathname.startsWith('/admin');
  
  useEffect(() => {
    // Load theme preference from localStorage
    const savedTheme = localStorage.getItem('themeMode');
    if (savedTheme === 'dark') {
      setIsDarkMode(true);
    }
  }, []);

  const toggleTheme = () => {
    const newMode = !isDarkMode;
    setIsDarkMode(newMode);
    localStorage.setItem('themeMode', newMode ? 'dark' : 'light');
  };

  // Select appropriate theme based on area and mode
  const getTheme = () => {
    if (isAdminArea) {
      return isDarkMode ? adminDarkTheme : adminTheme;
    }
    return customerTheme; // Customer area always uses light theme for now
  };

  const value = {
    isDarkMode,
    toggleTheme,
    isAdminArea,
    theme: getTheme(),
  };

  return (
    <ThemeContext.Provider value={value}>
      <MuiThemeProvider theme={getTheme()}>
        {children}
      </MuiThemeProvider>
    </ThemeContext.Provider>
  );
};
