{"ast": null, "code": "var _jsxFileName = \"D:\\\\Github_Repos\\\\cafe-mangement-system-react\\\\cafe-management-sys\\\\src\\\\index.jsx\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { LandingPage } from './pages/customer/LandingPage';\nimport { BrowserRouter, Route, Routes } from 'react-router-dom';\nimport LoginRegisterPage from './pages/customer/LoginRegisterPage';\nimport MenuPage from './pages/customer/MenuPage';\nimport Layout from './components/Layout';\nimport AddMenuItemForm from './pages/admin/AddMenuItemForm';\nimport { CartProvider } from './components/CartContext';\nimport CartPage from './pages/customer/CartPage';\nimport AdminDashboard from './pages/admin/AdminDashboard';\nimport AdminLayout from './pages/admin/AdminLayout';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render( /*#__PURE__*/_jsxDEV(_Fragment, {\n  children: /*#__PURE__*/_jsxDEV(BrowserRouter, {\n    children: /*#__PURE__*/_jsxDEV(CartProvider, {\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Layout, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 36\n          }, this),\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            index: true,\n            element: /*#__PURE__*/_jsxDEV(LandingPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 35\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login-register\",\n            element: /*#__PURE__*/_jsxDEV(LoginRegisterPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 52\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/menu\",\n            element: /*#__PURE__*/_jsxDEV(MenuPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 42\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/menu/add\",\n            element: /*#__PURE__*/_jsxDEV(AddMenuItemForm, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cart\",\n            element: /*#__PURE__*/_jsxDEV(CartPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 42\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin\",\n          element: /*#__PURE__*/_jsxDEV(AdminLayout, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin\",\n            element: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this)\n}, void 0, false));", "map": {"version": 3, "names": ["React", "ReactDOM", "LandingPage", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Route", "Routes", "LoginRegisterPage", "MenuPage", "Layout", "AddMenuItemForm", "CartProvider", "CartPage", "AdminDashboard", "AdminLayout", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "root", "createRoot", "document", "getElementById", "render", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "index"], "sources": ["D:/Github_Repos/cafe-mangement-system-react/cafe-management-sys/src/index.jsx"], "sourcesContent": ["import React from 'react';\r\nimport ReactDOM from 'react-dom/client';\r\nimport { LandingPage } from './pages/customer/LandingPage';\r\nimport { BrowserRouter, Route, Routes } from 'react-router-dom';\r\nimport LoginRegisterPage from './pages/customer/LoginRegisterPage';\r\nimport MenuPage from './pages/customer/MenuPage';\r\nimport Layout from './components/Layout';\r\nimport AddMenuItemForm from './pages/admin/AddMenuItemForm';\r\nimport { CartProvider } from './components/CartContext';\r\nimport CartPage from './pages/customer/CartPage';\r\nimport AdminDashboard from './pages/admin/AdminDashboard';\r\nimport AdminLayout from './pages/admin/AdminLayout';\r\n\r\nconst root = ReactDOM.createRoot(document.getElementById('root'));\r\n\r\nroot.render(\r\n  <>\r\n    <BrowserRouter>\r\n      <CartProvider>\r\n        <Routes>\r\n          <Route path=\"/\" element={<Layout />} >\r\n            <Route index element={<LandingPage />} />\r\n            <Route path=\"/login-register\" element={<LoginRegisterPage />} />\r\n            <Route path='/menu' element={<MenuPage />} />\r\n            <Route path='/menu/add' element={<AddMenuItemForm />} />\r\n            <Route path='/cart' element={<CartPage />} />\r\n          </Route>\r\n          <Route path=\"/admin\" element={<AdminLayout />}>\r\n            <Route path=\"/admin\" element={<AdminDashboard />} />\r\n          </Route>\r\n        </Routes>\r\n      </CartProvider>\r\n    </BrowserRouter>\r\n  </>\r\n);\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,aAAa,EAAEC,KAAK,EAAEC,MAAM,QAAQ,kBAAkB;AAC/D,OAAOC,iBAAiB,MAAM,oCAAoC;AAClE,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,SAASC,YAAY,QAAQ,0BAA0B;AACvD,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,WAAW,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpD,MAAMC,IAAI,GAAGjB,QAAQ,CAACkB,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC;AAEjEH,IAAI,CAACI,MAAM,eACTP,OAAA,CAAAE,SAAA;EAAAM,QAAA,eACER,OAAA,CAACZ,aAAa;IAAAoB,QAAA,eACZR,OAAA,CAACL,YAAY;MAAAa,QAAA,eACXR,OAAA,CAACV,MAAM;QAAAkB,QAAA,gBACLR,OAAA,CAACX,KAAK;UAACoB,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEV,OAAA,CAACP,MAAM;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAN,QAAA,gBAClCR,OAAA,CAACX,KAAK;YAAC0B,KAAK;YAACL,OAAO,eAAEV,OAAA,CAACb,WAAW;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzCd,OAAA,CAACX,KAAK;YAACoB,IAAI,EAAC,iBAAiB;YAACC,OAAO,eAAEV,OAAA,CAACT,iBAAiB;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChEd,OAAA,CAACX,KAAK;YAACoB,IAAI,EAAC,OAAO;YAACC,OAAO,eAAEV,OAAA,CAACR,QAAQ;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7Cd,OAAA,CAACX,KAAK;YAACoB,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEV,OAAA,CAACN,eAAe;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxDd,OAAA,CAACX,KAAK;YAACoB,IAAI,EAAC,OAAO;YAACC,OAAO,eAAEV,OAAA,CAACJ,QAAQ;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACRd,OAAA,CAACX,KAAK;UAACoB,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEV,OAAA,CAACF,WAAW;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAN,QAAA,eAC5CR,OAAA,CAACX,KAAK;YAACoB,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAEV,OAAA,CAACH,cAAc;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF;AAAC,gBAChB,CACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}