{"ast": null, "code": "var _jsxFileName = \"D:\\\\Github_Repos\\\\cafe-mangement-system-react\\\\cafe-management-sys\\\\src\\\\pages\\\\admin\\\\AdminMenu.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Card, CardContent, CardMedia, Grid, Button, TextField, InputAdornment, Chip, IconButton, Menu, MenuItem, Dialog, DialogTitle, DialogContent, DialogActions, Fab, Switch, FormControlLabel } from '@mui/material';\nimport { Search, Add, Edit, Delete, MoreVert, Visibility, FilterList } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { menuAPI } from '../../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminMenu = () => {\n  _s();\n  const navigate = useNavigate();\n  const [menuItems, setMenuItems] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [categoryFilter, setCategoryFilter] = useState('all');\n  const [selectedItem, setSelectedItem] = useState(null);\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [selectedItemId, setSelectedItemId] = useState(null);\n  const categories = [{\n    value: 'all',\n    label: 'All Categories'\n  }, {\n    value: 'coffee',\n    label: 'Coffee'\n  }, {\n    value: 'tea',\n    label: 'Tea'\n  }, {\n    value: 'pastries',\n    label: 'Pastries'\n  }, {\n    value: 'sandwiches',\n    label: 'Sandwiches'\n  }, {\n    value: 'desserts',\n    label: 'Desserts'\n  }];\n  useEffect(() => {\n    fetchMenuItems();\n  }, []);\n  const fetchMenuItems = async () => {\n    try {\n      setLoading(true);\n      const data = await menuAPI.getAll();\n      setMenuItems(data);\n    } catch (error) {\n      console.error('Error fetching menu items:', error);\n      // Mock data fallback\n      setMenuItems([{\n        _id: '1',\n        title: 'Espresso',\n        description: 'Rich and bold espresso shot',\n        price: 2.50,\n        category: 'coffee',\n        image: '/api/placeholder/300/200',\n        available: true,\n        ingredients: ['Coffee beans', 'Water'],\n        allergens: []\n      }, {\n        _id: '2',\n        title: 'Croissant',\n        description: 'Buttery, flaky pastry',\n        price: 3.00,\n        category: 'pastries',\n        image: '/api/placeholder/300/200',\n        available: true,\n        ingredients: ['Flour', 'Butter', 'Eggs'],\n        allergens: ['Gluten', 'Dairy']\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteItem = async () => {\n    try {\n      await menuAPI.delete(selectedItem._id);\n      setMenuItems(menuItems.filter(item => item._id !== selectedItem._id));\n      setDeleteDialogOpen(false);\n      setSelectedItem(null);\n    } catch (error) {\n      console.error('Error deleting menu item:', error);\n    }\n  };\n  const handleToggleAvailability = async (itemId, available) => {\n    try {\n      const item = menuItems.find(item => item._id === itemId);\n      await menuAPI.update(itemId, {\n        ...item,\n        available\n      });\n      setMenuItems(menuItems.map(item => item._id === itemId ? {\n        ...item,\n        available\n      } : item));\n    } catch (error) {\n      console.error('Error updating item availability:', error);\n    }\n  };\n  const filteredItems = menuItems.filter(item => {\n    const matchesSearch = item.title.toLowerCase().includes(searchQuery.toLowerCase()) || item.description.toLowerCase().includes(searchQuery.toLowerCase());\n    const matchesCategory = categoryFilter === 'all' || item.category === categoryFilter;\n    return matchesSearch && matchesCategory;\n  });\n  const handleMenuClick = (event, itemId) => {\n    setAnchorEl(event.currentTarget);\n    setSelectedItemId(itemId);\n  };\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n    setSelectedItemId(null);\n  };\n  const handleEditItem = () => {\n    navigate(`/admin/menu/edit/${selectedItemId}`);\n    handleMenuClose();\n  };\n  const handleDeleteClick = () => {\n    const item = menuItems.find(item => item._id === selectedItemId);\n    setSelectedItem(item);\n    setDeleteDialogOpen(true);\n    handleMenuClose();\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          fontWeight: 600\n        },\n        children: \"Menu Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(Add, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 22\n        }, this),\n        onClick: () => navigate('/admin/menu/add'),\n        sx: {\n          borderRadius: 2\n        },\n        children: \"Add New Item\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              placeholder: \"Search menu items...\",\n              value: searchQuery,\n              onChange: e => setSearchQuery(e.target.value),\n              InputProps: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(Search, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 178,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 21\n                }, this)\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              select: true,\n              label: \"Category\",\n              value: categoryFilter,\n              onChange: e => setCategoryFilter(e.target.value),\n              children: categories.map(category => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: category.value,\n                children: category.label\n              }, category.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 2,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              fullWidth: true,\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(FilterList, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 28\n              }, this),\n              children: \"Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: loading ? /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          align: \"center\",\n          children: \"Loading menu items...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 11\n      }, this) : filteredItems.length === 0 ? /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          align: \"center\",\n          children: \"No menu items found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 11\n      }, this) : filteredItems.map(item => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        lg: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column',\n            position: 'relative',\n            opacity: item.available ? 1 : 0.7\n          },\n          children: [/*#__PURE__*/_jsxDEV(CardMedia, {\n            component: \"img\",\n            height: \"200\",\n            image: item.image || '/api/placeholder/300/200',\n            alt: item.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              position: 'absolute',\n              top: 8,\n              right: 8\n            },\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: e => handleMenuClick(e, item._id),\n              sx: {\n                bgcolor: 'background.paper',\n                '&:hover': {\n                  bgcolor: 'background.paper'\n                }\n              },\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(MoreVert, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              flexGrow: 1,\n              display: 'flex',\n              flexDirection: 'column'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'flex-start',\n                mb: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                component: \"h3\",\n                sx: {\n                  fontWeight: 600\n                },\n                children: item.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: item.category,\n                size: \"small\",\n                color: \"primary\",\n                variant: \"outlined\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              sx: {\n                mb: 2,\n                flexGrow: 1\n              },\n              children: item.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                color: \"primary\",\n                sx: {\n                  fontWeight: 600\n                },\n                children: [\"$\", item.price.toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                control: /*#__PURE__*/_jsxDEV(Switch, {\n                  checked: item.available,\n                  onChange: e => handleToggleAvailability(item._id, e.target.checked),\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 25\n                }, this),\n                label: \"Available\",\n                labelPlacement: \"start\",\n                sx: {\n                  m: 0\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 19\n            }, this), item.allergens && item.allergens.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexWrap: 'wrap',\n                gap: 0.5\n              },\n              children: item.allergens.map((allergen, index) => /*#__PURE__*/_jsxDEV(Chip, {\n                label: allergen,\n                size: \"small\",\n                color: \"warning\",\n                variant: \"outlined\"\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 25\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 15\n        }, this)\n      }, item._id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Fab, {\n      color: \"primary\",\n      \"aria-label\": \"add\",\n      sx: {\n        position: 'fixed',\n        bottom: 16,\n        right: 16\n      },\n      onClick: () => navigate('/admin/menu/add'),\n      children: /*#__PURE__*/_jsxDEV(Add, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Menu, {\n      anchorEl: anchorEl,\n      open: Boolean(anchorEl),\n      onClose: handleMenuClose,\n      children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => {\n          const item = menuItems.find(item => item._id === selectedItemId);\n          console.log('View item:', item);\n          handleMenuClose();\n        },\n        children: [/*#__PURE__*/_jsxDEV(Visibility, {\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 11\n        }, this), \"View Details\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: handleEditItem,\n        children: [/*#__PURE__*/_jsxDEV(Edit, {\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this), \"Edit Item\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: handleDeleteClick,\n        sx: {\n          color: 'error.main'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Delete, {\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this), \"Delete Item\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: deleteDialogOpen,\n      onClose: () => setDeleteDialogOpen(false),\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Delete Menu Item\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          children: [\"Are you sure you want to delete \\\"\", selectedItem === null || selectedItem === void 0 ? void 0 : selectedItem.title, \"\\\"? This action cannot be undone.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setDeleteDialogOpen(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDeleteItem,\n          color: \"error\",\n          variant: \"contained\",\n          children: \"Delete\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 150,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminMenu, \"qCRZqbE3wh21f1tAsWhaZ8CJeGQ=\", false, function () {\n  return [useNavigate];\n});\n_c = AdminMenu;\nexport default AdminMenu;\nvar _c;\n$RefreshReg$(_c, \"AdminMenu\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardMedia", "Grid", "<PERSON><PERSON>", "TextField", "InputAdornment", "Chip", "IconButton", "<PERSON><PERSON>", "MenuItem", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Fab", "Switch", "FormControlLabel", "Search", "Add", "Edit", "Delete", "<PERSON><PERSON><PERSON>", "Visibility", "FilterList", "useNavigate", "menuAPI", "jsxDEV", "_jsxDEV", "AdminMenu", "_s", "navigate", "menuItems", "setMenuItems", "loading", "setLoading", "searchQuery", "setSearch<PERSON>uery", "categoryFilter", "setCate<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedItem", "setSelectedItem", "deleteDialogOpen", "setDeleteDialogOpen", "anchorEl", "setAnchorEl", "selectedItemId", "setSelectedItemId", "categories", "value", "label", "fetchMenuItems", "data", "getAll", "error", "console", "_id", "title", "description", "price", "category", "image", "available", "ingredients", "allergens", "handleDeleteItem", "delete", "filter", "item", "handleToggleAvailability", "itemId", "find", "update", "map", "filteredItems", "matchesSearch", "toLowerCase", "includes", "matchesCategory", "handleMenuClick", "event", "currentTarget", "handleMenuClose", "handleEditItem", "handleDeleteClick", "sx", "p", "children", "display", "justifyContent", "alignItems", "mb", "variant", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "startIcon", "onClick", "borderRadius", "container", "spacing", "xs", "md", "fullWidth", "placeholder", "onChange", "e", "target", "InputProps", "startAdornment", "position", "select", "align", "length", "sm", "lg", "height", "flexDirection", "opacity", "component", "alt", "top", "right", "bgcolor", "size", "flexGrow", "color", "toFixed", "control", "checked", "labelPlacement", "m", "flexWrap", "gap", "allergen", "index", "bottom", "open", "Boolean", "onClose", "log", "mr", "_c", "$RefreshReg$"], "sources": ["D:/Github_Repos/cafe-mangement-system-react/cafe-management-sys/src/pages/admin/AdminMenu.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typo<PERSON>,\n  Card,\n  CardContent,\n  CardMedia,\n  Grid,\n  Button,\n  TextField,\n  InputAdornment,\n  Chip,\n  IconButton,\n  Menu,\n  MenuItem,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Fab,\n  Switch,\n  FormControlLabel,\n} from '@mui/material';\nimport {\n  Search,\n  Add,\n  Edit,\n  Delete,\n  MoreVert,\n  Visibility,\n  FilterList,\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { menuAPI } from '../../services/api';\n\nconst AdminMenu = () => {\n  const navigate = useNavigate();\n  const [menuItems, setMenuItems] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [categoryFilter, setCategoryFilter] = useState('all');\n  const [selectedItem, setSelectedItem] = useState(null);\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [selectedItemId, setSelectedItemId] = useState(null);\n\n  const categories = [\n    { value: 'all', label: 'All Categories' },\n    { value: 'coffee', label: 'Coffee' },\n    { value: 'tea', label: 'Tea' },\n    { value: 'pastries', label: 'Pastries' },\n    { value: 'sandwiches', label: 'Sandwiches' },\n    { value: 'desserts', label: 'Desserts' },\n  ];\n\n  useEffect(() => {\n    fetchMenuItems();\n  }, []);\n\n  const fetchMenuItems = async () => {\n    try {\n      setLoading(true);\n      const data = await menuAPI.getAll();\n      setMenuItems(data);\n    } catch (error) {\n      console.error('Error fetching menu items:', error);\n      // Mock data fallback\n      setMenuItems([\n        {\n          _id: '1',\n          title: 'Espresso',\n          description: 'Rich and bold espresso shot',\n          price: 2.50,\n          category: 'coffee',\n          image: '/api/placeholder/300/200',\n          available: true,\n          ingredients: ['Coffee beans', 'Water'],\n          allergens: [],\n        },\n        {\n          _id: '2',\n          title: 'Croissant',\n          description: 'Buttery, flaky pastry',\n          price: 3.00,\n          category: 'pastries',\n          image: '/api/placeholder/300/200',\n          available: true,\n          ingredients: ['Flour', 'Butter', 'Eggs'],\n          allergens: ['Gluten', 'Dairy'],\n        },\n      ]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteItem = async () => {\n    try {\n      await menuAPI.delete(selectedItem._id);\n      setMenuItems(menuItems.filter(item => item._id !== selectedItem._id));\n      setDeleteDialogOpen(false);\n      setSelectedItem(null);\n    } catch (error) {\n      console.error('Error deleting menu item:', error);\n    }\n  };\n\n  const handleToggleAvailability = async (itemId, available) => {\n    try {\n      const item = menuItems.find(item => item._id === itemId);\n      await menuAPI.update(itemId, { ...item, available });\n      setMenuItems(menuItems.map(item => \n        item._id === itemId ? { ...item, available } : item\n      ));\n    } catch (error) {\n      console.error('Error updating item availability:', error);\n    }\n  };\n\n  const filteredItems = menuItems.filter(item => {\n    const matchesSearch = item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                         item.description.toLowerCase().includes(searchQuery.toLowerCase());\n    const matchesCategory = categoryFilter === 'all' || item.category === categoryFilter;\n    return matchesSearch && matchesCategory;\n  });\n\n  const handleMenuClick = (event, itemId) => {\n    setAnchorEl(event.currentTarget);\n    setSelectedItemId(itemId);\n  };\n\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n    setSelectedItemId(null);\n  };\n\n  const handleEditItem = () => {\n    navigate(`/admin/menu/edit/${selectedItemId}`);\n    handleMenuClose();\n  };\n\n  const handleDeleteClick = () => {\n    const item = menuItems.find(item => item._id === selectedItemId);\n    setSelectedItem(item);\n    setDeleteDialogOpen(true);\n    handleMenuClose();\n  };\n\n  return (\n    <Box sx={{ p: 3 }}>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h4\" sx={{ fontWeight: 600 }}>\n          Menu Management\n        </Typography>\n        <Button\n          variant=\"contained\"\n          startIcon={<Add />}\n          onClick={() => navigate('/admin/menu/add')}\n          sx={{ borderRadius: 2 }}\n        >\n          Add New Item\n        </Button>\n      </Box>\n\n      {/* Search and Filters */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Grid container spacing={2} alignItems=\"center\">\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                placeholder=\"Search menu items...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                InputProps={{\n                  startAdornment: (\n                    <InputAdornment position=\"start\">\n                      <Search />\n                    </InputAdornment>\n                  ),\n                }}\n              />\n            </Grid>\n            <Grid item xs={12} md={4}>\n              <TextField\n                fullWidth\n                select\n                label=\"Category\"\n                value={categoryFilter}\n                onChange={(e) => setCategoryFilter(e.target.value)}\n              >\n                {categories.map((category) => (\n                  <MenuItem key={category.value} value={category.value}>\n                    {category.label}\n                  </MenuItem>\n                ))}\n              </TextField>\n            </Grid>\n            <Grid item xs={12} md={2}>\n              <Button\n                fullWidth\n                variant=\"outlined\"\n                startIcon={<FilterList />}\n              >\n                Filters\n              </Button>\n            </Grid>\n          </Grid>\n        </CardContent>\n      </Card>\n\n      {/* Menu Items Grid */}\n      <Grid container spacing={3}>\n        {loading ? (\n          <Grid item xs={12}>\n            <Typography align=\"center\">Loading menu items...</Typography>\n          </Grid>\n        ) : filteredItems.length === 0 ? (\n          <Grid item xs={12}>\n            <Typography align=\"center\">No menu items found</Typography>\n          </Grid>\n        ) : (\n          filteredItems.map((item) => (\n            <Grid item xs={12} sm={6} md={4} lg={3} key={item._id}>\n              <Card \n                sx={{ \n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  position: 'relative',\n                  opacity: item.available ? 1 : 0.7,\n                }}\n              >\n                <CardMedia\n                  component=\"img\"\n                  height=\"200\"\n                  image={item.image || '/api/placeholder/300/200'}\n                  alt={item.title}\n                />\n                \n                <Box sx={{ position: 'absolute', top: 8, right: 8 }}>\n                  <IconButton\n                    onClick={(e) => handleMenuClick(e, item._id)}\n                    sx={{ \n                      bgcolor: 'background.paper',\n                      '&:hover': { bgcolor: 'background.paper' }\n                    }}\n                    size=\"small\"\n                  >\n                    <MoreVert />\n                  </IconButton>\n                </Box>\n\n                <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>\n                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>\n                    <Typography variant=\"h6\" component=\"h3\" sx={{ fontWeight: 600 }}>\n                      {item.title}\n                    </Typography>\n                    <Chip\n                      label={item.category}\n                      size=\"small\"\n                      color=\"primary\"\n                      variant=\"outlined\"\n                    />\n                  </Box>\n                  \n                  <Typography \n                    variant=\"body2\" \n                    color=\"text.secondary\" \n                    sx={{ mb: 2, flexGrow: 1 }}\n                  >\n                    {item.description}\n                  </Typography>\n\n                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n                    <Typography variant=\"h6\" color=\"primary\" sx={{ fontWeight: 600 }}>\n                      ${item.price.toFixed(2)}\n                    </Typography>\n                    <FormControlLabel\n                      control={\n                        <Switch\n                          checked={item.available}\n                          onChange={(e) => handleToggleAvailability(item._id, e.target.checked)}\n                          size=\"small\"\n                        />\n                      }\n                      label=\"Available\"\n                      labelPlacement=\"start\"\n                      sx={{ m: 0 }}\n                    />\n                  </Box>\n\n                  {item.allergens && item.allergens.length > 0 && (\n                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>\n                      {item.allergens.map((allergen, index) => (\n                        <Chip\n                          key={index}\n                          label={allergen}\n                          size=\"small\"\n                          color=\"warning\"\n                          variant=\"outlined\"\n                        />\n                      ))}\n                    </Box>\n                  )}\n                </CardContent>\n              </Card>\n            </Grid>\n          ))\n        )}\n      </Grid>\n\n      {/* Floating Action Button */}\n      <Fab\n        color=\"primary\"\n        aria-label=\"add\"\n        sx={{ position: 'fixed', bottom: 16, right: 16 }}\n        onClick={() => navigate('/admin/menu/add')}\n      >\n        <Add />\n      </Fab>\n\n      {/* Action Menu */}\n      <Menu\n        anchorEl={anchorEl}\n        open={Boolean(anchorEl)}\n        onClose={handleMenuClose}\n      >\n        <MenuItem onClick={() => {\n          const item = menuItems.find(item => item._id === selectedItemId);\n          console.log('View item:', item);\n          handleMenuClose();\n        }}>\n          <Visibility sx={{ mr: 1 }} />\n          View Details\n        </MenuItem>\n        <MenuItem onClick={handleEditItem}>\n          <Edit sx={{ mr: 1 }} />\n          Edit Item\n        </MenuItem>\n        <MenuItem onClick={handleDeleteClick} sx={{ color: 'error.main' }}>\n          <Delete sx={{ mr: 1 }} />\n          Delete Item\n        </MenuItem>\n      </Menu>\n\n      {/* Delete Confirmation Dialog */}\n      <Dialog\n        open={deleteDialogOpen}\n        onClose={() => setDeleteDialogOpen(false)}\n      >\n        <DialogTitle>Delete Menu Item</DialogTitle>\n        <DialogContent>\n          <Typography>\n            Are you sure you want to delete \"{selectedItem?.title}\"? This action cannot be undone.\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>\n          <Button onClick={handleDeleteItem} color=\"error\" variant=\"contained\">\n            Delete\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default AdminMenu;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,SAAS,EACTC,IAAI,EACJC,MAAM,EACNC,SAAS,EACTC,cAAc,EACdC,IAAI,EACJC,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,GAAG,EACHC,MAAM,EACNC,gBAAgB,QACX,eAAe;AACtB,SACEC,MAAM,EACNC,GAAG,EACHC,IAAI,EACJC,MAAM,EACNC,QAAQ,EACRC,UAAU,EACVC,UAAU,QACL,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACO,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwC,WAAW,EAAEC,cAAc,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC0C,cAAc,EAAEC,iBAAiB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC8C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACgD,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACkD,cAAc,EAAEC,iBAAiB,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EAE1D,MAAMoD,UAAU,GAAG,CACjB;IAAEC,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAiB,CAAC,EACzC;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC,EACpC;IAAED,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAM,CAAC,EAC9B;IAAED,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAW,CAAC,EACxC;IAAED,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAa,CAAC,EAC5C;IAAED,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAW,CAAC,CACzC;EAEDrD,SAAS,CAAC,MAAM;IACdsD,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFhB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMiB,IAAI,GAAG,MAAM1B,OAAO,CAAC2B,MAAM,CAAC,CAAC;MACnCpB,YAAY,CAACmB,IAAI,CAAC;IACpB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD;MACArB,YAAY,CAAC,CACX;QACEuB,GAAG,EAAE,GAAG;QACRC,KAAK,EAAE,UAAU;QACjBC,WAAW,EAAE,6BAA6B;QAC1CC,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE,0BAA0B;QACjCC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE,CAAC,cAAc,EAAE,OAAO,CAAC;QACtCC,SAAS,EAAE;MACb,CAAC,EACD;QACER,GAAG,EAAE,GAAG;QACRC,KAAK,EAAE,WAAW;QAClBC,WAAW,EAAE,uBAAuB;QACpCC,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,UAAU;QACpBC,KAAK,EAAE,0BAA0B;QACjCC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC;QACxCC,SAAS,EAAE,CAAC,QAAQ,EAAE,OAAO;MAC/B,CAAC,CACF,CAAC;IACJ,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM8B,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMvC,OAAO,CAACwC,MAAM,CAAC1B,YAAY,CAACgB,GAAG,CAAC;MACtCvB,YAAY,CAACD,SAAS,CAACmC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACZ,GAAG,KAAKhB,YAAY,CAACgB,GAAG,CAAC,CAAC;MACrEb,mBAAmB,CAAC,KAAK,CAAC;MAC1BF,eAAe,CAAC,IAAI,CAAC;IACvB,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;EACF,CAAC;EAED,MAAMe,wBAAwB,GAAG,MAAAA,CAAOC,MAAM,EAAER,SAAS,KAAK;IAC5D,IAAI;MACF,MAAMM,IAAI,GAAGpC,SAAS,CAACuC,IAAI,CAACH,IAAI,IAAIA,IAAI,CAACZ,GAAG,KAAKc,MAAM,CAAC;MACxD,MAAM5C,OAAO,CAAC8C,MAAM,CAACF,MAAM,EAAE;QAAE,GAAGF,IAAI;QAAEN;MAAU,CAAC,CAAC;MACpD7B,YAAY,CAACD,SAAS,CAACyC,GAAG,CAACL,IAAI,IAC7BA,IAAI,CAACZ,GAAG,KAAKc,MAAM,GAAG;QAAE,GAAGF,IAAI;QAAEN;MAAU,CAAC,GAAGM,IACjD,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOd,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IAC3D;EACF,CAAC;EAED,MAAMoB,aAAa,GAAG1C,SAAS,CAACmC,MAAM,CAACC,IAAI,IAAI;IAC7C,MAAMO,aAAa,GAAGP,IAAI,CAACX,KAAK,CAACmB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzC,WAAW,CAACwC,WAAW,CAAC,CAAC,CAAC,IAC7DR,IAAI,CAACV,WAAW,CAACkB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzC,WAAW,CAACwC,WAAW,CAAC,CAAC,CAAC;IACvF,MAAME,eAAe,GAAGxC,cAAc,KAAK,KAAK,IAAI8B,IAAI,CAACR,QAAQ,KAAKtB,cAAc;IACpF,OAAOqC,aAAa,IAAIG,eAAe;EACzC,CAAC,CAAC;EAEF,MAAMC,eAAe,GAAGA,CAACC,KAAK,EAAEV,MAAM,KAAK;IACzCzB,WAAW,CAACmC,KAAK,CAACC,aAAa,CAAC;IAChClC,iBAAiB,CAACuB,MAAM,CAAC;EAC3B,CAAC;EAED,MAAMY,eAAe,GAAGA,CAAA,KAAM;IAC5BrC,WAAW,CAAC,IAAI,CAAC;IACjBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMoC,cAAc,GAAGA,CAAA,KAAM;IAC3BpD,QAAQ,CAAC,oBAAoBe,cAAc,EAAE,CAAC;IAC9CoC,eAAe,CAAC,CAAC;EACnB,CAAC;EAED,MAAME,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMhB,IAAI,GAAGpC,SAAS,CAACuC,IAAI,CAACH,IAAI,IAAIA,IAAI,CAACZ,GAAG,KAAKV,cAAc,CAAC;IAChEL,eAAe,CAAC2B,IAAI,CAAC;IACrBzB,mBAAmB,CAAC,IAAI,CAAC;IACzBuC,eAAe,CAAC,CAAC;EACnB,CAAC;EAED,oBACEtD,OAAA,CAAC9B,GAAG;IAACuF,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAChB3D,OAAA,CAAC9B,GAAG;MAACuF,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzF3D,OAAA,CAAC7B,UAAU;QAAC6F,OAAO,EAAC,IAAI;QAACP,EAAE,EAAE;UAAEQ,UAAU,EAAE;QAAI,CAAE;QAAAN,QAAA,EAAC;MAElD;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbrE,OAAA,CAACxB,MAAM;QACLwF,OAAO,EAAC,WAAW;QACnBM,SAAS,eAAEtE,OAAA,CAACT,GAAG;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACnBE,OAAO,EAAEA,CAAA,KAAMpE,QAAQ,CAAC,iBAAiB,CAAE;QAC3CsD,EAAE,EAAE;UAAEe,YAAY,EAAE;QAAE,CAAE;QAAAb,QAAA,EACzB;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNrE,OAAA,CAAC5B,IAAI;MAACqF,EAAE,EAAE;QAAEM,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eAClB3D,OAAA,CAAC3B,WAAW;QAAAsF,QAAA,eACV3D,OAAA,CAACzB,IAAI;UAACkG,SAAS;UAACC,OAAO,EAAE,CAAE;UAACZ,UAAU,EAAC,QAAQ;UAAAH,QAAA,gBAC7C3D,OAAA,CAACzB,IAAI;YAACiE,IAAI;YAACmC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAjB,QAAA,eACvB3D,OAAA,CAACvB,SAAS;cACRoG,SAAS;cACTC,WAAW,EAAC,sBAAsB;cAClCzD,KAAK,EAAEb,WAAY;cACnBuE,QAAQ,EAAGC,CAAC,IAAKvE,cAAc,CAACuE,CAAC,CAACC,MAAM,CAAC5D,KAAK,CAAE;cAChD6D,UAAU,EAAE;gBACVC,cAAc,eACZnF,OAAA,CAACtB,cAAc;kBAAC0G,QAAQ,EAAC,OAAO;kBAAAzB,QAAA,eAC9B3D,OAAA,CAACV,MAAM;oBAAA4E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAEpB;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrE,OAAA,CAACzB,IAAI;YAACiE,IAAI;YAACmC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAjB,QAAA,eACvB3D,OAAA,CAACvB,SAAS;cACRoG,SAAS;cACTQ,MAAM;cACN/D,KAAK,EAAC,UAAU;cAChBD,KAAK,EAAEX,cAAe;cACtBqE,QAAQ,EAAGC,CAAC,IAAKrE,iBAAiB,CAACqE,CAAC,CAACC,MAAM,CAAC5D,KAAK,CAAE;cAAAsC,QAAA,EAElDvC,UAAU,CAACyB,GAAG,CAAEb,QAAQ,iBACvBhC,OAAA,CAAClB,QAAQ;gBAAsBuC,KAAK,EAAEW,QAAQ,CAACX,KAAM;gBAAAsC,QAAA,EAClD3B,QAAQ,CAACV;cAAK,GADFU,QAAQ,CAACX,KAAK;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEnB,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACPrE,OAAA,CAACzB,IAAI;YAACiE,IAAI;YAACmC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAjB,QAAA,eACvB3D,OAAA,CAACxB,MAAM;cACLqG,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBM,SAAS,eAAEtE,OAAA,CAACJ,UAAU;gBAAAsE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAV,QAAA,EAC3B;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPrE,OAAA,CAACzB,IAAI;MAACkG,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAf,QAAA,EACxBrD,OAAO,gBACNN,OAAA,CAACzB,IAAI;QAACiE,IAAI;QAACmC,EAAE,EAAE,EAAG;QAAAhB,QAAA,eAChB3D,OAAA,CAAC7B,UAAU;UAACmH,KAAK,EAAC,QAAQ;UAAA3B,QAAA,EAAC;QAAqB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,GACLvB,aAAa,CAACyC,MAAM,KAAK,CAAC,gBAC5BvF,OAAA,CAACzB,IAAI;QAACiE,IAAI;QAACmC,EAAE,EAAE,EAAG;QAAAhB,QAAA,eAChB3D,OAAA,CAAC7B,UAAU;UAACmH,KAAK,EAAC,QAAQ;UAAA3B,QAAA,EAAC;QAAmB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,GAEPvB,aAAa,CAACD,GAAG,CAAEL,IAAI,iBACrBxC,OAAA,CAACzB,IAAI;QAACiE,IAAI;QAACmC,EAAE,EAAE,EAAG;QAACa,EAAE,EAAE,CAAE;QAACZ,EAAE,EAAE,CAAE;QAACa,EAAE,EAAE,CAAE;QAAA9B,QAAA,eACrC3D,OAAA,CAAC5B,IAAI;UACHqF,EAAE,EAAE;YACFiC,MAAM,EAAE,MAAM;YACd9B,OAAO,EAAE,MAAM;YACf+B,aAAa,EAAE,QAAQ;YACvBP,QAAQ,EAAE,UAAU;YACpBQ,OAAO,EAAEpD,IAAI,CAACN,SAAS,GAAG,CAAC,GAAG;UAChC,CAAE;UAAAyB,QAAA,gBAEF3D,OAAA,CAAC1B,SAAS;YACRuH,SAAS,EAAC,KAAK;YACfH,MAAM,EAAC,KAAK;YACZzD,KAAK,EAAEO,IAAI,CAACP,KAAK,IAAI,0BAA2B;YAChD6D,GAAG,EAAEtD,IAAI,CAACX;UAAM;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eAEFrE,OAAA,CAAC9B,GAAG;YAACuF,EAAE,EAAE;cAAE2B,QAAQ,EAAE,UAAU;cAAEW,GAAG,EAAE,CAAC;cAAEC,KAAK,EAAE;YAAE,CAAE;YAAArC,QAAA,eAClD3D,OAAA,CAACpB,UAAU;cACT2F,OAAO,EAAGS,CAAC,IAAK7B,eAAe,CAAC6B,CAAC,EAAExC,IAAI,CAACZ,GAAG,CAAE;cAC7C6B,EAAE,EAAE;gBACFwC,OAAO,EAAE,kBAAkB;gBAC3B,SAAS,EAAE;kBAAEA,OAAO,EAAE;gBAAmB;cAC3C,CAAE;cACFC,IAAI,EAAC,OAAO;cAAAvC,QAAA,eAEZ3D,OAAA,CAACN,QAAQ;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAENrE,OAAA,CAAC3B,WAAW;YAACoF,EAAE,EAAE;cAAE0C,QAAQ,EAAE,CAAC;cAAEvC,OAAO,EAAE,MAAM;cAAE+B,aAAa,EAAE;YAAS,CAAE;YAAAhC,QAAA,gBACzE3D,OAAA,CAAC9B,GAAG;cAACuF,EAAE,EAAE;gBAAEG,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE,YAAY;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC7F3D,OAAA,CAAC7B,UAAU;gBAAC6F,OAAO,EAAC,IAAI;gBAAC6B,SAAS,EAAC,IAAI;gBAACpC,EAAE,EAAE;kBAAEQ,UAAU,EAAE;gBAAI,CAAE;gBAAAN,QAAA,EAC7DnB,IAAI,CAACX;cAAK;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACbrE,OAAA,CAACrB,IAAI;gBACH2C,KAAK,EAAEkB,IAAI,CAACR,QAAS;gBACrBkE,IAAI,EAAC,OAAO;gBACZE,KAAK,EAAC,SAAS;gBACfpC,OAAO,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENrE,OAAA,CAAC7B,UAAU;cACT6F,OAAO,EAAC,OAAO;cACfoC,KAAK,EAAC,gBAAgB;cACtB3C,EAAE,EAAE;gBAAEM,EAAE,EAAE,CAAC;gBAAEoC,QAAQ,EAAE;cAAE,CAAE;cAAAxC,QAAA,EAE1BnB,IAAI,CAACV;YAAW;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eAEbrE,OAAA,CAAC9B,GAAG;cAACuF,EAAE,EAAE;gBAAEG,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBACzF3D,OAAA,CAAC7B,UAAU;gBAAC6F,OAAO,EAAC,IAAI;gBAACoC,KAAK,EAAC,SAAS;gBAAC3C,EAAE,EAAE;kBAAEQ,UAAU,EAAE;gBAAI,CAAE;gBAAAN,QAAA,GAAC,GAC/D,EAACnB,IAAI,CAACT,KAAK,CAACsE,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eACbrE,OAAA,CAACX,gBAAgB;gBACfiH,OAAO,eACLtG,OAAA,CAACZ,MAAM;kBACLmH,OAAO,EAAE/D,IAAI,CAACN,SAAU;kBACxB6C,QAAQ,EAAGC,CAAC,IAAKvC,wBAAwB,CAACD,IAAI,CAACZ,GAAG,EAAEoD,CAAC,CAACC,MAAM,CAACsB,OAAO,CAAE;kBACtEL,IAAI,EAAC;gBAAO;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CACF;gBACD/C,KAAK,EAAC,WAAW;gBACjBkF,cAAc,EAAC,OAAO;gBACtB/C,EAAE,EAAE;kBAAEgD,CAAC,EAAE;gBAAE;cAAE;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAEL7B,IAAI,CAACJ,SAAS,IAAII,IAAI,CAACJ,SAAS,CAACmD,MAAM,GAAG,CAAC,iBAC1CvF,OAAA,CAAC9B,GAAG;cAACuF,EAAE,EAAE;gBAAEG,OAAO,EAAE,MAAM;gBAAE8C,QAAQ,EAAE,MAAM;gBAAEC,GAAG,EAAE;cAAI,CAAE;cAAAhD,QAAA,EACtDnB,IAAI,CAACJ,SAAS,CAACS,GAAG,CAAC,CAAC+D,QAAQ,EAAEC,KAAK,kBAClC7G,OAAA,CAACrB,IAAI;gBAEH2C,KAAK,EAAEsF,QAAS;gBAChBV,IAAI,EAAC,OAAO;gBACZE,KAAK,EAAC,SAAS;gBACfpC,OAAO,EAAC;cAAU,GAJb6C,KAAK;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKX,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC,GAnFoC7B,IAAI,CAACZ,GAAG;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoF/C,CACP;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGPrE,OAAA,CAACb,GAAG;MACFiH,KAAK,EAAC,SAAS;MACf,cAAW,KAAK;MAChB3C,EAAE,EAAE;QAAE2B,QAAQ,EAAE,OAAO;QAAE0B,MAAM,EAAE,EAAE;QAAEd,KAAK,EAAE;MAAG,CAAE;MACjDzB,OAAO,EAAEA,CAAA,KAAMpE,QAAQ,CAAC,iBAAiB,CAAE;MAAAwD,QAAA,eAE3C3D,OAAA,CAACT,GAAG;QAAA2E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNrE,OAAA,CAACnB,IAAI;MACHmC,QAAQ,EAAEA,QAAS;MACnB+F,IAAI,EAAEC,OAAO,CAAChG,QAAQ,CAAE;MACxBiG,OAAO,EAAE3D,eAAgB;MAAAK,QAAA,gBAEzB3D,OAAA,CAAClB,QAAQ;QAACyF,OAAO,EAAEA,CAAA,KAAM;UACvB,MAAM/B,IAAI,GAAGpC,SAAS,CAACuC,IAAI,CAACH,IAAI,IAAIA,IAAI,CAACZ,GAAG,KAAKV,cAAc,CAAC;UAChES,OAAO,CAACuF,GAAG,CAAC,YAAY,EAAE1E,IAAI,CAAC;UAC/Bc,eAAe,CAAC,CAAC;QACnB,CAAE;QAAAK,QAAA,gBACA3D,OAAA,CAACL,UAAU;UAAC8D,EAAE,EAAE;YAAE0D,EAAE,EAAE;UAAE;QAAE;UAAAjD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAE/B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eACXrE,OAAA,CAAClB,QAAQ;QAACyF,OAAO,EAAEhB,cAAe;QAAAI,QAAA,gBAChC3D,OAAA,CAACR,IAAI;UAACiE,EAAE,EAAE;YAAE0D,EAAE,EAAE;UAAE;QAAE;UAAAjD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,aAEzB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eACXrE,OAAA,CAAClB,QAAQ;QAACyF,OAAO,EAAEf,iBAAkB;QAACC,EAAE,EAAE;UAAE2C,KAAK,EAAE;QAAa,CAAE;QAAAzC,QAAA,gBAChE3D,OAAA,CAACP,MAAM;UAACgE,EAAE,EAAE;YAAE0D,EAAE,EAAE;UAAE;QAAE;UAAAjD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE3B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGPrE,OAAA,CAACjB,MAAM;MACLgI,IAAI,EAAEjG,gBAAiB;MACvBmG,OAAO,EAAEA,CAAA,KAAMlG,mBAAmB,CAAC,KAAK,CAAE;MAAA4C,QAAA,gBAE1C3D,OAAA,CAAChB,WAAW;QAAA2E,QAAA,EAAC;MAAgB;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC3CrE,OAAA,CAACf,aAAa;QAAA0E,QAAA,eACZ3D,OAAA,CAAC7B,UAAU;UAAAwF,QAAA,GAAC,oCACuB,EAAC/C,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEiB,KAAK,EAAC,mCACxD;QAAA;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAChBrE,OAAA,CAACd,aAAa;QAAAyE,QAAA,gBACZ3D,OAAA,CAACxB,MAAM;UAAC+F,OAAO,EAAEA,CAAA,KAAMxD,mBAAmB,CAAC,KAAK,CAAE;UAAA4C,QAAA,EAAC;QAAM;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClErE,OAAA,CAACxB,MAAM;UAAC+F,OAAO,EAAElC,gBAAiB;UAAC+D,KAAK,EAAC,OAAO;UAACpC,OAAO,EAAC,WAAW;UAAAL,QAAA,EAAC;QAErE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACnE,EAAA,CA3UID,SAAS;EAAA,QACIJ,WAAW;AAAA;AAAAuH,EAAA,GADxBnH,SAAS;AA6Uf,eAAeA,SAAS;AAAC,IAAAmH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}