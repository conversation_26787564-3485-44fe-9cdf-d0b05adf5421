[{"D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Layout.jsx": "1", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Footer.jsx": "2", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Navbar.jsx": "3", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\index.jsx": "4", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AddMenuItemForm.jsx": "5", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\data.js": "6", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Loader\\HamsterLoader.jsx": "7", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminLayout.jsx": "8", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\MenuItemCard.jsx": "9", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CustomizationDialog.jsx": "10", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\MenuItemInfoDialog.jsx": "11", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\hooks\\useCheckoutFlow.js": "12", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\CheckoutStepper.jsx": "13", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\ShippingForm.jsx": "14", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\CartItems.jsx": "15", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\OrderConfirmationDialog.jsx": "16", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\EmptyCart.jsx": "17", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\OrderReview.jsx": "18", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\PaymentForm.jsx": "19", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\OrderSummary.jsx": "20", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\CartItemCard.jsx": "21", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\QuantitySelector.jsx": "22", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartContext.jsx": "23", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\MenuPage.jsx": "24", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\LoginRegisterPage.jsx": "25", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\LandingPage.jsx": "26", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\CartPage.jsx": "27", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminDashboard.jsx": "28", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminSidebarItem.jsx": "29", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\App.jsx": "30", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\contexts\\AuthContext.jsx": "31", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\contexts\\ThemeContext.jsx": "32", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\ProtectedRoute.jsx": "33", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\AdminRoute.jsx": "34", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\NotFound.jsx": "35", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminOrders.jsx": "36", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\services\\api.js": "37", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminMenu.jsx": "38", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminStaff.jsx": "39", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminRevenue.jsx": "40", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminInventory.jsx": "41", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminAnalytics.jsx": "42", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminSettings.jsx": "43"}, {"size": 267, "mtime": 1751119077990, "results": "44", "hashOfConfig": "45"}, {"size": 2177, "mtime": 1718732233010, "results": "46", "hashOfConfig": "45"}, {"size": 10310, "mtime": 1740967359605, "results": "47", "hashOfConfig": "45"}, {"size": 667, "mtime": 1751118751479, "results": "48", "hashOfConfig": "45"}, {"size": 28729, "mtime": 1740969965671, "results": "49", "hashOfConfig": "45"}, {"size": 15625, "mtime": 1719131962620, "results": "50", "hashOfConfig": "51"}, {"size": 7287, "mtime": 1719648013413, "results": "52", "hashOfConfig": "45"}, {"size": 11529, "mtime": 1748535972727, "results": "53", "hashOfConfig": "45"}, {"size": 16320, "mtime": 1748416589579, "results": "54", "hashOfConfig": "45"}, {"size": 15729, "mtime": 1748347421811, "results": "55", "hashOfConfig": "45"}, {"size": 17262, "mtime": 1748415465876, "results": "56", "hashOfConfig": "45"}, {"size": 1264, "mtime": 1748446758125, "results": "57", "hashOfConfig": "45"}, {"size": 2282, "mtime": 1748446537646, "results": "58", "hashOfConfig": "45"}, {"size": 4159, "mtime": 1748447453623, "results": "59", "hashOfConfig": "45"}, {"size": 1381, "mtime": 1748446570917, "results": "60", "hashOfConfig": "45"}, {"size": 2411, "mtime": 1748447524156, "results": "61", "hashOfConfig": "45"}, {"size": 4200, "mtime": 1748446679307, "results": "62", "hashOfConfig": "45"}, {"size": 4545, "mtime": 1748447500939, "results": "63", "hashOfConfig": "45"}, {"size": 3968, "mtime": 1748447479142, "results": "64", "hashOfConfig": "45"}, {"size": 6117, "mtime": 1748446663887, "results": "65", "hashOfConfig": "45"}, {"size": 5962, "mtime": 1748446630811, "results": "66", "hashOfConfig": "45"}, {"size": 2875, "mtime": 1748446647615, "results": "67", "hashOfConfig": "45"}, {"size": 6372, "mtime": 1748449225106, "results": "68", "hashOfConfig": "45"}, {"size": 23673, "mtime": 1748535770633, "results": "69", "hashOfConfig": "45"}, {"size": 30203, "mtime": 1748446213378, "results": "70", "hashOfConfig": "45"}, {"size": 8884, "mtime": 1748535891279, "results": "71", "hashOfConfig": "45"}, {"size": 4319, "mtime": 1748535770633, "results": "72", "hashOfConfig": "45"}, {"size": 17679, "mtime": 1751119489559, "results": "73", "hashOfConfig": "45"}, {"size": 3968, "mtime": 1748536000400, "results": "74", "hashOfConfig": "45"}, {"size": 3114, "mtime": 1751119870153, "results": "75", "hashOfConfig": "45"}, {"size": 2866, "mtime": 1751120331862, "results": "76", "hashOfConfig": "45"}, {"size": 4659, "mtime": 1751118861617, "results": "77", "hashOfConfig": "45"}, {"size": 1304, "mtime": 1751118874069, "results": "78", "hashOfConfig": "45"}, {"size": 2446, "mtime": 1751118892270, "results": "79", "hashOfConfig": "45"}, {"size": 2171, "mtime": 1751118907909, "results": "80", "hashOfConfig": "45"}, {"size": 12970, "mtime": 1751119163816, "results": "81", "hashOfConfig": "45"}, {"size": 8091, "mtime": 1751120728887, "results": "82", "hashOfConfig": "45"}, {"size": 11435, "mtime": 1751119206170, "results": "83", "hashOfConfig": "45"}, {"size": 12187, "mtime": 1751119250641, "results": "84", "hashOfConfig": "45"}, {"size": 9293, "mtime": 1751119536470, "results": "85", "hashOfConfig": "45"}, {"size": 17641, "mtime": 1751119635741, "results": "86", "hashOfConfig": "45"}, {"size": 13713, "mtime": 1751119693629, "results": "87", "hashOfConfig": "45"}, {"size": 19416, "mtime": 1751119757842, "results": "88", "hashOfConfig": "45"}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "yuudx6", {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1b3vn63", {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Layout.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Footer.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Navbar.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\index.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AddMenuItemForm.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\data.js", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Loader\\HamsterLoader.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminLayout.jsx", ["218", "219", "220"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\MenuItemCard.jsx", ["221", "222", "223"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CustomizationDialog.jsx", ["224"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\MenuItemInfoDialog.jsx", ["225"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\hooks\\useCheckoutFlow.js", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\CheckoutStepper.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\ShippingForm.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\CartItems.jsx", ["226", "227", "228", "229"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\OrderConfirmationDialog.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\EmptyCart.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\OrderReview.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\PaymentForm.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\OrderSummary.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\CartItemCard.jsx", ["230", "231"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\QuantitySelector.jsx", ["232"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartContext.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\MenuPage.jsx", ["233", "234"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\LoginRegisterPage.jsx", ["235"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\LandingPage.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\CartPage.jsx", ["236"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminDashboard.jsx", ["237", "238", "239", "240", "241", "242", "243", "244", "245"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminSidebarItem.jsx", ["246"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\App.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\contexts\\AuthContext.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\contexts\\ThemeContext.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\ProtectedRoute.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\AdminRoute.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\NotFound.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminOrders.jsx", ["247", "248", "249"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\services\\api.js", ["250"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminMenu.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminStaff.jsx", ["251"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminRevenue.jsx", ["252", "253"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminInventory.jsx", ["254"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminAnalytics.jsx", ["255", "256", "257"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminSettings.jsx", ["258", "259", "260", "261"], [], {"ruleId": "262", "severity": 1, "message": "263", "line": 17, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 17, "endColumn": 8}, {"ruleId": "262", "severity": 1, "message": "266", "line": 20, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 20, "endColumn": 7}, {"ruleId": "262", "severity": 1, "message": "267", "line": 119, "column": 9, "nodeType": "264", "messageId": "265", "endLine": 119, "endColumn": 17}, {"ruleId": "262", "severity": 1, "message": "268", "line": 11, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 11, "endColumn": 9}, {"ruleId": "262", "severity": 1, "message": "269", "line": 44, "column": 5, "nodeType": "264", "messageId": "265", "endLine": 44, "endColumn": 13}, {"ruleId": "262", "severity": 1, "message": "270", "line": 48, "column": 5, "nodeType": "264", "messageId": "265", "endLine": 48, "endColumn": 14}, {"ruleId": "262", "severity": 1, "message": "271", "line": 5, "column": 5, "nodeType": "264", "messageId": "265", "endLine": 5, "endColumn": 16}, {"ruleId": "262", "severity": 1, "message": "271", "line": 4, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 4, "endColumn": 14}, {"ruleId": "262", "severity": 1, "message": "272", "line": 3, "column": 21, "nodeType": "264", "messageId": "265", "endLine": 3, "endColumn": 31}, {"ruleId": "262", "severity": 1, "message": "273", "line": 3, "column": 33, "nodeType": "264", "messageId": "265", "endLine": 3, "endColumn": 39}, {"ruleId": "262", "severity": 1, "message": "274", "line": 4, "column": 26, "nodeType": "264", "messageId": "265", "endLine": 4, "endColumn": 42}, {"ruleId": "262", "severity": 1, "message": "275", "line": 5, "column": 10, "nodeType": "264", "messageId": "265", "endLine": 5, "endColumn": 14}, {"ruleId": "262", "severity": 1, "message": "276", "line": 17, "column": 10, "nodeType": "264", "messageId": "265", "endLine": 17, "endColumn": 17}, {"ruleId": "262", "severity": 1, "message": "277", "line": 18, "column": 13, "nodeType": "264", "messageId": "265", "endLine": 18, "endColumn": 23}, {"ruleId": "262", "severity": 1, "message": "272", "line": 3, "column": 38, "nodeType": "264", "messageId": "265", "endLine": 3, "endColumn": 48}, {"ruleId": "262", "severity": 1, "message": "278", "line": 13, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 13, "endColumn": 19}, {"ruleId": "262", "severity": 1, "message": "279", "line": 17, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 17, "endColumn": 13}, {"ruleId": "262", "severity": 1, "message": "280", "line": 98, "column": 11, "nodeType": "264", "messageId": "265", "endLine": 98, "endColumn": 20}, {"ruleId": "262", "severity": 1, "message": "281", "line": 1, "column": 29, "nodeType": "264", "messageId": "265", "endLine": 1, "endColumn": 37}, {"ruleId": "262", "severity": 1, "message": "282", "line": 11, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 11, "endColumn": 9}, {"ruleId": "262", "severity": 1, "message": "283", "line": 12, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 12, "endColumn": 13}, {"ruleId": "262", "severity": 1, "message": "284", "line": 23, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 23, "endColumn": 9}, {"ruleId": "262", "severity": 1, "message": "285", "line": 28, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 28, "endColumn": 12}, {"ruleId": "262", "severity": 1, "message": "286", "line": 30, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 30, "endColumn": 11}, {"ruleId": "262", "severity": 1, "message": "287", "line": 34, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 34, "endColumn": 11}, {"ruleId": "262", "severity": 1, "message": "288", "line": 37, "column": 8, "nodeType": "264", "messageId": "265", "endLine": 37, "endColumn": 19}, {"ruleId": "262", "severity": 1, "message": "289", "line": 190, "column": 9, "nodeType": "264", "messageId": "265", "endLine": 190, "endColumn": 14}, {"ruleId": "262", "severity": 1, "message": "290", "line": 194, "column": 10, "nodeType": "264", "messageId": "265", "endLine": 194, "endColumn": 21}, {"ruleId": "262", "severity": 1, "message": "289", "line": 91, "column": 9, "nodeType": "264", "messageId": "265", "endLine": 91, "endColumn": 14}, {"ruleId": "262", "severity": 1, "message": "263", "line": 25, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 25, "endColumn": 8}, {"ruleId": "262", "severity": 1, "message": "291", "line": 34, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 34, "endColumn": 7}, {"ruleId": "262", "severity": 1, "message": "292", "line": 35, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 35, "endColumn": 9}, {"ruleId": "293", "severity": 1, "message": "294", "line": 310, "column": 1, "nodeType": "295", "endLine": 318, "endColumn": 3}, {"ruleId": "262", "severity": 1, "message": "296", "line": 34, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 34, "endColumn": 9}, {"ruleId": "262", "severity": 1, "message": "263", "line": 10, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 10, "endColumn": 8}, {"ruleId": "297", "severity": 1, "message": "298", "line": 46, "column": 6, "nodeType": "299", "endLine": 46, "endColumn": 14, "suggestions": "300"}, {"ruleId": "262", "severity": 1, "message": "301", "line": 49, "column": 10, "nodeType": "264", "messageId": "265", "endLine": 49, "endColumn": 24}, {"ruleId": "262", "severity": 1, "message": "263", "line": 10, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 10, "endColumn": 8}, {"ruleId": "262", "severity": 1, "message": "302", "line": 29, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 29, "endColumn": 12}, {"ruleId": "297", "severity": 1, "message": "303", "line": 48, "column": 6, "nodeType": "299", "endLine": 48, "endColumn": 14, "suggestions": "304"}, {"ruleId": "262", "severity": 1, "message": "263", "line": 16, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 16, "endColumn": 8}, {"ruleId": "262", "severity": 1, "message": "283", "line": 21, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 21, "endColumn": 13}, {"ruleId": "262", "severity": 1, "message": "305", "line": 34, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 34, "endColumn": 8}, {"ruleId": "262", "severity": 1, "message": "291", "line": 37, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 37, "endColumn": 7}, "no-unused-vars", "'Paper' is defined but never used.", "Identifier", "unusedVar", "'Chip' is defined but never used.", "'isMobile' is assigned a value but never used.", "'Rating' is defined but never used.", "'calories' is assigned a value but never used.", "'allergens' is assigned a value but never used.", "'DialogTitle' is defined but never used.", "'Typography' is defined but never used.", "'Button' is defined but never used.", "'ShoppingCartIcon' is defined but never used.", "'Link' is defined but never used.", "'AddIcon' is defined but never used.", "'RemoveIcon' is defined but never used.", "'CircularProgress' is defined but never used.", "'InputLabel' is defined but never used.", "'errorList' is assigned a value but never used.", "'useState' is defined but never used.", "'Avatar' is defined but never used.", "'IconButton' is defined but never used.", "'People' is defined but never used.", "'PersonAdd' is defined but never used.", "'Schedule' is defined but never used.", "'MoreVert' is defined but never used.", "'AdminLayout' is defined but never used.", "'theme' is assigned a value but never used.", "'lastUpdated' is assigned a value but never used.", "'Edit' is defined but never used.", "'Delete' is defined but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'Person' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchRevenueData'. Either include it or remove the dependency array.", "ArrayExpression", ["306"], "'editDialogOpen' is assigned a value but never used.", "'DateRange' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchAnalyticsData'. Either include it or remove the dependency array.", ["307"], "'Email' is defined but never used.", {"desc": "308", "fix": "309"}, {"desc": "310", "fix": "311"}, "Update the dependencies array to be: [fetchRevenueData, period]", {"range": "312", "text": "313"}, "Update the dependencies array to be: [fetchAnalyticsData, period]", {"range": "314", "text": "315"}, [935, 943], "[fetchRevenueData, period]", [964, 972], "[fetchAnalyticsData, period]"]