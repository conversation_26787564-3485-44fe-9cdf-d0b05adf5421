[{"D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Layout.jsx": "1", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Footer.jsx": "2", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Navbar.jsx": "3", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\index.jsx": "4", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AddMenuItemForm.jsx": "5", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\data.js": "6", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Loader\\HamsterLoader.jsx": "7", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminLayout.jsx": "8", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\MenuItemCard.jsx": "9", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CustomizationDialog.jsx": "10", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\MenuItemInfoDialog.jsx": "11", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\hooks\\useCheckoutFlow.js": "12", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\CheckoutStepper.jsx": "13", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\ShippingForm.jsx": "14", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\CartItems.jsx": "15", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\OrderConfirmationDialog.jsx": "16", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\EmptyCart.jsx": "17", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\OrderReview.jsx": "18", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\PaymentForm.jsx": "19", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\OrderSummary.jsx": "20", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\CartItemCard.jsx": "21", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\QuantitySelector.jsx": "22", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartContext.jsx": "23", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\MenuPage.jsx": "24", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\LoginRegisterPage.jsx": "25", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\LandingPage.jsx": "26", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\CartPage.jsx": "27", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminDashboard.jsx": "28", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminSidebarItem.jsx": "29", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\App.jsx": "30", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\contexts\\AuthContext.jsx": "31", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\contexts\\ThemeContext.jsx": "32", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\ProtectedRoute.jsx": "33", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\AdminRoute.jsx": "34", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\NotFound.jsx": "35", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminOrders.jsx": "36", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\services\\api.js": "37", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminMenu.jsx": "38", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminStaff.jsx": "39", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminRevenue.jsx": "40", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminInventory.jsx": "41", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminAnalytics.jsx": "42", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminSettings.jsx": "43", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\CartCustomizationDialog.jsx": "44"}, {"size": 267, "mtime": 1751119077990, "results": "45", "hashOfConfig": "46"}, {"size": 2177, "mtime": 1718732233010, "results": "47", "hashOfConfig": "46"}, {"size": 10310, "mtime": 1740967359605, "results": "48", "hashOfConfig": "46"}, {"size": 667, "mtime": 1751118751479, "results": "49", "hashOfConfig": "46"}, {"size": 28729, "mtime": 1740969965671, "results": "50", "hashOfConfig": "46"}, {"size": 15625, "mtime": 1719131962620, "results": "51", "hashOfConfig": "52"}, {"size": 7287, "mtime": 1719648013413, "results": "53", "hashOfConfig": "46"}, {"size": 13249, "mtime": 1751128027928, "results": "54", "hashOfConfig": "46"}, {"size": 16320, "mtime": 1748416589579, "results": "55", "hashOfConfig": "46"}, {"size": 15729, "mtime": 1748347421811, "results": "56", "hashOfConfig": "46"}, {"size": 17262, "mtime": 1748415465876, "results": "57", "hashOfConfig": "46"}, {"size": 1264, "mtime": 1748446758125, "results": "58", "hashOfConfig": "46"}, {"size": 2282, "mtime": 1748446537646, "results": "59", "hashOfConfig": "46"}, {"size": 4159, "mtime": 1748447453623, "results": "60", "hashOfConfig": "46"}, {"size": 1463, "mtime": 1751124719324, "results": "61", "hashOfConfig": "46"}, {"size": 2411, "mtime": 1748447524156, "results": "62", "hashOfConfig": "46"}, {"size": 4200, "mtime": 1748446679307, "results": "63", "hashOfConfig": "46"}, {"size": 4545, "mtime": 1748447500939, "results": "64", "hashOfConfig": "46"}, {"size": 3968, "mtime": 1748447479142, "results": "65", "hashOfConfig": "46"}, {"size": 6117, "mtime": 1748446663887, "results": "66", "hashOfConfig": "46"}, {"size": 7975, "mtime": 1751124799596, "results": "67", "hashOfConfig": "46"}, {"size": 2875, "mtime": 1748446647615, "results": "68", "hashOfConfig": "46"}, {"size": 6372, "mtime": 1748449225106, "results": "69", "hashOfConfig": "46"}, {"size": 23673, "mtime": 1748535770633, "results": "70", "hashOfConfig": "46"}, {"size": 31494, "mtime": 1751124510912, "results": "71", "hashOfConfig": "46"}, {"size": 8884, "mtime": 1748535891279, "results": "72", "hashOfConfig": "46"}, {"size": 4522, "mtime": 1751124773943, "results": "73", "hashOfConfig": "46"}, {"size": 18330, "mtime": 1751128212141, "results": "74", "hashOfConfig": "46"}, {"size": 3968, "mtime": 1748536000400, "results": "75", "hashOfConfig": "46"}, {"size": 3114, "mtime": 1751119870153, "results": "76", "hashOfConfig": "46"}, {"size": 4223, "mtime": 1751124453582, "results": "77", "hashOfConfig": "46"}, {"size": 4659, "mtime": 1751118861617, "results": "78", "hashOfConfig": "46"}, {"size": 1304, "mtime": 1751118874069, "results": "79", "hashOfConfig": "46"}, {"size": 2446, "mtime": 1751118892270, "results": "80", "hashOfConfig": "46"}, {"size": 2171, "mtime": 1751118907909, "results": "81", "hashOfConfig": "46"}, {"size": 12312, "mtime": 1751128274032, "results": "82", "hashOfConfig": "46"}, {"size": 10010, "mtime": 1751128103675, "results": "83", "hashOfConfig": "46"}, {"size": 10832, "mtime": 1751128320550, "results": "84", "hashOfConfig": "46"}, {"size": 12187, "mtime": 1751119250641, "results": "85", "hashOfConfig": "46"}, {"size": 9293, "mtime": 1751119536470, "results": "86", "hashOfConfig": "46"}, {"size": 17641, "mtime": 1751119635741, "results": "87", "hashOfConfig": "46"}, {"size": 13713, "mtime": 1751119693629, "results": "88", "hashOfConfig": "46"}, {"size": 19416, "mtime": 1751119757842, "results": "89", "hashOfConfig": "46"}, {"size": 9344, "mtime": 1751124622983, "results": "90", "hashOfConfig": "46"}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "yuudx6", {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1b3vn63", {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Layout.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Footer.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Navbar.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\index.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AddMenuItemForm.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\data.js", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Loader\\HamsterLoader.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminLayout.jsx", ["223", "224", "225"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\MenuItemCard.jsx", ["226", "227", "228"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CustomizationDialog.jsx", ["229"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\MenuItemInfoDialog.jsx", ["230"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\hooks\\useCheckoutFlow.js", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\CheckoutStepper.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\ShippingForm.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\CartItems.jsx", ["231", "232", "233", "234"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\OrderConfirmationDialog.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\EmptyCart.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\OrderReview.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\PaymentForm.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\OrderSummary.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\CartItemCard.jsx", ["235", "236", "237", "238"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\QuantitySelector.jsx", ["239"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartContext.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\MenuPage.jsx", ["240", "241"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\LoginRegisterPage.jsx", ["242", "243", "244", "245"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\LandingPage.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\CartPage.jsx", ["246"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminDashboard.jsx", ["247", "248", "249", "250", "251", "252", "253", "254", "255"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminSidebarItem.jsx", ["256"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\App.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\contexts\\AuthContext.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\contexts\\ThemeContext.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\ProtectedRoute.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\AdminRoute.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\NotFound.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminOrders.jsx", ["257", "258", "259", "260"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\services\\api.js", ["261"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminMenu.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminStaff.jsx", ["262"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminRevenue.jsx", ["263", "264"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminInventory.jsx", ["265"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminAnalytics.jsx", ["266", "267", "268"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminSettings.jsx", ["269", "270", "271", "272"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\CartCustomizationDialog.jsx", ["273", "274", "275"], [], {"ruleId": "276", "severity": 1, "message": "277", "line": 18, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 18, "endColumn": 8}, {"ruleId": "276", "severity": 1, "message": "280", "line": 21, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 21, "endColumn": 7}, {"ruleId": "276", "severity": 1, "message": "281", "line": 124, "column": 9, "nodeType": "278", "messageId": "279", "endLine": 124, "endColumn": 17}, {"ruleId": "276", "severity": 1, "message": "282", "line": 11, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 11, "endColumn": 9}, {"ruleId": "276", "severity": 1, "message": "283", "line": 44, "column": 5, "nodeType": "278", "messageId": "279", "endLine": 44, "endColumn": 13}, {"ruleId": "276", "severity": 1, "message": "284", "line": 48, "column": 5, "nodeType": "278", "messageId": "279", "endLine": 48, "endColumn": 14}, {"ruleId": "276", "severity": 1, "message": "285", "line": 5, "column": 5, "nodeType": "278", "messageId": "279", "endLine": 5, "endColumn": 16}, {"ruleId": "276", "severity": 1, "message": "285", "line": 4, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 4, "endColumn": 14}, {"ruleId": "276", "severity": 1, "message": "286", "line": 3, "column": 21, "nodeType": "278", "messageId": "279", "endLine": 3, "endColumn": 31}, {"ruleId": "276", "severity": 1, "message": "287", "line": 3, "column": 33, "nodeType": "278", "messageId": "279", "endLine": 3, "endColumn": 39}, {"ruleId": "276", "severity": 1, "message": "288", "line": 4, "column": 26, "nodeType": "278", "messageId": "279", "endLine": 4, "endColumn": 42}, {"ruleId": "276", "severity": 1, "message": "289", "line": 5, "column": 10, "nodeType": "278", "messageId": "279", "endLine": 5, "endColumn": 14}, {"ruleId": "276", "severity": 1, "message": "287", "line": 14, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 14, "endColumn": 9}, {"ruleId": "276", "severity": 1, "message": "290", "line": 18, "column": 10, "nodeType": "278", "messageId": "279", "endLine": 18, "endColumn": 17}, {"ruleId": "276", "severity": 1, "message": "291", "line": 19, "column": 13, "nodeType": "278", "messageId": "279", "endLine": 19, "endColumn": 23}, {"ruleId": "276", "severity": 1, "message": "292", "line": 20, "column": 11, "nodeType": "278", "messageId": "279", "endLine": 20, "endColumn": 19}, {"ruleId": "276", "severity": 1, "message": "286", "line": 3, "column": 38, "nodeType": "278", "messageId": "279", "endLine": 3, "endColumn": 48}, {"ruleId": "276", "severity": 1, "message": "293", "line": 13, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 13, "endColumn": 19}, {"ruleId": "276", "severity": 1, "message": "294", "line": 17, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 17, "endColumn": 13}, {"ruleId": "276", "severity": 1, "message": "295", "line": 99, "column": 11, "nodeType": "278", "messageId": "279", "endLine": 99, "endColumn": 20}, {"ruleId": "296", "severity": 1, "message": "297", "line": 236, "column": 29, "nodeType": "298", "messageId": "299", "endLine": 236, "endColumn": 30, "suggestions": "300"}, {"ruleId": "296", "severity": 1, "message": "301", "line": 237, "column": 59, "nodeType": "298", "messageId": "299", "endLine": 237, "endColumn": 60, "suggestions": "302"}, {"ruleId": "296", "severity": 1, "message": "303", "line": 237, "column": 61, "nodeType": "298", "messageId": "299", "endLine": 237, "endColumn": 62, "suggestions": "304"}, {"ruleId": "276", "severity": 1, "message": "305", "line": 1, "column": 29, "nodeType": "278", "messageId": "279", "endLine": 1, "endColumn": 37}, {"ruleId": "276", "severity": 1, "message": "306", "line": 11, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 11, "endColumn": 9}, {"ruleId": "276", "severity": 1, "message": "307", "line": 12, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 12, "endColumn": 13}, {"ruleId": "276", "severity": 1, "message": "308", "line": 23, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 23, "endColumn": 9}, {"ruleId": "276", "severity": 1, "message": "309", "line": 28, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 28, "endColumn": 12}, {"ruleId": "276", "severity": 1, "message": "310", "line": 34, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 34, "endColumn": 11}, {"ruleId": "276", "severity": 1, "message": "311", "line": 37, "column": 8, "nodeType": "278", "messageId": "279", "endLine": 37, "endColumn": 19}, {"ruleId": "276", "severity": 1, "message": "312", "line": 38, "column": 35, "nodeType": "278", "messageId": "279", "endLine": 38, "endColumn": 42}, {"ruleId": "276", "severity": 1, "message": "313", "line": 190, "column": 9, "nodeType": "278", "messageId": "279", "endLine": 190, "endColumn": 14}, {"ruleId": "276", "severity": 1, "message": "314", "line": 194, "column": 10, "nodeType": "278", "messageId": "279", "endLine": 194, "endColumn": 21}, {"ruleId": "276", "severity": 1, "message": "313", "line": 91, "column": 9, "nodeType": "278", "messageId": "279", "endLine": 91, "endColumn": 14}, {"ruleId": "276", "severity": 1, "message": "277", "line": 25, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 25, "endColumn": 8}, {"ruleId": "276", "severity": 1, "message": "315", "line": 34, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 34, "endColumn": 7}, {"ruleId": "276", "severity": 1, "message": "316", "line": 35, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 35, "endColumn": 9}, {"ruleId": "317", "severity": 1, "message": "318", "line": 65, "column": 6, "nodeType": "319", "endLine": 65, "endColumn": 20, "suggestions": "320"}, {"ruleId": null, "fatal": true, "severity": 2, "message": "321", "line": 245, "column": 13, "nodeType": null}, {"ruleId": "276", "severity": 1, "message": "322", "line": 34, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 34, "endColumn": 9}, {"ruleId": "276", "severity": 1, "message": "277", "line": 10, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 10, "endColumn": 8}, {"ruleId": "317", "severity": 1, "message": "323", "line": 46, "column": 6, "nodeType": "319", "endLine": 46, "endColumn": 14, "suggestions": "324"}, {"ruleId": "276", "severity": 1, "message": "325", "line": 49, "column": 10, "nodeType": "278", "messageId": "279", "endLine": 49, "endColumn": 24}, {"ruleId": "276", "severity": 1, "message": "277", "line": 10, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 10, "endColumn": 8}, {"ruleId": "276", "severity": 1, "message": "326", "line": 29, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 29, "endColumn": 12}, {"ruleId": "317", "severity": 1, "message": "327", "line": 48, "column": 6, "nodeType": "319", "endLine": 48, "endColumn": 14, "suggestions": "328"}, {"ruleId": "276", "severity": 1, "message": "277", "line": 16, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 16, "endColumn": 8}, {"ruleId": "276", "severity": 1, "message": "307", "line": 21, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 21, "endColumn": 13}, {"ruleId": "276", "severity": 1, "message": "329", "line": 34, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 34, "endColumn": 8}, {"ruleId": "276", "severity": 1, "message": "315", "line": 37, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 37, "endColumn": 7}, {"ruleId": "276", "severity": 1, "message": "280", "line": 17, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 17, "endColumn": 7}, {"ruleId": "276", "severity": 1, "message": "330", "line": 19, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 19, "endColumn": 8}, {"ruleId": "317", "severity": 1, "message": "331", "line": 46, "column": 6, "nodeType": "319", "endLine": 46, "endColumn": 18, "suggestions": "332"}, "no-unused-vars", "'Paper' is defined but never used.", "Identifier", "unusedVar", "'Chip' is defined but never used.", "'isMobile' is assigned a value but never used.", "'Rating' is defined but never used.", "'calories' is assigned a value but never used.", "'allergens' is assigned a value but never used.", "'DialogTitle' is defined but never used.", "'Typography' is defined but never used.", "'Button' is defined but never used.", "'ShoppingCartIcon' is defined but never used.", "'Link' is defined but never used.", "'AddIcon' is defined but never used.", "'RemoveIcon' is defined but never used.", "'EditIcon' is defined but never used.", "'CircularProgress' is defined but never used.", "'InputLabel' is defined but never used.", "'errorList' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\+.", "Literal", "unnecessaryEscape", ["333", "334"], "Unnecessary escape character: \\(.", ["335", "336"], "Unnecessary escape character: \\).", ["337", "338"], "'useState' is defined but never used.", "'Avatar' is defined but never used.", "'IconButton' is defined but never used.", "'People' is defined but never used.", "'PersonAdd' is defined but never used.", "'MoreVert' is defined but never used.", "'AdminLayout' is defined but never used.", "'menuAPI' is defined but never used.", "'theme' is assigned a value but never used.", "'lastUpdated' is assigned a value but never used.", "'Edit' is defined but never used.", "'Delete' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchOrders'. Either include it or remove the dependency array.", "ArrayExpression", ["339"], "Parsing error: Identifier 'ordersAPI' has already been declared. (245:13)", "'Person' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchRevenueData'. Either include it or remove the dependency array.", ["340"], "'editDialogOpen' is assigned a value but never used.", "'DateRange' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchAnalyticsData'. Either include it or remove the dependency array.", ["341"], "'Email' is defined but never used.", "'Alert' is defined but never used.", "React Hook useEffect has a missing dependency: 'calculateTotalPrice'. Either include it or remove the dependency array.", ["342"], {"messageId": "343", "fix": "344", "desc": "345"}, {"messageId": "346", "fix": "347", "desc": "348"}, {"messageId": "343", "fix": "349", "desc": "345"}, {"messageId": "346", "fix": "350", "desc": "348"}, {"messageId": "343", "fix": "351", "desc": "345"}, {"messageId": "346", "fix": "352", "desc": "348"}, {"desc": "353", "fix": "354"}, {"desc": "355", "fix": "356"}, {"desc": "357", "fix": "358"}, {"desc": "359", "fix": "360"}, "removeEscape", {"range": "361", "text": "362"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "363", "text": "364"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "365", "text": "362"}, {"range": "366", "text": "364"}, {"range": "367", "text": "362"}, {"range": "368", "text": "364"}, "Update the dependencies array to be: [fetchOrders, statusFilter]", {"range": "369", "text": "370"}, "Update the dependencies array to be: [fetchRevenueData, period]", {"range": "371", "text": "372"}, "Update the dependencies array to be: [fetchAnalyticsData, period]", {"range": "373", "text": "374"}, "Update the dependencies array to be: [open, item, calculateTotalPrice]", {"range": "375", "text": "376"}, [6618, 6619], "", [6618, 6618], "\\", [6700, 6701], [6700, 6700], [6702, 6703], [6702, 6702], [1480, 1494], "[fetchOrde<PERSON>, statusFilter]", [935, 943], "[fetchRevenueData, period]", [964, 972], "[fetchAnalyticsData, period]", [1145, 1157], "[open, item, calculateTotalPrice]"]