[{"D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Layout.jsx": "1", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Footer.jsx": "2", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Navbar.jsx": "3", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\index.jsx": "4", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AddMenuItemForm.jsx": "5", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\data.js": "6", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Loader\\HamsterLoader.jsx": "7", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminLayout.jsx": "8", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\MenuItemCard.jsx": "9", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CustomizationDialog.jsx": "10", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\MenuItemInfoDialog.jsx": "11", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\hooks\\useCheckoutFlow.js": "12", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\CheckoutStepper.jsx": "13", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\ShippingForm.jsx": "14", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\CartItems.jsx": "15", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\OrderConfirmationDialog.jsx": "16", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\EmptyCart.jsx": "17", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\OrderReview.jsx": "18", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\PaymentForm.jsx": "19", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\OrderSummary.jsx": "20", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\CartItemCard.jsx": "21", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\QuantitySelector.jsx": "22", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartContext.jsx": "23", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\MenuPage.jsx": "24", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\LoginRegisterPage.jsx": "25", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\LandingPage.jsx": "26", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\CartPage.jsx": "27", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminDashboard.jsx": "28", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminSidebarItem.jsx": "29", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\App.jsx": "30", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\contexts\\AuthContext.jsx": "31", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\contexts\\ThemeContext.jsx": "32", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\ProtectedRoute.jsx": "33", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\AdminRoute.jsx": "34", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\NotFound.jsx": "35", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminOrders.jsx": "36", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\services\\api.js": "37", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminMenu.jsx": "38", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminStaff.jsx": "39", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminRevenue.jsx": "40", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminInventory.jsx": "41", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminAnalytics.jsx": "42", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminSettings.jsx": "43", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\CartCustomizationDialog.jsx": "44"}, {"size": 267, "mtime": 1751119077990, "results": "45", "hashOfConfig": "46"}, {"size": 2177, "mtime": 1718732233010, "results": "47", "hashOfConfig": "46"}, {"size": 10310, "mtime": 1740967359605, "results": "48", "hashOfConfig": "46"}, {"size": 667, "mtime": 1751118751479, "results": "49", "hashOfConfig": "46"}, {"size": 28729, "mtime": 1740969965671, "results": "50", "hashOfConfig": "46"}, {"size": 15625, "mtime": 1719131962620, "results": "51", "hashOfConfig": "52"}, {"size": 7287, "mtime": 1719648013413, "results": "53", "hashOfConfig": "46"}, {"size": 11529, "mtime": 1748535972727, "results": "54", "hashOfConfig": "46"}, {"size": 16320, "mtime": 1748416589579, "results": "55", "hashOfConfig": "46"}, {"size": 15729, "mtime": 1748347421811, "results": "56", "hashOfConfig": "46"}, {"size": 17262, "mtime": 1748415465876, "results": "57", "hashOfConfig": "46"}, {"size": 1264, "mtime": 1748446758125, "results": "58", "hashOfConfig": "46"}, {"size": 2282, "mtime": 1748446537646, "results": "59", "hashOfConfig": "46"}, {"size": 4159, "mtime": 1748447453623, "results": "60", "hashOfConfig": "46"}, {"size": 1463, "mtime": 1751124719324, "results": "61", "hashOfConfig": "46"}, {"size": 2411, "mtime": 1748447524156, "results": "62", "hashOfConfig": "46"}, {"size": 4200, "mtime": 1748446679307, "results": "63", "hashOfConfig": "46"}, {"size": 4545, "mtime": 1748447500939, "results": "64", "hashOfConfig": "46"}, {"size": 3968, "mtime": 1748447479142, "results": "65", "hashOfConfig": "46"}, {"size": 6117, "mtime": 1748446663887, "results": "66", "hashOfConfig": "46"}, {"size": 7975, "mtime": 1751124799596, "results": "67", "hashOfConfig": "46"}, {"size": 2875, "mtime": 1748446647615, "results": "68", "hashOfConfig": "46"}, {"size": 6372, "mtime": 1748449225106, "results": "69", "hashOfConfig": "46"}, {"size": 23673, "mtime": 1748535770633, "results": "70", "hashOfConfig": "46"}, {"size": 31494, "mtime": 1751124510912, "results": "71", "hashOfConfig": "46"}, {"size": 8884, "mtime": 1748535891279, "results": "72", "hashOfConfig": "46"}, {"size": 4522, "mtime": 1751124773943, "results": "73", "hashOfConfig": "46"}, {"size": 17679, "mtime": 1751119489559, "results": "74", "hashOfConfig": "46"}, {"size": 3968, "mtime": 1748536000400, "results": "75", "hashOfConfig": "46"}, {"size": 3114, "mtime": 1751119870153, "results": "76", "hashOfConfig": "46"}, {"size": 4223, "mtime": 1751124453582, "results": "77", "hashOfConfig": "46"}, {"size": 4659, "mtime": 1751118861617, "results": "78", "hashOfConfig": "46"}, {"size": 1304, "mtime": 1751118874069, "results": "79", "hashOfConfig": "46"}, {"size": 2446, "mtime": 1751118892270, "results": "80", "hashOfConfig": "46"}, {"size": 2171, "mtime": 1751118907909, "results": "81", "hashOfConfig": "46"}, {"size": 12970, "mtime": 1751119163816, "results": "82", "hashOfConfig": "46"}, {"size": 8091, "mtime": 1751120728887, "results": "83", "hashOfConfig": "46"}, {"size": 11435, "mtime": 1751119206170, "results": "84", "hashOfConfig": "46"}, {"size": 12187, "mtime": 1751119250641, "results": "85", "hashOfConfig": "46"}, {"size": 9293, "mtime": 1751119536470, "results": "86", "hashOfConfig": "46"}, {"size": 17641, "mtime": 1751119635741, "results": "87", "hashOfConfig": "46"}, {"size": 13713, "mtime": 1751119693629, "results": "88", "hashOfConfig": "46"}, {"size": 19416, "mtime": 1751119757842, "results": "89", "hashOfConfig": "46"}, {"size": 9344, "mtime": 1751124622983, "results": "90", "hashOfConfig": "46"}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "yuudx6", {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1b3vn63", {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Layout.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Footer.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Navbar.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\index.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AddMenuItemForm.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\data.js", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Loader\\HamsterLoader.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminLayout.jsx", ["223", "224", "225"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\MenuItemCard.jsx", ["226", "227", "228"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CustomizationDialog.jsx", ["229"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\MenuItemInfoDialog.jsx", ["230"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\hooks\\useCheckoutFlow.js", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\CheckoutStepper.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\ShippingForm.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\CartItems.jsx", ["231", "232", "233", "234"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\OrderConfirmationDialog.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\EmptyCart.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\OrderReview.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\PaymentForm.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\OrderSummary.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\CartItemCard.jsx", ["235", "236", "237", "238"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\QuantitySelector.jsx", ["239"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartContext.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\MenuPage.jsx", ["240", "241"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\LoginRegisterPage.jsx", ["242", "243", "244", "245"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\LandingPage.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\CartPage.jsx", ["246"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminDashboard.jsx", ["247", "248", "249", "250", "251", "252", "253", "254", "255"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminSidebarItem.jsx", ["256"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\App.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\contexts\\AuthContext.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\contexts\\ThemeContext.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\ProtectedRoute.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\AdminRoute.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\NotFound.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminOrders.jsx", ["257", "258", "259"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\services\\api.js", ["260"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminMenu.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminStaff.jsx", ["261"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminRevenue.jsx", ["262", "263"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminInventory.jsx", ["264"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminAnalytics.jsx", ["265", "266", "267"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminSettings.jsx", ["268", "269", "270", "271"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\CartCustomizationDialog.jsx", ["272", "273", "274"], [], {"ruleId": "275", "severity": 1, "message": "276", "line": 17, "column": 3, "nodeType": "277", "messageId": "278", "endLine": 17, "endColumn": 8}, {"ruleId": "275", "severity": 1, "message": "279", "line": 20, "column": 3, "nodeType": "277", "messageId": "278", "endLine": 20, "endColumn": 7}, {"ruleId": "275", "severity": 1, "message": "280", "line": 119, "column": 9, "nodeType": "277", "messageId": "278", "endLine": 119, "endColumn": 17}, {"ruleId": "275", "severity": 1, "message": "281", "line": 11, "column": 3, "nodeType": "277", "messageId": "278", "endLine": 11, "endColumn": 9}, {"ruleId": "275", "severity": 1, "message": "282", "line": 44, "column": 5, "nodeType": "277", "messageId": "278", "endLine": 44, "endColumn": 13}, {"ruleId": "275", "severity": 1, "message": "283", "line": 48, "column": 5, "nodeType": "277", "messageId": "278", "endLine": 48, "endColumn": 14}, {"ruleId": "275", "severity": 1, "message": "284", "line": 5, "column": 5, "nodeType": "277", "messageId": "278", "endLine": 5, "endColumn": 16}, {"ruleId": "275", "severity": 1, "message": "284", "line": 4, "column": 3, "nodeType": "277", "messageId": "278", "endLine": 4, "endColumn": 14}, {"ruleId": "275", "severity": 1, "message": "285", "line": 3, "column": 21, "nodeType": "277", "messageId": "278", "endLine": 3, "endColumn": 31}, {"ruleId": "275", "severity": 1, "message": "286", "line": 3, "column": 33, "nodeType": "277", "messageId": "278", "endLine": 3, "endColumn": 39}, {"ruleId": "275", "severity": 1, "message": "287", "line": 4, "column": 26, "nodeType": "277", "messageId": "278", "endLine": 4, "endColumn": 42}, {"ruleId": "275", "severity": 1, "message": "288", "line": 5, "column": 10, "nodeType": "277", "messageId": "278", "endLine": 5, "endColumn": 14}, {"ruleId": "275", "severity": 1, "message": "286", "line": 14, "column": 3, "nodeType": "277", "messageId": "278", "endLine": 14, "endColumn": 9}, {"ruleId": "275", "severity": 1, "message": "289", "line": 18, "column": 10, "nodeType": "277", "messageId": "278", "endLine": 18, "endColumn": 17}, {"ruleId": "275", "severity": 1, "message": "290", "line": 19, "column": 13, "nodeType": "277", "messageId": "278", "endLine": 19, "endColumn": 23}, {"ruleId": "275", "severity": 1, "message": "291", "line": 20, "column": 11, "nodeType": "277", "messageId": "278", "endLine": 20, "endColumn": 19}, {"ruleId": "275", "severity": 1, "message": "285", "line": 3, "column": 38, "nodeType": "277", "messageId": "278", "endLine": 3, "endColumn": 48}, {"ruleId": "275", "severity": 1, "message": "292", "line": 13, "column": 3, "nodeType": "277", "messageId": "278", "endLine": 13, "endColumn": 19}, {"ruleId": "275", "severity": 1, "message": "293", "line": 17, "column": 3, "nodeType": "277", "messageId": "278", "endLine": 17, "endColumn": 13}, {"ruleId": "275", "severity": 1, "message": "294", "line": 99, "column": 11, "nodeType": "277", "messageId": "278", "endLine": 99, "endColumn": 20}, {"ruleId": "295", "severity": 1, "message": "296", "line": 236, "column": 29, "nodeType": "297", "messageId": "298", "endLine": 236, "endColumn": 30, "suggestions": "299"}, {"ruleId": "295", "severity": 1, "message": "300", "line": 237, "column": 59, "nodeType": "297", "messageId": "298", "endLine": 237, "endColumn": 60, "suggestions": "301"}, {"ruleId": "295", "severity": 1, "message": "302", "line": 237, "column": 61, "nodeType": "297", "messageId": "298", "endLine": 237, "endColumn": 62, "suggestions": "303"}, {"ruleId": "275", "severity": 1, "message": "304", "line": 1, "column": 29, "nodeType": "277", "messageId": "278", "endLine": 1, "endColumn": 37}, {"ruleId": "275", "severity": 1, "message": "305", "line": 11, "column": 3, "nodeType": "277", "messageId": "278", "endLine": 11, "endColumn": 9}, {"ruleId": "275", "severity": 1, "message": "306", "line": 12, "column": 3, "nodeType": "277", "messageId": "278", "endLine": 12, "endColumn": 13}, {"ruleId": "275", "severity": 1, "message": "307", "line": 23, "column": 3, "nodeType": "277", "messageId": "278", "endLine": 23, "endColumn": 9}, {"ruleId": "275", "severity": 1, "message": "308", "line": 28, "column": 3, "nodeType": "277", "messageId": "278", "endLine": 28, "endColumn": 12}, {"ruleId": "275", "severity": 1, "message": "309", "line": 30, "column": 3, "nodeType": "277", "messageId": "278", "endLine": 30, "endColumn": 11}, {"ruleId": "275", "severity": 1, "message": "310", "line": 34, "column": 3, "nodeType": "277", "messageId": "278", "endLine": 34, "endColumn": 11}, {"ruleId": "275", "severity": 1, "message": "311", "line": 37, "column": 8, "nodeType": "277", "messageId": "278", "endLine": 37, "endColumn": 19}, {"ruleId": "275", "severity": 1, "message": "312", "line": 190, "column": 9, "nodeType": "277", "messageId": "278", "endLine": 190, "endColumn": 14}, {"ruleId": "275", "severity": 1, "message": "313", "line": 194, "column": 10, "nodeType": "277", "messageId": "278", "endLine": 194, "endColumn": 21}, {"ruleId": "275", "severity": 1, "message": "312", "line": 91, "column": 9, "nodeType": "277", "messageId": "278", "endLine": 91, "endColumn": 14}, {"ruleId": "275", "severity": 1, "message": "276", "line": 25, "column": 3, "nodeType": "277", "messageId": "278", "endLine": 25, "endColumn": 8}, {"ruleId": "275", "severity": 1, "message": "314", "line": 34, "column": 3, "nodeType": "277", "messageId": "278", "endLine": 34, "endColumn": 7}, {"ruleId": "275", "severity": 1, "message": "315", "line": 35, "column": 3, "nodeType": "277", "messageId": "278", "endLine": 35, "endColumn": 9}, {"ruleId": "316", "severity": 1, "message": "317", "line": 310, "column": 1, "nodeType": "318", "endLine": 318, "endColumn": 3}, {"ruleId": "275", "severity": 1, "message": "319", "line": 34, "column": 3, "nodeType": "277", "messageId": "278", "endLine": 34, "endColumn": 9}, {"ruleId": "275", "severity": 1, "message": "276", "line": 10, "column": 3, "nodeType": "277", "messageId": "278", "endLine": 10, "endColumn": 8}, {"ruleId": "320", "severity": 1, "message": "321", "line": 46, "column": 6, "nodeType": "322", "endLine": 46, "endColumn": 14, "suggestions": "323"}, {"ruleId": "275", "severity": 1, "message": "324", "line": 49, "column": 10, "nodeType": "277", "messageId": "278", "endLine": 49, "endColumn": 24}, {"ruleId": "275", "severity": 1, "message": "276", "line": 10, "column": 3, "nodeType": "277", "messageId": "278", "endLine": 10, "endColumn": 8}, {"ruleId": "275", "severity": 1, "message": "325", "line": 29, "column": 3, "nodeType": "277", "messageId": "278", "endLine": 29, "endColumn": 12}, {"ruleId": "320", "severity": 1, "message": "326", "line": 48, "column": 6, "nodeType": "322", "endLine": 48, "endColumn": 14, "suggestions": "327"}, {"ruleId": "275", "severity": 1, "message": "276", "line": 16, "column": 3, "nodeType": "277", "messageId": "278", "endLine": 16, "endColumn": 8}, {"ruleId": "275", "severity": 1, "message": "306", "line": 21, "column": 3, "nodeType": "277", "messageId": "278", "endLine": 21, "endColumn": 13}, {"ruleId": "275", "severity": 1, "message": "328", "line": 34, "column": 3, "nodeType": "277", "messageId": "278", "endLine": 34, "endColumn": 8}, {"ruleId": "275", "severity": 1, "message": "314", "line": 37, "column": 3, "nodeType": "277", "messageId": "278", "endLine": 37, "endColumn": 7}, {"ruleId": "275", "severity": 1, "message": "279", "line": 17, "column": 3, "nodeType": "277", "messageId": "278", "endLine": 17, "endColumn": 7}, {"ruleId": "275", "severity": 1, "message": "329", "line": 19, "column": 3, "nodeType": "277", "messageId": "278", "endLine": 19, "endColumn": 8}, {"ruleId": "320", "severity": 1, "message": "330", "line": 46, "column": 6, "nodeType": "322", "endLine": 46, "endColumn": 18, "suggestions": "331"}, "no-unused-vars", "'Paper' is defined but never used.", "Identifier", "unusedVar", "'Chip' is defined but never used.", "'isMobile' is assigned a value but never used.", "'Rating' is defined but never used.", "'calories' is assigned a value but never used.", "'allergens' is assigned a value but never used.", "'DialogTitle' is defined but never used.", "'Typography' is defined but never used.", "'Button' is defined but never used.", "'ShoppingCartIcon' is defined but never used.", "'Link' is defined but never used.", "'AddIcon' is defined but never used.", "'RemoveIcon' is defined but never used.", "'EditIcon' is defined but never used.", "'CircularProgress' is defined but never used.", "'InputLabel' is defined but never used.", "'errorList' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\+.", "Literal", "unnecessaryEscape", ["332", "333"], "Unnecessary escape character: \\(.", ["334", "335"], "Unnecessary escape character: \\).", ["336", "337"], "'useState' is defined but never used.", "'Avatar' is defined but never used.", "'IconButton' is defined but never used.", "'People' is defined but never used.", "'PersonAdd' is defined but never used.", "'Schedule' is defined but never used.", "'MoreVert' is defined but never used.", "'AdminLayout' is defined but never used.", "'theme' is assigned a value but never used.", "'lastUpdated' is assigned a value but never used.", "'Edit' is defined but never used.", "'Delete' is defined but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'Person' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchRevenueData'. Either include it or remove the dependency array.", "ArrayExpression", ["338"], "'editDialogOpen' is assigned a value but never used.", "'DateRange' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchAnalyticsData'. Either include it or remove the dependency array.", ["339"], "'Email' is defined but never used.", "'Alert' is defined but never used.", "React Hook useEffect has a missing dependency: 'calculateTotalPrice'. Either include it or remove the dependency array.", ["340"], {"messageId": "341", "fix": "342", "desc": "343"}, {"messageId": "344", "fix": "345", "desc": "346"}, {"messageId": "341", "fix": "347", "desc": "343"}, {"messageId": "344", "fix": "348", "desc": "346"}, {"messageId": "341", "fix": "349", "desc": "343"}, {"messageId": "344", "fix": "350", "desc": "346"}, {"desc": "351", "fix": "352"}, {"desc": "353", "fix": "354"}, {"desc": "355", "fix": "356"}, "removeEscape", {"range": "357", "text": "358"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "359", "text": "360"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "361", "text": "358"}, {"range": "362", "text": "360"}, {"range": "363", "text": "358"}, {"range": "364", "text": "360"}, "Update the dependencies array to be: [fetchRevenueData, period]", {"range": "365", "text": "366"}, "Update the dependencies array to be: [fetchAnalyticsData, period]", {"range": "367", "text": "368"}, "Update the dependencies array to be: [open, item, calculateTotalPrice]", {"range": "369", "text": "370"}, [6618, 6619], "", [6618, 6618], "\\", [6700, 6701], [6700, 6700], [6702, 6703], [6702, 6702], [935, 943], "[fetchRevenueData, period]", [964, 972], "[fetchAnalyticsData, period]", [1145, 1157], "[open, item, calculateTotalPrice]"]