[{"D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Layout.jsx": "1", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Footer.jsx": "2", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Navbar.jsx": "3", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\index.jsx": "4", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AddMenuItemForm.jsx": "5", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\data.js": "6", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Loader\\HamsterLoader.jsx": "7", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminLayout.jsx": "8", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\MenuItemCard.jsx": "9", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CustomizationDialog.jsx": "10", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\MenuItemInfoDialog.jsx": "11", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\hooks\\useCheckoutFlow.js": "12", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\CheckoutStepper.jsx": "13", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\ShippingForm.jsx": "14", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\CartItems.jsx": "15", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\OrderConfirmationDialog.jsx": "16", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\EmptyCart.jsx": "17", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\OrderReview.jsx": "18", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\PaymentForm.jsx": "19", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\OrderSummary.jsx": "20", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\CartItemCard.jsx": "21", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\QuantitySelector.jsx": "22", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartContext.jsx": "23", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\MenuPage.jsx": "24", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\LoginRegisterPage.jsx": "25", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\LandingPage.jsx": "26", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\CartPage.jsx": "27", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminDashboard.jsx": "28", "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminSidebarItem.jsx": "29"}, {"size": 8144, "mtime": 1748415960085, "results": "30", "hashOfConfig": "31"}, {"size": 2177, "mtime": 1718732233010, "results": "32", "hashOfConfig": "31"}, {"size": 10310, "mtime": 1740967359605, "results": "33", "hashOfConfig": "31"}, {"size": 1437, "mtime": 1751118629961, "results": "34", "hashOfConfig": "31"}, {"size": 28729, "mtime": 1740969965671, "results": "35", "hashOfConfig": "31"}, {"size": 15625, "mtime": 1719131962620, "results": "36", "hashOfConfig": "37"}, {"size": 7287, "mtime": 1719648013413, "results": "38", "hashOfConfig": "31"}, {"size": 11529, "mtime": 1748535972727, "results": "39", "hashOfConfig": "31"}, {"size": 16320, "mtime": 1748416589579, "results": "40", "hashOfConfig": "31"}, {"size": 15729, "mtime": 1748347421811, "results": "41", "hashOfConfig": "31"}, {"size": 17262, "mtime": 1748415465876, "results": "42", "hashOfConfig": "31"}, {"size": 1264, "mtime": 1748446758125, "results": "43", "hashOfConfig": "31"}, {"size": 2282, "mtime": 1748446537646, "results": "44", "hashOfConfig": "31"}, {"size": 4159, "mtime": 1748447453623, "results": "45", "hashOfConfig": "31"}, {"size": 1381, "mtime": 1748446570917, "results": "46", "hashOfConfig": "31"}, {"size": 2411, "mtime": 1748447524156, "results": "47", "hashOfConfig": "31"}, {"size": 4200, "mtime": 1748446679307, "results": "48", "hashOfConfig": "31"}, {"size": 4545, "mtime": 1748447500939, "results": "49", "hashOfConfig": "31"}, {"size": 3968, "mtime": 1748447479142, "results": "50", "hashOfConfig": "31"}, {"size": 6117, "mtime": 1748446663887, "results": "51", "hashOfConfig": "31"}, {"size": 5962, "mtime": 1748446630811, "results": "52", "hashOfConfig": "31"}, {"size": 2875, "mtime": 1748446647615, "results": "53", "hashOfConfig": "31"}, {"size": 6372, "mtime": 1748449225106, "results": "54", "hashOfConfig": "31"}, {"size": 23673, "mtime": 1748535770633, "results": "55", "hashOfConfig": "31"}, {"size": 30203, "mtime": 1748446213378, "results": "56", "hashOfConfig": "31"}, {"size": 8884, "mtime": 1748535891279, "results": "57", "hashOfConfig": "31"}, {"size": 4319, "mtime": 1748535770633, "results": "58", "hashOfConfig": "31"}, {"size": 15258, "mtime": 1751118311298, "results": "59", "hashOfConfig": "31"}, {"size": 3968, "mtime": 1748536000400, "results": "60", "hashOfConfig": "31"}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "yuudx6", {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1b3vn63", {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Layout.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Footer.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Navbar.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\index.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AddMenuItemForm.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\data.js", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\Loader\\HamsterLoader.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminLayout.jsx", ["148", "149", "150"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\MenuItemCard.jsx", ["151", "152", "153"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CustomizationDialog.jsx", ["154"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\MenuItemInfoDialog.jsx", ["155"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\hooks\\useCheckoutFlow.js", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\CheckoutStepper.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\ShippingForm.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\CartItems.jsx", ["156", "157", "158", "159"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\OrderConfirmationDialog.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\EmptyCart.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\OrderReview.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\PaymentForm.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\OrderSummary.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\CartItemCard.jsx", ["160", "161"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartComponents\\QuantitySelector.jsx", ["162"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\components\\CartContext.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\MenuPage.jsx", ["163", "164"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\LoginRegisterPage.jsx", ["165"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\LandingPage.jsx", [], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\customer\\CartPage.jsx", ["166"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminDashboard.jsx", ["167", "168", "169", "170", "171", "172", "173", "174"], [], "D:\\Github_Repos\\cafe-mangement-system-react\\cafe-management-sys\\src\\pages\\admin\\AdminSidebarItem.jsx", ["175"], [], {"ruleId": "176", "severity": 1, "message": "177", "line": 17, "column": 3, "nodeType": "178", "messageId": "179", "endLine": 17, "endColumn": 8}, {"ruleId": "176", "severity": 1, "message": "180", "line": 20, "column": 3, "nodeType": "178", "messageId": "179", "endLine": 20, "endColumn": 7}, {"ruleId": "176", "severity": 1, "message": "181", "line": 119, "column": 9, "nodeType": "178", "messageId": "179", "endLine": 119, "endColumn": 17}, {"ruleId": "176", "severity": 1, "message": "182", "line": 11, "column": 3, "nodeType": "178", "messageId": "179", "endLine": 11, "endColumn": 9}, {"ruleId": "176", "severity": 1, "message": "183", "line": 44, "column": 5, "nodeType": "178", "messageId": "179", "endLine": 44, "endColumn": 13}, {"ruleId": "176", "severity": 1, "message": "184", "line": 48, "column": 5, "nodeType": "178", "messageId": "179", "endLine": 48, "endColumn": 14}, {"ruleId": "176", "severity": 1, "message": "185", "line": 5, "column": 5, "nodeType": "178", "messageId": "179", "endLine": 5, "endColumn": 16}, {"ruleId": "176", "severity": 1, "message": "185", "line": 4, "column": 3, "nodeType": "178", "messageId": "179", "endLine": 4, "endColumn": 14}, {"ruleId": "176", "severity": 1, "message": "186", "line": 3, "column": 21, "nodeType": "178", "messageId": "179", "endLine": 3, "endColumn": 31}, {"ruleId": "176", "severity": 1, "message": "187", "line": 3, "column": 33, "nodeType": "178", "messageId": "179", "endLine": 3, "endColumn": 39}, {"ruleId": "176", "severity": 1, "message": "188", "line": 4, "column": 26, "nodeType": "178", "messageId": "179", "endLine": 4, "endColumn": 42}, {"ruleId": "176", "severity": 1, "message": "189", "line": 5, "column": 10, "nodeType": "178", "messageId": "179", "endLine": 5, "endColumn": 14}, {"ruleId": "176", "severity": 1, "message": "190", "line": 17, "column": 10, "nodeType": "178", "messageId": "179", "endLine": 17, "endColumn": 17}, {"ruleId": "176", "severity": 1, "message": "191", "line": 18, "column": 13, "nodeType": "178", "messageId": "179", "endLine": 18, "endColumn": 23}, {"ruleId": "176", "severity": 1, "message": "186", "line": 3, "column": 38, "nodeType": "178", "messageId": "179", "endLine": 3, "endColumn": 48}, {"ruleId": "176", "severity": 1, "message": "192", "line": 13, "column": 3, "nodeType": "178", "messageId": "179", "endLine": 13, "endColumn": 19}, {"ruleId": "176", "severity": 1, "message": "193", "line": 17, "column": 3, "nodeType": "178", "messageId": "179", "endLine": 17, "endColumn": 13}, {"ruleId": "176", "severity": 1, "message": "194", "line": 98, "column": 11, "nodeType": "178", "messageId": "179", "endLine": 98, "endColumn": 20}, {"ruleId": "176", "severity": 1, "message": "195", "line": 1, "column": 29, "nodeType": "178", "messageId": "179", "endLine": 1, "endColumn": 37}, {"ruleId": "176", "severity": 1, "message": "196", "line": 11, "column": 3, "nodeType": "178", "messageId": "179", "endLine": 11, "endColumn": 9}, {"ruleId": "176", "severity": 1, "message": "197", "line": 12, "column": 3, "nodeType": "178", "messageId": "179", "endLine": 12, "endColumn": 13}, {"ruleId": "176", "severity": 1, "message": "198", "line": 23, "column": 3, "nodeType": "178", "messageId": "179", "endLine": 23, "endColumn": 9}, {"ruleId": "176", "severity": 1, "message": "199", "line": 28, "column": 3, "nodeType": "178", "messageId": "179", "endLine": 28, "endColumn": 12}, {"ruleId": "176", "severity": 1, "message": "200", "line": 30, "column": 3, "nodeType": "178", "messageId": "179", "endLine": 30, "endColumn": 11}, {"ruleId": "176", "severity": 1, "message": "201", "line": 34, "column": 3, "nodeType": "178", "messageId": "179", "endLine": 34, "endColumn": 11}, {"ruleId": "176", "severity": 1, "message": "202", "line": 37, "column": 8, "nodeType": "178", "messageId": "179", "endLine": 37, "endColumn": 19}, {"ruleId": "176", "severity": 1, "message": "203", "line": 195, "column": 9, "nodeType": "178", "messageId": "179", "endLine": 195, "endColumn": 14}, {"ruleId": "176", "severity": 1, "message": "203", "line": 91, "column": 9, "nodeType": "178", "messageId": "179", "endLine": 91, "endColumn": 14}, "no-unused-vars", "'Paper' is defined but never used.", "Identifier", "unusedVar", "'Chip' is defined but never used.", "'isMobile' is assigned a value but never used.", "'Rating' is defined but never used.", "'calories' is assigned a value but never used.", "'allergens' is assigned a value but never used.", "'DialogTitle' is defined but never used.", "'Typography' is defined but never used.", "'Button' is defined but never used.", "'ShoppingCartIcon' is defined but never used.", "'Link' is defined but never used.", "'AddIcon' is defined but never used.", "'RemoveIcon' is defined but never used.", "'CircularProgress' is defined but never used.", "'InputLabel' is defined but never used.", "'errorList' is assigned a value but never used.", "'useState' is defined but never used.", "'Avatar' is defined but never used.", "'IconButton' is defined but never used.", "'People' is defined but never used.", "'PersonAdd' is defined but never used.", "'Schedule' is defined but never used.", "'MoreVert' is defined but never used.", "'AdminLayout' is defined but never used.", "'theme' is assigned a value but never used."]