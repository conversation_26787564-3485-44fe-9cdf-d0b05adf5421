{"ast": null, "code": "var _jsxFileName = \"D:\\\\Github_Repos\\\\cafe-mangement-system-react\\\\cafe-management-sys\\\\src\\\\pages\\\\admin\\\\AdminMenu.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Card, CardContent, CardMedia, Grid, Button, TextField, InputAdornment, Chip, IconButton, Menu, MenuItem, Dialog, DialogTitle, DialogContent, DialogActions, Fab, Switch, FormControlLabel } from '@mui/material';\nimport { Search, Add, Edit, Delete, MoreVert, Visibility, FilterList } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { menuAPI } from '../../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminMenu = () => {\n  _s();\n  const navigate = useNavigate();\n  const [menuItems, setMenuItems] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [categoryFilter, setCategoryFilter] = useState('all');\n  const [selectedItem, setSelectedItem] = useState(null);\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [selectedItemId, setSelectedItemId] = useState(null);\n  const categories = [{\n    value: 'all',\n    label: 'All Categories'\n  }, {\n    value: 'coffee',\n    label: 'Coffee'\n  }, {\n    value: 'tea',\n    label: 'Tea'\n  }, {\n    value: 'pastries',\n    label: 'Pastries'\n  }, {\n    value: 'sandwiches',\n    label: 'Sandwiches'\n  }, {\n    value: 'desserts',\n    label: 'Desserts'\n  }];\n  useEffect(() => {\n    fetchMenuItems();\n  }, []);\n  const fetchMenuItems = async () => {\n    try {\n      setLoading(true);\n      const response = await menuAPI.getAll();\n      const menuData = (response === null || response === void 0 ? void 0 : response.data) || response || [];\n      setMenuItems(Array.isArray(menuData) ? menuData : []);\n    } catch (error) {\n      console.error('Error fetching menu items:', error);\n      setMenuItems([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteItem = async () => {\n    try {\n      await menuAPI.delete(selectedItem._id);\n      setMenuItems(menuItems.filter(item => item._id !== selectedItem._id));\n      setDeleteDialogOpen(false);\n      setSelectedItem(null);\n    } catch (error) {\n      console.error('Error deleting menu item:', error);\n    }\n  };\n  const handleToggleAvailability = async (itemId, available) => {\n    try {\n      const item = menuItems.find(item => item._id === itemId);\n      await menuAPI.update(itemId, {\n        ...item,\n        available\n      });\n      setMenuItems(menuItems.map(item => item._id === itemId ? {\n        ...item,\n        available\n      } : item));\n    } catch (error) {\n      console.error('Error updating item availability:', error);\n    }\n  };\n  const filteredItems = menuItems.filter(item => {\n    const matchesSearch = item.title.toLowerCase().includes(searchQuery.toLowerCase()) || item.description.toLowerCase().includes(searchQuery.toLowerCase());\n    const matchesCategory = categoryFilter === 'all' || item.category === categoryFilter;\n    return matchesSearch && matchesCategory;\n  });\n  const handleMenuClick = (event, itemId) => {\n    setAnchorEl(event.currentTarget);\n    setSelectedItemId(itemId);\n  };\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n    setSelectedItemId(null);\n  };\n  const handleEditItem = () => {\n    navigate(`/admin/menu/edit/${selectedItemId}`);\n    handleMenuClose();\n  };\n  const handleDeleteClick = () => {\n    const item = menuItems.find(item => item._id === selectedItemId);\n    setSelectedItem(item);\n    setDeleteDialogOpen(true);\n    handleMenuClose();\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          fontWeight: 600\n        },\n        children: \"Menu Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(Add, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 22\n        }, this),\n        onClick: () => navigate('/admin/menu/add'),\n        sx: {\n          borderRadius: 2\n        },\n        children: \"Add New Item\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              placeholder: \"Search menu items...\",\n              value: searchQuery,\n              onChange: e => setSearchQuery(e.target.value),\n              InputProps: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(Search, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 155,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 21\n                }, this)\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              select: true,\n              label: \"Category\",\n              value: categoryFilter,\n              onChange: e => setCategoryFilter(e.target.value),\n              children: categories.map(category => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: category.value,\n                children: category.label\n              }, category.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 2,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              fullWidth: true,\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(FilterList, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 28\n              }, this),\n              children: \"Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: loading ? /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          align: \"center\",\n          children: \"Loading menu items...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 11\n      }, this) : filteredItems.length === 0 ? /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          align: \"center\",\n          children: \"No menu items found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 11\n      }, this) : filteredItems.map(item => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        lg: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column',\n            position: 'relative',\n            opacity: item.available ? 1 : 0.7\n          },\n          children: [/*#__PURE__*/_jsxDEV(CardMedia, {\n            component: \"img\",\n            height: \"200\",\n            image: item.image || '/api/placeholder/300/200',\n            alt: item.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              position: 'absolute',\n              top: 8,\n              right: 8\n            },\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: e => handleMenuClick(e, item._id),\n              sx: {\n                bgcolor: 'background.paper',\n                '&:hover': {\n                  bgcolor: 'background.paper'\n                }\n              },\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(MoreVert, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              flexGrow: 1,\n              display: 'flex',\n              flexDirection: 'column'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'flex-start',\n                mb: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                component: \"h3\",\n                sx: {\n                  fontWeight: 600\n                },\n                children: item.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: item.category,\n                size: \"small\",\n                color: \"primary\",\n                variant: \"outlined\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              sx: {\n                mb: 2,\n                flexGrow: 1\n              },\n              children: item.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                color: \"primary\",\n                sx: {\n                  fontWeight: 600\n                },\n                children: [\"$\", item.price.toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                control: /*#__PURE__*/_jsxDEV(Switch, {\n                  checked: item.available,\n                  onChange: e => handleToggleAvailability(item._id, e.target.checked),\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 25\n                }, this),\n                label: \"Available\",\n                labelPlacement: \"start\",\n                sx: {\n                  m: 0\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 19\n            }, this), item.allergens && item.allergens.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexWrap: 'wrap',\n                gap: 0.5\n              },\n              children: item.allergens.map((allergen, index) => /*#__PURE__*/_jsxDEV(Chip, {\n                label: allergen,\n                size: \"small\",\n                color: \"warning\",\n                variant: \"outlined\"\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 25\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 15\n        }, this)\n      }, item._id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Fab, {\n      color: \"primary\",\n      \"aria-label\": \"add\",\n      sx: {\n        position: 'fixed',\n        bottom: 16,\n        right: 16\n      },\n      onClick: () => navigate('/admin/menu/add'),\n      children: /*#__PURE__*/_jsxDEV(Add, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Menu, {\n      anchorEl: anchorEl,\n      open: Boolean(anchorEl),\n      onClose: handleMenuClose,\n      children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => {\n          const item = menuItems.find(item => item._id === selectedItemId);\n          console.log('View item:', item);\n          handleMenuClose();\n        },\n        children: [/*#__PURE__*/_jsxDEV(Visibility, {\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this), \"View Details\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: handleEditItem,\n        children: [/*#__PURE__*/_jsxDEV(Edit, {\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this), \"Edit Item\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: handleDeleteClick,\n        sx: {\n          color: 'error.main'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Delete, {\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this), \"Delete Item\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: deleteDialogOpen,\n      onClose: () => setDeleteDialogOpen(false),\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Delete Menu Item\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          children: [\"Are you sure you want to delete \\\"\", selectedItem === null || selectedItem === void 0 ? void 0 : selectedItem.title, \"\\\"? This action cannot be undone.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setDeleteDialogOpen(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDeleteItem,\n          color: \"error\",\n          variant: \"contained\",\n          children: \"Delete\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 127,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminMenu, \"qCRZqbE3wh21f1tAsWhaZ8CJeGQ=\", false, function () {\n  return [useNavigate];\n});\n_c = AdminMenu;\nexport default AdminMenu;\nvar _c;\n$RefreshReg$(_c, \"AdminMenu\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardMedia", "Grid", "<PERSON><PERSON>", "TextField", "InputAdornment", "Chip", "IconButton", "<PERSON><PERSON>", "MenuItem", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Fab", "Switch", "FormControlLabel", "Search", "Add", "Edit", "Delete", "<PERSON><PERSON><PERSON>", "Visibility", "FilterList", "useNavigate", "menuAPI", "jsxDEV", "_jsxDEV", "AdminMenu", "_s", "navigate", "menuItems", "setMenuItems", "loading", "setLoading", "searchQuery", "setSearch<PERSON>uery", "categoryFilter", "setCate<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedItem", "setSelectedItem", "deleteDialogOpen", "setDeleteDialogOpen", "anchorEl", "setAnchorEl", "selectedItemId", "setSelectedItemId", "categories", "value", "label", "fetchMenuItems", "response", "getAll", "menuData", "data", "Array", "isArray", "error", "console", "handleDeleteItem", "delete", "_id", "filter", "item", "handleToggleAvailability", "itemId", "available", "find", "update", "map", "filteredItems", "matchesSearch", "title", "toLowerCase", "includes", "description", "matchesCategory", "category", "handleMenuClick", "event", "currentTarget", "handleMenuClose", "handleEditItem", "handleDeleteClick", "sx", "p", "children", "display", "justifyContent", "alignItems", "mb", "variant", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "startIcon", "onClick", "borderRadius", "container", "spacing", "xs", "md", "fullWidth", "placeholder", "onChange", "e", "target", "InputProps", "startAdornment", "position", "select", "align", "length", "sm", "lg", "height", "flexDirection", "opacity", "component", "image", "alt", "top", "right", "bgcolor", "size", "flexGrow", "color", "price", "toFixed", "control", "checked", "labelPlacement", "m", "allergens", "flexWrap", "gap", "allergen", "index", "bottom", "open", "Boolean", "onClose", "log", "mr", "_c", "$RefreshReg$"], "sources": ["D:/Github_Repos/cafe-mangement-system-react/cafe-management-sys/src/pages/admin/AdminMenu.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typo<PERSON>,\n  Card,\n  CardContent,\n  CardMedia,\n  Grid,\n  Button,\n  TextField,\n  InputAdornment,\n  Chip,\n  IconButton,\n  Menu,\n  MenuItem,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Fab,\n  Switch,\n  FormControlLabel,\n} from '@mui/material';\nimport {\n  Search,\n  Add,\n  Edit,\n  Delete,\n  MoreVert,\n  Visibility,\n  FilterList,\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { menuAPI } from '../../services/api';\n\nconst AdminMenu = () => {\n  const navigate = useNavigate();\n  const [menuItems, setMenuItems] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [categoryFilter, setCategoryFilter] = useState('all');\n  const [selectedItem, setSelectedItem] = useState(null);\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [selectedItemId, setSelectedItemId] = useState(null);\n\n  const categories = [\n    { value: 'all', label: 'All Categories' },\n    { value: 'coffee', label: 'Coffee' },\n    { value: 'tea', label: 'Tea' },\n    { value: 'pastries', label: 'Pastries' },\n    { value: 'sandwiches', label: 'Sandwiches' },\n    { value: 'desserts', label: 'Desserts' },\n  ];\n\n  useEffect(() => {\n    fetchMenuItems();\n  }, []);\n\n  const fetchMenuItems = async () => {\n    try {\n      setLoading(true);\n      const response = await menuAPI.getAll();\n      const menuData = response?.data || response || [];\n      setMenuItems(Array.isArray(menuData) ? menuData : []);\n    } catch (error) {\n      console.error('Error fetching menu items:', error);\n      setMenuItems([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteItem = async () => {\n    try {\n      await menuAPI.delete(selectedItem._id);\n      setMenuItems(menuItems.filter(item => item._id !== selectedItem._id));\n      setDeleteDialogOpen(false);\n      setSelectedItem(null);\n    } catch (error) {\n      console.error('Error deleting menu item:', error);\n    }\n  };\n\n  const handleToggleAvailability = async (itemId, available) => {\n    try {\n      const item = menuItems.find(item => item._id === itemId);\n      await menuAPI.update(itemId, { ...item, available });\n      setMenuItems(menuItems.map(item => \n        item._id === itemId ? { ...item, available } : item\n      ));\n    } catch (error) {\n      console.error('Error updating item availability:', error);\n    }\n  };\n\n  const filteredItems = menuItems.filter(item => {\n    const matchesSearch = item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                         item.description.toLowerCase().includes(searchQuery.toLowerCase());\n    const matchesCategory = categoryFilter === 'all' || item.category === categoryFilter;\n    return matchesSearch && matchesCategory;\n  });\n\n  const handleMenuClick = (event, itemId) => {\n    setAnchorEl(event.currentTarget);\n    setSelectedItemId(itemId);\n  };\n\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n    setSelectedItemId(null);\n  };\n\n  const handleEditItem = () => {\n    navigate(`/admin/menu/edit/${selectedItemId}`);\n    handleMenuClose();\n  };\n\n  const handleDeleteClick = () => {\n    const item = menuItems.find(item => item._id === selectedItemId);\n    setSelectedItem(item);\n    setDeleteDialogOpen(true);\n    handleMenuClose();\n  };\n\n  return (\n    <Box sx={{ p: 3 }}>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h4\" sx={{ fontWeight: 600 }}>\n          Menu Management\n        </Typography>\n        <Button\n          variant=\"contained\"\n          startIcon={<Add />}\n          onClick={() => navigate('/admin/menu/add')}\n          sx={{ borderRadius: 2 }}\n        >\n          Add New Item\n        </Button>\n      </Box>\n\n      {/* Search and Filters */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Grid container spacing={2} alignItems=\"center\">\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                placeholder=\"Search menu items...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                InputProps={{\n                  startAdornment: (\n                    <InputAdornment position=\"start\">\n                      <Search />\n                    </InputAdornment>\n                  ),\n                }}\n              />\n            </Grid>\n            <Grid item xs={12} md={4}>\n              <TextField\n                fullWidth\n                select\n                label=\"Category\"\n                value={categoryFilter}\n                onChange={(e) => setCategoryFilter(e.target.value)}\n              >\n                {categories.map((category) => (\n                  <MenuItem key={category.value} value={category.value}>\n                    {category.label}\n                  </MenuItem>\n                ))}\n              </TextField>\n            </Grid>\n            <Grid item xs={12} md={2}>\n              <Button\n                fullWidth\n                variant=\"outlined\"\n                startIcon={<FilterList />}\n              >\n                Filters\n              </Button>\n            </Grid>\n          </Grid>\n        </CardContent>\n      </Card>\n\n      {/* Menu Items Grid */}\n      <Grid container spacing={3}>\n        {loading ? (\n          <Grid item xs={12}>\n            <Typography align=\"center\">Loading menu items...</Typography>\n          </Grid>\n        ) : filteredItems.length === 0 ? (\n          <Grid item xs={12}>\n            <Typography align=\"center\">No menu items found</Typography>\n          </Grid>\n        ) : (\n          filteredItems.map((item) => (\n            <Grid item xs={12} sm={6} md={4} lg={3} key={item._id}>\n              <Card \n                sx={{ \n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  position: 'relative',\n                  opacity: item.available ? 1 : 0.7,\n                }}\n              >\n                <CardMedia\n                  component=\"img\"\n                  height=\"200\"\n                  image={item.image || '/api/placeholder/300/200'}\n                  alt={item.title}\n                />\n                \n                <Box sx={{ position: 'absolute', top: 8, right: 8 }}>\n                  <IconButton\n                    onClick={(e) => handleMenuClick(e, item._id)}\n                    sx={{ \n                      bgcolor: 'background.paper',\n                      '&:hover': { bgcolor: 'background.paper' }\n                    }}\n                    size=\"small\"\n                  >\n                    <MoreVert />\n                  </IconButton>\n                </Box>\n\n                <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>\n                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>\n                    <Typography variant=\"h6\" component=\"h3\" sx={{ fontWeight: 600 }}>\n                      {item.title}\n                    </Typography>\n                    <Chip\n                      label={item.category}\n                      size=\"small\"\n                      color=\"primary\"\n                      variant=\"outlined\"\n                    />\n                  </Box>\n                  \n                  <Typography \n                    variant=\"body2\" \n                    color=\"text.secondary\" \n                    sx={{ mb: 2, flexGrow: 1 }}\n                  >\n                    {item.description}\n                  </Typography>\n\n                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n                    <Typography variant=\"h6\" color=\"primary\" sx={{ fontWeight: 600 }}>\n                      ${item.price.toFixed(2)}\n                    </Typography>\n                    <FormControlLabel\n                      control={\n                        <Switch\n                          checked={item.available}\n                          onChange={(e) => handleToggleAvailability(item._id, e.target.checked)}\n                          size=\"small\"\n                        />\n                      }\n                      label=\"Available\"\n                      labelPlacement=\"start\"\n                      sx={{ m: 0 }}\n                    />\n                  </Box>\n\n                  {item.allergens && item.allergens.length > 0 && (\n                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>\n                      {item.allergens.map((allergen, index) => (\n                        <Chip\n                          key={index}\n                          label={allergen}\n                          size=\"small\"\n                          color=\"warning\"\n                          variant=\"outlined\"\n                        />\n                      ))}\n                    </Box>\n                  )}\n                </CardContent>\n              </Card>\n            </Grid>\n          ))\n        )}\n      </Grid>\n\n      {/* Floating Action Button */}\n      <Fab\n        color=\"primary\"\n        aria-label=\"add\"\n        sx={{ position: 'fixed', bottom: 16, right: 16 }}\n        onClick={() => navigate('/admin/menu/add')}\n      >\n        <Add />\n      </Fab>\n\n      {/* Action Menu */}\n      <Menu\n        anchorEl={anchorEl}\n        open={Boolean(anchorEl)}\n        onClose={handleMenuClose}\n      >\n        <MenuItem onClick={() => {\n          const item = menuItems.find(item => item._id === selectedItemId);\n          console.log('View item:', item);\n          handleMenuClose();\n        }}>\n          <Visibility sx={{ mr: 1 }} />\n          View Details\n        </MenuItem>\n        <MenuItem onClick={handleEditItem}>\n          <Edit sx={{ mr: 1 }} />\n          Edit Item\n        </MenuItem>\n        <MenuItem onClick={handleDeleteClick} sx={{ color: 'error.main' }}>\n          <Delete sx={{ mr: 1 }} />\n          Delete Item\n        </MenuItem>\n      </Menu>\n\n      {/* Delete Confirmation Dialog */}\n      <Dialog\n        open={deleteDialogOpen}\n        onClose={() => setDeleteDialogOpen(false)}\n      >\n        <DialogTitle>Delete Menu Item</DialogTitle>\n        <DialogContent>\n          <Typography>\n            Are you sure you want to delete \"{selectedItem?.title}\"? This action cannot be undone.\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>\n          <Button onClick={handleDeleteItem} color=\"error\" variant=\"contained\">\n            Delete\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default AdminMenu;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,SAAS,EACTC,IAAI,EACJC,MAAM,EACNC,SAAS,EACTC,cAAc,EACdC,IAAI,EACJC,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,GAAG,EACHC,MAAM,EACNC,gBAAgB,QACX,eAAe;AACtB,SACEC,MAAM,EACNC,GAAG,EACHC,IAAI,EACJC,MAAM,EACNC,QAAQ,EACRC,UAAU,EACVC,UAAU,QACL,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACO,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwC,WAAW,EAAEC,cAAc,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC0C,cAAc,EAAEC,iBAAiB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC8C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACgD,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACkD,cAAc,EAAEC,iBAAiB,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EAE1D,MAAMoD,UAAU,GAAG,CACjB;IAAEC,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAiB,CAAC,EACzC;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC,EACpC;IAAED,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAM,CAAC,EAC9B;IAAED,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAW,CAAC,EACxC;IAAED,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAa,CAAC,EAC5C;IAAED,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAW,CAAC,CACzC;EAEDrD,SAAS,CAAC,MAAM;IACdsD,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFhB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMiB,QAAQ,GAAG,MAAM1B,OAAO,CAAC2B,MAAM,CAAC,CAAC;MACvC,MAAMC,QAAQ,GAAG,CAAAF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEG,IAAI,KAAIH,QAAQ,IAAI,EAAE;MACjDnB,YAAY,CAACuB,KAAK,CAACC,OAAO,CAACH,QAAQ,CAAC,GAAGA,QAAQ,GAAG,EAAE,CAAC;IACvD,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDzB,YAAY,CAAC,EAAE,CAAC;IAClB,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMyB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMlC,OAAO,CAACmC,MAAM,CAACrB,YAAY,CAACsB,GAAG,CAAC;MACtC7B,YAAY,CAACD,SAAS,CAAC+B,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACF,GAAG,KAAKtB,YAAY,CAACsB,GAAG,CAAC,CAAC;MACrEnB,mBAAmB,CAAC,KAAK,CAAC;MAC1BF,eAAe,CAAC,IAAI,CAAC;IACvB,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;EACF,CAAC;EAED,MAAMO,wBAAwB,GAAG,MAAAA,CAAOC,MAAM,EAAEC,SAAS,KAAK;IAC5D,IAAI;MACF,MAAMH,IAAI,GAAGhC,SAAS,CAACoC,IAAI,CAACJ,IAAI,IAAIA,IAAI,CAACF,GAAG,KAAKI,MAAM,CAAC;MACxD,MAAMxC,OAAO,CAAC2C,MAAM,CAACH,MAAM,EAAE;QAAE,GAAGF,IAAI;QAAEG;MAAU,CAAC,CAAC;MACpDlC,YAAY,CAACD,SAAS,CAACsC,GAAG,CAACN,IAAI,IAC7BA,IAAI,CAACF,GAAG,KAAKI,MAAM,GAAG;QAAE,GAAGF,IAAI;QAAEG;MAAU,CAAC,GAAGH,IACjD,CAAC,CAAC;IACJ,CAAC,CAAC,OAAON,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IAC3D;EACF,CAAC;EAED,MAAMa,aAAa,GAAGvC,SAAS,CAAC+B,MAAM,CAACC,IAAI,IAAI;IAC7C,MAAMQ,aAAa,GAAGR,IAAI,CAACS,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvC,WAAW,CAACsC,WAAW,CAAC,CAAC,CAAC,IAC7DV,IAAI,CAACY,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvC,WAAW,CAACsC,WAAW,CAAC,CAAC,CAAC;IACvF,MAAMG,eAAe,GAAGvC,cAAc,KAAK,KAAK,IAAI0B,IAAI,CAACc,QAAQ,KAAKxC,cAAc;IACpF,OAAOkC,aAAa,IAAIK,eAAe;EACzC,CAAC,CAAC;EAEF,MAAME,eAAe,GAAGA,CAACC,KAAK,EAAEd,MAAM,KAAK;IACzCrB,WAAW,CAACmC,KAAK,CAACC,aAAa,CAAC;IAChClC,iBAAiB,CAACmB,MAAM,CAAC;EAC3B,CAAC;EAED,MAAMgB,eAAe,GAAGA,CAAA,KAAM;IAC5BrC,WAAW,CAAC,IAAI,CAAC;IACjBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMoC,cAAc,GAAGA,CAAA,KAAM;IAC3BpD,QAAQ,CAAC,oBAAoBe,cAAc,EAAE,CAAC;IAC9CoC,eAAe,CAAC,CAAC;EACnB,CAAC;EAED,MAAME,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMpB,IAAI,GAAGhC,SAAS,CAACoC,IAAI,CAACJ,IAAI,IAAIA,IAAI,CAACF,GAAG,KAAKhB,cAAc,CAAC;IAChEL,eAAe,CAACuB,IAAI,CAAC;IACrBrB,mBAAmB,CAAC,IAAI,CAAC;IACzBuC,eAAe,CAAC,CAAC;EACnB,CAAC;EAED,oBACEtD,OAAA,CAAC9B,GAAG;IAACuF,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAChB3D,OAAA,CAAC9B,GAAG;MAACuF,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzF3D,OAAA,CAAC7B,UAAU;QAAC6F,OAAO,EAAC,IAAI;QAACP,EAAE,EAAE;UAAEQ,UAAU,EAAE;QAAI,CAAE;QAAAN,QAAA,EAAC;MAElD;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbrE,OAAA,CAACxB,MAAM;QACLwF,OAAO,EAAC,WAAW;QACnBM,SAAS,eAAEtE,OAAA,CAACT,GAAG;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACnBE,OAAO,EAAEA,CAAA,KAAMpE,QAAQ,CAAC,iBAAiB,CAAE;QAC3CsD,EAAE,EAAE;UAAEe,YAAY,EAAE;QAAE,CAAE;QAAAb,QAAA,EACzB;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNrE,OAAA,CAAC5B,IAAI;MAACqF,EAAE,EAAE;QAAEM,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eAClB3D,OAAA,CAAC3B,WAAW;QAAAsF,QAAA,eACV3D,OAAA,CAACzB,IAAI;UAACkG,SAAS;UAACC,OAAO,EAAE,CAAE;UAACZ,UAAU,EAAC,QAAQ;UAAAH,QAAA,gBAC7C3D,OAAA,CAACzB,IAAI;YAAC6D,IAAI;YAACuC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAjB,QAAA,eACvB3D,OAAA,CAACvB,SAAS;cACRoG,SAAS;cACTC,WAAW,EAAC,sBAAsB;cAClCzD,KAAK,EAAEb,WAAY;cACnBuE,QAAQ,EAAGC,CAAC,IAAKvE,cAAc,CAACuE,CAAC,CAACC,MAAM,CAAC5D,KAAK,CAAE;cAChD6D,UAAU,EAAE;gBACVC,cAAc,eACZnF,OAAA,CAACtB,cAAc;kBAAC0G,QAAQ,EAAC,OAAO;kBAAAzB,QAAA,eAC9B3D,OAAA,CAACV,MAAM;oBAAA4E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAEpB;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrE,OAAA,CAACzB,IAAI;YAAC6D,IAAI;YAACuC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAjB,QAAA,eACvB3D,OAAA,CAACvB,SAAS;cACRoG,SAAS;cACTQ,MAAM;cACN/D,KAAK,EAAC,UAAU;cAChBD,KAAK,EAAEX,cAAe;cACtBqE,QAAQ,EAAGC,CAAC,IAAKrE,iBAAiB,CAACqE,CAAC,CAACC,MAAM,CAAC5D,KAAK,CAAE;cAAAsC,QAAA,EAElDvC,UAAU,CAACsB,GAAG,CAAEQ,QAAQ,iBACvBlD,OAAA,CAAClB,QAAQ;gBAAsBuC,KAAK,EAAE6B,QAAQ,CAAC7B,KAAM;gBAAAsC,QAAA,EAClDT,QAAQ,CAAC5B;cAAK,GADF4B,QAAQ,CAAC7B,KAAK;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEnB,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACPrE,OAAA,CAACzB,IAAI;YAAC6D,IAAI;YAACuC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAjB,QAAA,eACvB3D,OAAA,CAACxB,MAAM;cACLqG,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBM,SAAS,eAAEtE,OAAA,CAACJ,UAAU;gBAAAsE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAV,QAAA,EAC3B;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPrE,OAAA,CAACzB,IAAI;MAACkG,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAf,QAAA,EACxBrD,OAAO,gBACNN,OAAA,CAACzB,IAAI;QAAC6D,IAAI;QAACuC,EAAE,EAAE,EAAG;QAAAhB,QAAA,eAChB3D,OAAA,CAAC7B,UAAU;UAACmH,KAAK,EAAC,QAAQ;UAAA3B,QAAA,EAAC;QAAqB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,GACL1B,aAAa,CAAC4C,MAAM,KAAK,CAAC,gBAC5BvF,OAAA,CAACzB,IAAI;QAAC6D,IAAI;QAACuC,EAAE,EAAE,EAAG;QAAAhB,QAAA,eAChB3D,OAAA,CAAC7B,UAAU;UAACmH,KAAK,EAAC,QAAQ;UAAA3B,QAAA,EAAC;QAAmB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,GAEP1B,aAAa,CAACD,GAAG,CAAEN,IAAI,iBACrBpC,OAAA,CAACzB,IAAI;QAAC6D,IAAI;QAACuC,EAAE,EAAE,EAAG;QAACa,EAAE,EAAE,CAAE;QAACZ,EAAE,EAAE,CAAE;QAACa,EAAE,EAAE,CAAE;QAAA9B,QAAA,eACrC3D,OAAA,CAAC5B,IAAI;UACHqF,EAAE,EAAE;YACFiC,MAAM,EAAE,MAAM;YACd9B,OAAO,EAAE,MAAM;YACf+B,aAAa,EAAE,QAAQ;YACvBP,QAAQ,EAAE,UAAU;YACpBQ,OAAO,EAAExD,IAAI,CAACG,SAAS,GAAG,CAAC,GAAG;UAChC,CAAE;UAAAoB,QAAA,gBAEF3D,OAAA,CAAC1B,SAAS;YACRuH,SAAS,EAAC,KAAK;YACfH,MAAM,EAAC,KAAK;YACZI,KAAK,EAAE1D,IAAI,CAAC0D,KAAK,IAAI,0BAA2B;YAChDC,GAAG,EAAE3D,IAAI,CAACS;UAAM;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eAEFrE,OAAA,CAAC9B,GAAG;YAACuF,EAAE,EAAE;cAAE2B,QAAQ,EAAE,UAAU;cAAEY,GAAG,EAAE,CAAC;cAAEC,KAAK,EAAE;YAAE,CAAE;YAAAtC,QAAA,eAClD3D,OAAA,CAACpB,UAAU;cACT2F,OAAO,EAAGS,CAAC,IAAK7B,eAAe,CAAC6B,CAAC,EAAE5C,IAAI,CAACF,GAAG,CAAE;cAC7CuB,EAAE,EAAE;gBACFyC,OAAO,EAAE,kBAAkB;gBAC3B,SAAS,EAAE;kBAAEA,OAAO,EAAE;gBAAmB;cAC3C,CAAE;cACFC,IAAI,EAAC,OAAO;cAAAxC,QAAA,eAEZ3D,OAAA,CAACN,QAAQ;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAENrE,OAAA,CAAC3B,WAAW;YAACoF,EAAE,EAAE;cAAE2C,QAAQ,EAAE,CAAC;cAAExC,OAAO,EAAE,MAAM;cAAE+B,aAAa,EAAE;YAAS,CAAE;YAAAhC,QAAA,gBACzE3D,OAAA,CAAC9B,GAAG;cAACuF,EAAE,EAAE;gBAAEG,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE,YAAY;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC7F3D,OAAA,CAAC7B,UAAU;gBAAC6F,OAAO,EAAC,IAAI;gBAAC6B,SAAS,EAAC,IAAI;gBAACpC,EAAE,EAAE;kBAAEQ,UAAU,EAAE;gBAAI,CAAE;gBAAAN,QAAA,EAC7DvB,IAAI,CAACS;cAAK;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACbrE,OAAA,CAACrB,IAAI;gBACH2C,KAAK,EAAEc,IAAI,CAACc,QAAS;gBACrBiD,IAAI,EAAC,OAAO;gBACZE,KAAK,EAAC,SAAS;gBACfrC,OAAO,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENrE,OAAA,CAAC7B,UAAU;cACT6F,OAAO,EAAC,OAAO;cACfqC,KAAK,EAAC,gBAAgB;cACtB5C,EAAE,EAAE;gBAAEM,EAAE,EAAE,CAAC;gBAAEqC,QAAQ,EAAE;cAAE,CAAE;cAAAzC,QAAA,EAE1BvB,IAAI,CAACY;YAAW;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eAEbrE,OAAA,CAAC9B,GAAG;cAACuF,EAAE,EAAE;gBAAEG,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBACzF3D,OAAA,CAAC7B,UAAU;gBAAC6F,OAAO,EAAC,IAAI;gBAACqC,KAAK,EAAC,SAAS;gBAAC5C,EAAE,EAAE;kBAAEQ,UAAU,EAAE;gBAAI,CAAE;gBAAAN,QAAA,GAAC,GAC/D,EAACvB,IAAI,CAACkE,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eACbrE,OAAA,CAACX,gBAAgB;gBACfmH,OAAO,eACLxG,OAAA,CAACZ,MAAM;kBACLqH,OAAO,EAAErE,IAAI,CAACG,SAAU;kBACxBwC,QAAQ,EAAGC,CAAC,IAAK3C,wBAAwB,CAACD,IAAI,CAACF,GAAG,EAAE8C,CAAC,CAACC,MAAM,CAACwB,OAAO,CAAE;kBACtEN,IAAI,EAAC;gBAAO;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CACF;gBACD/C,KAAK,EAAC,WAAW;gBACjBoF,cAAc,EAAC,OAAO;gBACtBjD,EAAE,EAAE;kBAAEkD,CAAC,EAAE;gBAAE;cAAE;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAELjC,IAAI,CAACwE,SAAS,IAAIxE,IAAI,CAACwE,SAAS,CAACrB,MAAM,GAAG,CAAC,iBAC1CvF,OAAA,CAAC9B,GAAG;cAACuF,EAAE,EAAE;gBAAEG,OAAO,EAAE,MAAM;gBAAEiD,QAAQ,EAAE,MAAM;gBAAEC,GAAG,EAAE;cAAI,CAAE;cAAAnD,QAAA,EACtDvB,IAAI,CAACwE,SAAS,CAAClE,GAAG,CAAC,CAACqE,QAAQ,EAAEC,KAAK,kBAClChH,OAAA,CAACrB,IAAI;gBAEH2C,KAAK,EAAEyF,QAAS;gBAChBZ,IAAI,EAAC,OAAO;gBACZE,KAAK,EAAC,SAAS;gBACfrC,OAAO,EAAC;cAAU,GAJbgD,KAAK;gBAAA9C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKX,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC,GAnFoCjC,IAAI,CAACF,GAAG;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoF/C,CACP;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGPrE,OAAA,CAACb,GAAG;MACFkH,KAAK,EAAC,SAAS;MACf,cAAW,KAAK;MAChB5C,EAAE,EAAE;QAAE2B,QAAQ,EAAE,OAAO;QAAE6B,MAAM,EAAE,EAAE;QAAEhB,KAAK,EAAE;MAAG,CAAE;MACjD1B,OAAO,EAAEA,CAAA,KAAMpE,QAAQ,CAAC,iBAAiB,CAAE;MAAAwD,QAAA,eAE3C3D,OAAA,CAACT,GAAG;QAAA2E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNrE,OAAA,CAACnB,IAAI;MACHmC,QAAQ,EAAEA,QAAS;MACnBkG,IAAI,EAAEC,OAAO,CAACnG,QAAQ,CAAE;MACxBoG,OAAO,EAAE9D,eAAgB;MAAAK,QAAA,gBAEzB3D,OAAA,CAAClB,QAAQ;QAACyF,OAAO,EAAEA,CAAA,KAAM;UACvB,MAAMnC,IAAI,GAAGhC,SAAS,CAACoC,IAAI,CAACJ,IAAI,IAAIA,IAAI,CAACF,GAAG,KAAKhB,cAAc,CAAC;UAChEa,OAAO,CAACsF,GAAG,CAAC,YAAY,EAAEjF,IAAI,CAAC;UAC/BkB,eAAe,CAAC,CAAC;QACnB,CAAE;QAAAK,QAAA,gBACA3D,OAAA,CAACL,UAAU;UAAC8D,EAAE,EAAE;YAAE6D,EAAE,EAAE;UAAE;QAAE;UAAApD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAE/B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eACXrE,OAAA,CAAClB,QAAQ;QAACyF,OAAO,EAAEhB,cAAe;QAAAI,QAAA,gBAChC3D,OAAA,CAACR,IAAI;UAACiE,EAAE,EAAE;YAAE6D,EAAE,EAAE;UAAE;QAAE;UAAApD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,aAEzB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eACXrE,OAAA,CAAClB,QAAQ;QAACyF,OAAO,EAAEf,iBAAkB;QAACC,EAAE,EAAE;UAAE4C,KAAK,EAAE;QAAa,CAAE;QAAA1C,QAAA,gBAChE3D,OAAA,CAACP,MAAM;UAACgE,EAAE,EAAE;YAAE6D,EAAE,EAAE;UAAE;QAAE;UAAApD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE3B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGPrE,OAAA,CAACjB,MAAM;MACLmI,IAAI,EAAEpG,gBAAiB;MACvBsG,OAAO,EAAEA,CAAA,KAAMrG,mBAAmB,CAAC,KAAK,CAAE;MAAA4C,QAAA,gBAE1C3D,OAAA,CAAChB,WAAW;QAAA2E,QAAA,EAAC;MAAgB;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC3CrE,OAAA,CAACf,aAAa;QAAA0E,QAAA,eACZ3D,OAAA,CAAC7B,UAAU;UAAAwF,QAAA,GAAC,oCACuB,EAAC/C,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEiC,KAAK,EAAC,mCACxD;QAAA;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAChBrE,OAAA,CAACd,aAAa;QAAAyE,QAAA,gBACZ3D,OAAA,CAACxB,MAAM;UAAC+F,OAAO,EAAEA,CAAA,KAAMxD,mBAAmB,CAAC,KAAK,CAAE;UAAA4C,QAAA,EAAC;QAAM;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClErE,OAAA,CAACxB,MAAM;UAAC+F,OAAO,EAAEvC,gBAAiB;UAACqE,KAAK,EAAC,OAAO;UAACrC,OAAO,EAAC,WAAW;UAAAL,QAAA,EAAC;QAErE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACnE,EAAA,CApTID,SAAS;EAAA,QACIJ,WAAW;AAAA;AAAA0H,EAAA,GADxBtH,SAAS;AAsTf,eAAeA,SAAS;AAAC,IAAAsH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}