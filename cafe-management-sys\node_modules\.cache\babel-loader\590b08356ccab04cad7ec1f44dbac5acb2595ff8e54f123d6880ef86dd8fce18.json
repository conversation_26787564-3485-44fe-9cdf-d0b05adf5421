{"ast": null, "code": "var _jsxFileName = \"D:\\\\Github_Repos\\\\cafe-mangement-system-react\\\\cafe-management-sys\\\\src\\\\contexts\\\\AuthContext.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  useEffect(() => {\n    // Check for stored authentication data on app load\n    const checkAuthStatus = () => {\n      try {\n        // Check both sessionStorage (preferred) and localStorage for compatibility\n        const token = sessionStorage.getItem('token') || localStorage.getItem('authToken');\n        const userData = sessionStorage.getItem('user') || localStorage.getItem('userData');\n        const userType = sessionStorage.getItem('userType');\n        if (token && userData) {\n          const parsedUser = JSON.parse(userData);\n          // Add userType to user object if available\n          if (userType) {\n            parsedUser.userType = userType;\n          }\n          setUser(parsedUser);\n          setIsAuthenticated(true);\n        }\n      } catch (error) {\n        console.error('Error checking auth status:', error);\n        // Clear invalid data from both storages\n        sessionStorage.removeItem('token');\n        sessionStorage.removeItem('user');\n        sessionStorage.removeItem('userType');\n        localStorage.removeItem('authToken');\n        localStorage.removeItem('userData');\n      } finally {\n        setLoading(false);\n      }\n    };\n    checkAuthStatus();\n  }, []);\n  const login = (userData, token, userType) => {\n    try {\n      // Store in sessionStorage (preferred) and localStorage (fallback)\n      sessionStorage.setItem('token', token);\n      sessionStorage.setItem('user', JSON.stringify(userData));\n      if (userType) {\n        sessionStorage.setItem('userType', userType);\n        userData.userType = userType;\n      }\n\n      // Also store in localStorage for compatibility\n      localStorage.setItem('authToken', token);\n      localStorage.setItem('userData', JSON.stringify(userData));\n      setUser(userData);\n      setIsAuthenticated(true);\n      return true;\n    } catch (error) {\n      console.error('Error during login:', error);\n      return false;\n    }\n  };\n  const logout = () => {\n    try {\n      // Clear both sessionStorage and localStorage\n      sessionStorage.removeItem('token');\n      sessionStorage.removeItem('user');\n      sessionStorage.removeItem('userType');\n      localStorage.removeItem('authToken');\n      localStorage.removeItem('userData');\n      setUser(null);\n      setIsAuthenticated(false);\n      return true;\n    } catch (error) {\n      console.error('Error during logout:', error);\n      return false;\n    }\n  };\n  const updateUser = updatedUserData => {\n    try {\n      const newUserData = {\n        ...user,\n        ...updatedUserData\n      };\n      // Update both storages\n      sessionStorage.setItem('user', JSON.stringify(newUserData));\n      localStorage.setItem('userData', JSON.stringify(newUserData));\n      setUser(newUserData);\n      return true;\n    } catch (error) {\n      console.error('Error updating user:', error);\n      return false;\n    }\n  };\n  const getAuthToken = () => {\n    return sessionStorage.getItem('token') || localStorage.getItem('authToken');\n  };\n  const isAdmin = () => {\n    // Check for admin role or staffOrAdmin userType\n    return user && (user.role === 'admin' || user.role === 'manager' || user.userType === 'staffOrAdmin');\n  };\n  const isStaff = () => {\n    return user && (user.role === 'admin' || user.role === 'manager' || user.role === 'waiter' || user.userType === 'staffOrAdmin');\n  };\n  const isCustomer = () => {\n    return user && user.role === 'customer';\n  };\n  const value = {\n    user,\n    loading,\n    isAuthenticated,\n    login,\n    logout,\n    updateUser,\n    getAuthToken,\n    isAdmin,\n    isStaff,\n    isCustomer\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 146,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"xBgiRagNfQVCfEr2dT2PptfN+TE=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "isAuthenticated", "setIsAuthenticated", "checkAuthStatus", "token", "sessionStorage", "getItem", "localStorage", "userData", "userType", "parsedUser", "JSON", "parse", "error", "console", "removeItem", "login", "setItem", "stringify", "logout", "updateUser", "updatedUserData", "newUserData", "getAuthToken", "isAdmin", "role", "isStaff", "isCustomer", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Github_Repos/cafe-mangement-system-react/cafe-management-sys/src/contexts/AuthContext.jsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n\n  useEffect(() => {\n    // Check for stored authentication data on app load\n    const checkAuthStatus = () => {\n      try {\n        // Check both sessionStorage (preferred) and localStorage for compatibility\n        const token = sessionStorage.getItem('token') || localStorage.getItem('authToken');\n        const userData = sessionStorage.getItem('user') || localStorage.getItem('userData');\n        const userType = sessionStorage.getItem('userType');\n\n        if (token && userData) {\n          const parsedUser = JSON.parse(userData);\n          // Add userType to user object if available\n          if (userType) {\n            parsedUser.userType = userType;\n          }\n          setUser(parsedUser);\n          setIsAuthenticated(true);\n        }\n      } catch (error) {\n        console.error('Error checking auth status:', error);\n        // Clear invalid data from both storages\n        sessionStorage.removeItem('token');\n        sessionStorage.removeItem('user');\n        sessionStorage.removeItem('userType');\n        localStorage.removeItem('authToken');\n        localStorage.removeItem('userData');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    checkAuthStatus();\n  }, []);\n\n  const login = (userData, token, userType) => {\n    try {\n      // Store in sessionStorage (preferred) and localStorage (fallback)\n      sessionStorage.setItem('token', token);\n      sessionStorage.setItem('user', JSON.stringify(userData));\n      if (userType) {\n        sessionStorage.setItem('userType', userType);\n        userData.userType = userType;\n      }\n\n      // Also store in localStorage for compatibility\n      localStorage.setItem('authToken', token);\n      localStorage.setItem('userData', JSON.stringify(userData));\n\n      setUser(userData);\n      setIsAuthenticated(true);\n      return true;\n    } catch (error) {\n      console.error('Error during login:', error);\n      return false;\n    }\n  };\n\n  const logout = () => {\n    try {\n      // Clear both sessionStorage and localStorage\n      sessionStorage.removeItem('token');\n      sessionStorage.removeItem('user');\n      sessionStorage.removeItem('userType');\n      localStorage.removeItem('authToken');\n      localStorage.removeItem('userData');\n      setUser(null);\n      setIsAuthenticated(false);\n      return true;\n    } catch (error) {\n      console.error('Error during logout:', error);\n      return false;\n    }\n  };\n\n  const updateUser = (updatedUserData) => {\n    try {\n      const newUserData = { ...user, ...updatedUserData };\n      // Update both storages\n      sessionStorage.setItem('user', JSON.stringify(newUserData));\n      localStorage.setItem('userData', JSON.stringify(newUserData));\n      setUser(newUserData);\n      return true;\n    } catch (error) {\n      console.error('Error updating user:', error);\n      return false;\n    }\n  };\n\n  const getAuthToken = () => {\n    return sessionStorage.getItem('token') || localStorage.getItem('authToken');\n  };\n\n  const isAdmin = () => {\n    // Check for admin role or staffOrAdmin userType\n    return user && (\n      user.role === 'admin' ||\n      user.role === 'manager' ||\n      user.userType === 'staffOrAdmin'\n    );\n  };\n\n  const isStaff = () => {\n    return user && (\n      user.role === 'admin' ||\n      user.role === 'manager' ||\n      user.role === 'waiter' ||\n      user.userType === 'staffOrAdmin'\n    );\n  };\n\n  const isCustomer = () => {\n    return user && user.role === 'customer';\n  };\n\n  const value = {\n    user,\n    loading,\n    isAuthenticated,\n    login,\n    logout,\n    updateUser,\n    getAuthToken,\n    isAdmin,\n    isStaff,\n    isCustomer,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,WAAW,gBAAGN,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMO,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGR,UAAU,CAACK,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,eAAe,EAAEC,kBAAkB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAE7DC,SAAS,CAAC,MAAM;IACd;IACA,MAAMiB,eAAe,GAAGA,CAAA,KAAM;MAC5B,IAAI;QACF;QACA,MAAMC,KAAK,GAAGC,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC,IAAIC,YAAY,CAACD,OAAO,CAAC,WAAW,CAAC;QAClF,MAAME,QAAQ,GAAGH,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,IAAIC,YAAY,CAACD,OAAO,CAAC,UAAU,CAAC;QACnF,MAAMG,QAAQ,GAAGJ,cAAc,CAACC,OAAO,CAAC,UAAU,CAAC;QAEnD,IAAIF,KAAK,IAAII,QAAQ,EAAE;UACrB,MAAME,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAAC;UACvC;UACA,IAAIC,QAAQ,EAAE;YACZC,UAAU,CAACD,QAAQ,GAAGA,QAAQ;UAChC;UACAX,OAAO,CAACY,UAAU,CAAC;UACnBR,kBAAkB,CAAC,IAAI,CAAC;QAC1B;MACF,CAAC,CAAC,OAAOW,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD;QACAR,cAAc,CAACU,UAAU,CAAC,OAAO,CAAC;QAClCV,cAAc,CAACU,UAAU,CAAC,MAAM,CAAC;QACjCV,cAAc,CAACU,UAAU,CAAC,UAAU,CAAC;QACrCR,YAAY,CAACQ,UAAU,CAAC,WAAW,CAAC;QACpCR,YAAY,CAACQ,UAAU,CAAC,UAAU,CAAC;MACrC,CAAC,SAAS;QACRf,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMa,KAAK,GAAGA,CAACR,QAAQ,EAAEJ,KAAK,EAAEK,QAAQ,KAAK;IAC3C,IAAI;MACF;MACAJ,cAAc,CAACY,OAAO,CAAC,OAAO,EAAEb,KAAK,CAAC;MACtCC,cAAc,CAACY,OAAO,CAAC,MAAM,EAAEN,IAAI,CAACO,SAAS,CAACV,QAAQ,CAAC,CAAC;MACxD,IAAIC,QAAQ,EAAE;QACZJ,cAAc,CAACY,OAAO,CAAC,UAAU,EAAER,QAAQ,CAAC;QAC5CD,QAAQ,CAACC,QAAQ,GAAGA,QAAQ;MAC9B;;MAEA;MACAF,YAAY,CAACU,OAAO,CAAC,WAAW,EAAEb,KAAK,CAAC;MACxCG,YAAY,CAACU,OAAO,CAAC,UAAU,EAAEN,IAAI,CAACO,SAAS,CAACV,QAAQ,CAAC,CAAC;MAE1DV,OAAO,CAACU,QAAQ,CAAC;MACjBN,kBAAkB,CAAC,IAAI,CAAC;MACxB,OAAO,IAAI;IACb,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,OAAO,KAAK;IACd;EACF,CAAC;EAED,MAAMM,MAAM,GAAGA,CAAA,KAAM;IACnB,IAAI;MACF;MACAd,cAAc,CAACU,UAAU,CAAC,OAAO,CAAC;MAClCV,cAAc,CAACU,UAAU,CAAC,MAAM,CAAC;MACjCV,cAAc,CAACU,UAAU,CAAC,UAAU,CAAC;MACrCR,YAAY,CAACQ,UAAU,CAAC,WAAW,CAAC;MACpCR,YAAY,CAACQ,UAAU,CAAC,UAAU,CAAC;MACnCjB,OAAO,CAAC,IAAI,CAAC;MACbI,kBAAkB,CAAC,KAAK,CAAC;MACzB,OAAO,IAAI;IACb,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,OAAO,KAAK;IACd;EACF,CAAC;EAED,MAAMO,UAAU,GAAIC,eAAe,IAAK;IACtC,IAAI;MACF,MAAMC,WAAW,GAAG;QAAE,GAAGzB,IAAI;QAAE,GAAGwB;MAAgB,CAAC;MACnD;MACAhB,cAAc,CAACY,OAAO,CAAC,MAAM,EAAEN,IAAI,CAACO,SAAS,CAACI,WAAW,CAAC,CAAC;MAC3Df,YAAY,CAACU,OAAO,CAAC,UAAU,EAAEN,IAAI,CAACO,SAAS,CAACI,WAAW,CAAC,CAAC;MAC7DxB,OAAO,CAACwB,WAAW,CAAC;MACpB,OAAO,IAAI;IACb,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,OAAO,KAAK;IACd;EACF,CAAC;EAED,MAAMU,YAAY,GAAGA,CAAA,KAAM;IACzB,OAAOlB,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC,IAAIC,YAAY,CAACD,OAAO,CAAC,WAAW,CAAC;EAC7E,CAAC;EAED,MAAMkB,OAAO,GAAGA,CAAA,KAAM;IACpB;IACA,OAAO3B,IAAI,KACTA,IAAI,CAAC4B,IAAI,KAAK,OAAO,IACrB5B,IAAI,CAAC4B,IAAI,KAAK,SAAS,IACvB5B,IAAI,CAACY,QAAQ,KAAK,cAAc,CACjC;EACH,CAAC;EAED,MAAMiB,OAAO,GAAGA,CAAA,KAAM;IACpB,OAAO7B,IAAI,KACTA,IAAI,CAAC4B,IAAI,KAAK,OAAO,IACrB5B,IAAI,CAAC4B,IAAI,KAAK,SAAS,IACvB5B,IAAI,CAAC4B,IAAI,KAAK,QAAQ,IACtB5B,IAAI,CAACY,QAAQ,KAAK,cAAc,CACjC;EACH,CAAC;EAED,MAAMkB,UAAU,GAAGA,CAAA,KAAM;IACvB,OAAO9B,IAAI,IAAIA,IAAI,CAAC4B,IAAI,KAAK,UAAU;EACzC,CAAC;EAED,MAAMG,KAAK,GAAG;IACZ/B,IAAI;IACJE,OAAO;IACPE,eAAe;IACfe,KAAK;IACLG,MAAM;IACNC,UAAU;IACVG,YAAY;IACZC,OAAO;IACPE,OAAO;IACPC;EACF,CAAC;EAED,oBACEvC,OAAA,CAACC,WAAW,CAACwC,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAjC,QAAA,EAChCA;EAAQ;IAAAmC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACrC,GAAA,CAzIWF,YAAY;AAAAwC,EAAA,GAAZxC,YAAY;AAAA,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}