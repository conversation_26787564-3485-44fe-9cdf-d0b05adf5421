{"ast": null, "code": "var _jsxFileName = \"D:\\\\Github_Repos\\\\cafe-mangement-system-react\\\\cafe-management-sys\\\\src\\\\pages\\\\admin\\\\AdminRevenue.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Card, CardContent, Grid, Button, ButtonGroup, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip, IconButton, LinearProgress } from '@mui/material';\nimport { TrendingUp, TrendingDown, AttachMoney, DateRange, Download, Refresh } from '@mui/icons-material';\nimport { analyticsAPI } from '../../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminRevenue = () => {\n  _s();\n  var _periods$find, _revenueData$breakdow, _revenueData$dailyRev;\n  const [loading, setLoading] = useState(true);\n  const [period, setPeriod] = useState('30d');\n  const [revenueData, setRevenueData] = useState(null);\n  const [lastUpdated, setLastUpdated] = useState(new Date());\n  const periods = [{\n    value: '7d',\n    label: '7 Days'\n  }, {\n    value: '30d',\n    label: '30 Days'\n  }, {\n    value: '90d',\n    label: '90 Days'\n  }, {\n    value: '1y',\n    label: '1 Year'\n  }];\n  useEffect(() => {\n    fetchRevenueData();\n  }, [period]);\n  const fetchRevenueData = async () => {\n    try {\n      setLoading(true);\n      const data = await analyticsAPI.getRevenueStats(period);\n      setRevenueData(data);\n      setLastUpdated(new Date());\n    } catch (error) {\n      console.error('Error fetching revenue data:', error);\n      // Mock data fallback\n      setRevenueData({\n        total: 12426,\n        change: 12.5,\n        chartData: [],\n        breakdown: [{\n          category: 'Coffee',\n          amount: 7500,\n          percentage: 60.4,\n          change: 15.2\n        }, {\n          category: 'Pastries',\n          amount: 2800,\n          percentage: 22.5,\n          change: 8.7\n        }, {\n          category: 'Sandwiches',\n          amount: 1500,\n          percentage: 12.1,\n          change: -2.3\n        }, {\n          category: 'Desserts',\n          amount: 626,\n          percentage: 5.0,\n          change: 22.1\n        }],\n        dailyRevenue: [{\n          date: '2024-01-01',\n          amount: 450\n        }, {\n          date: '2024-01-02',\n          amount: 520\n        }, {\n          date: '2024-01-03',\n          amount: 380\n        }, {\n          date: '2024-01-04',\n          amount: 610\n        }, {\n          date: '2024-01-05',\n          amount: 490\n        }]\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const StatCard = ({\n    title,\n    value,\n    change,\n    icon,\n    color = 'primary'\n  }) => /*#__PURE__*/_jsxDEV(Card, {\n    sx: {\n      height: '100%'\n    },\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            color: `${color}.main`\n          },\n          children: icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 0.5\n          },\n          children: [change > 0 ? /*#__PURE__*/_jsxDEV(TrendingUp, {\n            color: \"success\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 15\n          }, this) : change < 0 ? /*#__PURE__*/_jsxDEV(TrendingDown, {\n            color: \"error\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 15\n          }, this) : null, /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: change > 0 ? 'success.main' : change < 0 ? 'error.main' : 'text.secondary',\n            fontWeight: 600,\n            children: [change > 0 ? '+' : '', change, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        color: `${color}.main`,\n        fontWeight: 700,\n        gutterBottom: true,\n        children: typeof value === 'number' ? `$${value.toLocaleString()}` : value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n  if (loading && !revenueData) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          mb: 3,\n          fontWeight: 600\n        },\n        children: \"Revenue Analytics\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          fontWeight: 600\n        },\n        children: \"Revenue Analytics\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          color: \"text.secondary\",\n          children: [\"Last updated: \", lastUpdated.toLocaleTimeString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: fetchRevenueData,\n          disabled: loading,\n          children: /*#__PURE__*/_jsxDEV(Refresh, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(Download, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 49\n          }, this),\n          children: \"Export\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Time Period\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ButtonGroup, {\n            variant: \"outlined\",\n            size: \"small\",\n            children: periods.map(p => /*#__PURE__*/_jsxDEV(Button, {\n              variant: period === p.value ? 'contained' : 'outlined',\n              onClick: () => setPeriod(p.value),\n              children: p.label\n            }, p.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: `Total Revenue (${(_periods$find = periods.find(p => p.value === period)) === null || _periods$find === void 0 ? void 0 : _periods$find.label})`,\n          value: revenueData === null || revenueData === void 0 ? void 0 : revenueData.total,\n          change: revenueData === null || revenueData === void 0 ? void 0 : revenueData.change,\n          icon: /*#__PURE__*/_jsxDEV(AttachMoney, {\n            fontSize: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 19\n          }, this),\n          color: \"success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Average Daily Revenue\",\n          value: revenueData !== null && revenueData !== void 0 && revenueData.total ? Math.round(revenueData.total / (period === '7d' ? 7 : period === '30d' ? 30 : period === '90d' ? 90 : 365)) : 0,\n          change: revenueData === null || revenueData === void 0 ? void 0 : revenueData.change,\n          icon: /*#__PURE__*/_jsxDEV(DateRange, {\n            fontSize: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 19\n          }, this),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Growth Rate\",\n          value: `${(revenueData === null || revenueData === void 0 ? void 0 : revenueData.change) || 0}%`,\n          change: revenueData === null || revenueData === void 0 ? void 0 : revenueData.change,\n          icon: /*#__PURE__*/_jsxDEV(TrendingUp, {\n            fontSize: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 19\n          }, this),\n          color: \"info\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Revenue by Category\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n              children: /*#__PURE__*/_jsxDEV(Table, {\n                children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                  children: /*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Category\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 205,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: \"Amount\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 206,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: \"Percentage\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 207,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: \"Change\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 208,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                  children: revenueData === null || revenueData === void 0 ? void 0 : (_revenueData$breakdow = revenueData.breakdown) === null || _revenueData$breakdow === void 0 ? void 0 : _revenueData$breakdow.map(item => /*#__PURE__*/_jsxDEV(TableRow, {\n                    hover: true,\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"subtitle2\",\n                        fontWeight: 600,\n                        children: item.category\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 215,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 214,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"subtitle2\",\n                        fontWeight: 600,\n                        children: [\"$\", item.amount.toLocaleString()]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 220,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 219,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: [item.percentage.toFixed(1), \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 225,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 224,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: /*#__PURE__*/_jsxDEV(Chip, {\n                        label: `${item.change > 0 ? '+' : ''}${item.change}%`,\n                        color: item.change > 0 ? 'success' : item.change < 0 ? 'error' : 'default',\n                        size: \"small\",\n                        icon: item.change > 0 ? /*#__PURE__*/_jsxDEV(TrendingUp, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 234,\n                          columnNumber: 53\n                        }, this) : item.change < 0 ? /*#__PURE__*/_jsxDEV(TrendingDown, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 234,\n                          columnNumber: 88\n                        }, this) : null\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 230,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 229,\n                      columnNumber: 25\n                    }, this)]\n                  }, item.category, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Recent Daily Revenue\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: 2\n              },\n              children: revenueData === null || revenueData === void 0 ? void 0 : (_revenueData$dailyRev = revenueData.dailyRevenue) === null || _revenueData$dailyRev === void 0 ? void 0 : _revenueData$dailyRev.slice(-5).map((day, index) => /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: new Date(day.date).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  fontWeight: 600,\n                  children: [\"$\", day.amount.toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 21\n                }, this)]\n              }, day.date, true, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 124,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminRevenue, \"otFEtFNj8j6mabglZqnrXwRvJMY=\");\n_c = AdminRevenue;\nexport default AdminRevenue;\nvar _c;\n$RefreshReg$(_c, \"AdminRevenue\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "<PERSON><PERSON>", "ButtonGroup", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Chip", "IconButton", "LinearProgress", "TrendingUp", "TrendingDown", "AttachMoney", "DateRange", "Download", "Refresh", "analyticsAPI", "jsxDEV", "_jsxDEV", "AdminRevenue", "_s", "_periods$find", "_revenueData$breakdow", "_revenueData$dailyRev", "loading", "setLoading", "period", "<PERSON><PERSON><PERSON><PERSON>", "revenueData", "setRevenueData", "lastUpdated", "setLastUpdated", "Date", "periods", "value", "label", "fetchRevenueData", "data", "getRevenueStats", "error", "console", "total", "change", "chartData", "breakdown", "category", "amount", "percentage", "dailyRevenue", "date", "StatCard", "title", "icon", "color", "sx", "height", "children", "display", "alignItems", "justifyContent", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gap", "fontSize", "variant", "fontWeight", "gutterBottom", "toLocaleString", "p", "toLocaleTimeString", "onClick", "disabled", "startIcon", "size", "map", "container", "spacing", "item", "xs", "md", "find", "Math", "round", "align", "hover", "toFixed", "flexDirection", "slice", "day", "index", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["D:/Github_Repos/cafe-mangement-system-react/cafe-management-sys/src/pages/admin/AdminRevenue.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON>,\n  Typography,\n  Card,\n  CardContent,\n  Grid,\n  Button,\n  ButtonGroup,\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Chip,\n  IconButton,\n  LinearProgress,\n} from '@mui/material';\nimport {\n  TrendingUp,\n  TrendingDown,\n  AttachMoney,\n  DateRange,\n  Download,\n  Refresh,\n} from '@mui/icons-material';\nimport { analyticsAPI } from '../../services/api';\n\nconst AdminRevenue = () => {\n  const [loading, setLoading] = useState(true);\n  const [period, setPeriod] = useState('30d');\n  const [revenueData, setRevenueData] = useState(null);\n  const [lastUpdated, setLastUpdated] = useState(new Date());\n\n  const periods = [\n    { value: '7d', label: '7 Days' },\n    { value: '30d', label: '30 Days' },\n    { value: '90d', label: '90 Days' },\n    { value: '1y', label: '1 Year' },\n  ];\n\n  useEffect(() => {\n    fetchRevenueData();\n  }, [period]);\n\n  const fetchRevenueData = async () => {\n    try {\n      setLoading(true);\n      const data = await analyticsAPI.getRevenueStats(period);\n      setRevenueData(data);\n      setLastUpdated(new Date());\n    } catch (error) {\n      console.error('Error fetching revenue data:', error);\n      // Mock data fallback\n      setRevenueData({\n        total: 12426,\n        change: 12.5,\n        chartData: [],\n        breakdown: [\n          { category: 'Coffee', amount: 7500, percentage: 60.4, change: 15.2 },\n          { category: 'Pastries', amount: 2800, percentage: 22.5, change: 8.7 },\n          { category: 'Sandwiches', amount: 1500, percentage: 12.1, change: -2.3 },\n          { category: 'Desserts', amount: 626, percentage: 5.0, change: 22.1 },\n        ],\n        dailyRevenue: [\n          { date: '2024-01-01', amount: 450 },\n          { date: '2024-01-02', amount: 520 },\n          { date: '2024-01-03', amount: 380 },\n          { date: '2024-01-04', amount: 610 },\n          { date: '2024-01-05', amount: 490 },\n        ],\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const StatCard = ({ title, value, change, icon, color = 'primary' }) => (\n    <Card sx={{ height: '100%' }}>\n      <CardContent>\n        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>\n          <Box sx={{ color: `${color}.main` }}>\n            {icon}\n          </Box>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n            {change > 0 ? (\n              <TrendingUp color=\"success\" fontSize=\"small\" />\n            ) : change < 0 ? (\n              <TrendingDown color=\"error\" fontSize=\"small\" />\n            ) : null}\n            <Typography\n              variant=\"caption\"\n              color={change > 0 ? 'success.main' : change < 0 ? 'error.main' : 'text.secondary'}\n              fontWeight={600}\n            >\n              {change > 0 ? '+' : ''}{change}%\n            </Typography>\n          </Box>\n        </Box>\n        <Typography variant=\"h4\" color={`${color}.main`} fontWeight={700} gutterBottom>\n          {typeof value === 'number' ? `$${value.toLocaleString()}` : value}\n        </Typography>\n        <Typography variant=\"body2\" color=\"text.secondary\">\n          {title}\n        </Typography>\n      </CardContent>\n    </Card>\n  );\n\n  if (loading && !revenueData) {\n    return (\n      <Box sx={{ p: 3 }}>\n        <Typography variant=\"h4\" sx={{ mb: 3, fontWeight: 600 }}>\n          Revenue Analytics\n        </Typography>\n        <LinearProgress />\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ p: 3 }}>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h4\" sx={{ fontWeight: 600 }}>\n          Revenue Analytics\n        </Typography>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          <Typography variant=\"caption\" color=\"text.secondary\">\n            Last updated: {lastUpdated.toLocaleTimeString()}\n          </Typography>\n          <IconButton onClick={fetchRevenueData} disabled={loading}>\n            <Refresh />\n          </IconButton>\n          <Button variant=\"outlined\" startIcon={<Download />}>\n            Export\n          </Button>\n        </Box>\n      </Box>\n\n      {/* Period Selection */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"h6\">Time Period</Typography>\n            <ButtonGroup variant=\"outlined\" size=\"small\">\n              {periods.map((p) => (\n                <Button\n                  key={p.value}\n                  variant={period === p.value ? 'contained' : 'outlined'}\n                  onClick={() => setPeriod(p.value)}\n                >\n                  {p.label}\n                </Button>\n              ))}\n            </ButtonGroup>\n          </Box>\n        </CardContent>\n      </Card>\n\n      {/* Revenue Summary */}\n      <Grid container spacing={3} sx={{ mb: 3 }}>\n        <Grid item xs={12} md={4}>\n          <StatCard\n            title={`Total Revenue (${periods.find(p => p.value === period)?.label})`}\n            value={revenueData?.total}\n            change={revenueData?.change}\n            icon={<AttachMoney fontSize=\"large\" />}\n            color=\"success\"\n          />\n        </Grid>\n        <Grid item xs={12} md={4}>\n          <StatCard\n            title=\"Average Daily Revenue\"\n            value={revenueData?.total ? Math.round(revenueData.total / (period === '7d' ? 7 : period === '30d' ? 30 : period === '90d' ? 90 : 365)) : 0}\n            change={revenueData?.change}\n            icon={<DateRange fontSize=\"large\" />}\n            color=\"primary\"\n          />\n        </Grid>\n        <Grid item xs={12} md={4}>\n          <StatCard\n            title=\"Growth Rate\"\n            value={`${revenueData?.change || 0}%`}\n            change={revenueData?.change}\n            icon={<TrendingUp fontSize=\"large\" />}\n            color=\"info\"\n          />\n        </Grid>\n      </Grid>\n\n      {/* Revenue Breakdown */}\n      <Grid container spacing={3}>\n        <Grid item xs={12} md={8}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Revenue by Category\n              </Typography>\n              <TableContainer>\n                <Table>\n                  <TableHead>\n                    <TableRow>\n                      <TableCell>Category</TableCell>\n                      <TableCell align=\"right\">Amount</TableCell>\n                      <TableCell align=\"right\">Percentage</TableCell>\n                      <TableCell align=\"right\">Change</TableCell>\n                    </TableRow>\n                  </TableHead>\n                  <TableBody>\n                    {revenueData?.breakdown?.map((item) => (\n                      <TableRow key={item.category} hover>\n                        <TableCell>\n                          <Typography variant=\"subtitle2\" fontWeight={600}>\n                            {item.category}\n                          </Typography>\n                        </TableCell>\n                        <TableCell align=\"right\">\n                          <Typography variant=\"subtitle2\" fontWeight={600}>\n                            ${item.amount.toLocaleString()}\n                          </Typography>\n                        </TableCell>\n                        <TableCell align=\"right\">\n                          <Typography variant=\"body2\">\n                            {item.percentage.toFixed(1)}%\n                          </Typography>\n                        </TableCell>\n                        <TableCell align=\"right\">\n                          <Chip\n                            label={`${item.change > 0 ? '+' : ''}${item.change}%`}\n                            color={item.change > 0 ? 'success' : item.change < 0 ? 'error' : 'default'}\n                            size=\"small\"\n                            icon={item.change > 0 ? <TrendingUp /> : item.change < 0 ? <TrendingDown /> : null}\n                          />\n                        </TableCell>\n                      </TableRow>\n                    ))}\n                  </TableBody>\n                </Table>\n              </TableContainer>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} md={4}>\n          <Card sx={{ height: '100%' }}>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Recent Daily Revenue\n              </Typography>\n              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>\n                {revenueData?.dailyRevenue?.slice(-5).map((day, index) => (\n                  <Box key={day.date} sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      {new Date(day.date).toLocaleDateString()}\n                    </Typography>\n                    <Typography variant=\"subtitle2\" fontWeight={600}>\n                      ${day.amount.toLocaleString()}\n                    </Typography>\n                  </Box>\n                ))}\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nexport default AdminRevenue;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,UAAU,EACVC,cAAc,QACT,eAAe;AACtB,SACEC,UAAU,EACVC,YAAY,EACZC,WAAW,EACXC,SAAS,EACTC,QAAQ,EACRC,OAAO,QACF,qBAAqB;AAC5B,SAASC,YAAY,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,aAAA,EAAAC,qBAAA,EAAAC,qBAAA;EACzB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmC,MAAM,EAAEC,SAAS,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACqC,WAAW,EAAEC,cAAc,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACuC,WAAW,EAAEC,cAAc,CAAC,GAAGxC,QAAQ,CAAC,IAAIyC,IAAI,CAAC,CAAC,CAAC;EAE1D,MAAMC,OAAO,GAAG,CACd;IAAEC,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAS,CAAC,EAChC;IAAED,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAU,CAAC,EAClC;IAAED,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAU,CAAC,EAClC;IAAED,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAS,CAAC,CACjC;EAED3C,SAAS,CAAC,MAAM;IACd4C,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAACV,MAAM,CAAC,CAAC;EAEZ,MAAMU,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFX,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMY,IAAI,GAAG,MAAMrB,YAAY,CAACsB,eAAe,CAACZ,MAAM,CAAC;MACvDG,cAAc,CAACQ,IAAI,CAAC;MACpBN,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD;MACAV,cAAc,CAAC;QACbY,KAAK,EAAE,KAAK;QACZC,MAAM,EAAE,IAAI;QACZC,SAAS,EAAE,EAAE;QACbC,SAAS,EAAE,CACT;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,IAAI;UAAEL,MAAM,EAAE;QAAK,CAAC,EACpE;UAAEG,QAAQ,EAAE,UAAU;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,IAAI;UAAEL,MAAM,EAAE;QAAI,CAAC,EACrE;UAAEG,QAAQ,EAAE,YAAY;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,IAAI;UAAEL,MAAM,EAAE,CAAC;QAAI,CAAC,EACxE;UAAEG,QAAQ,EAAE,UAAU;UAAEC,MAAM,EAAE,GAAG;UAAEC,UAAU,EAAE,GAAG;UAAEL,MAAM,EAAE;QAAK,CAAC,CACrE;QACDM,YAAY,EAAE,CACZ;UAAEC,IAAI,EAAE,YAAY;UAAEH,MAAM,EAAE;QAAI,CAAC,EACnC;UAAEG,IAAI,EAAE,YAAY;UAAEH,MAAM,EAAE;QAAI,CAAC,EACnC;UAAEG,IAAI,EAAE,YAAY;UAAEH,MAAM,EAAE;QAAI,CAAC,EACnC;UAAEG,IAAI,EAAE,YAAY;UAAEH,MAAM,EAAE;QAAI,CAAC,EACnC;UAAEG,IAAI,EAAE,YAAY;UAAEH,MAAM,EAAE;QAAI,CAAC;MAEvC,CAAC,CAAC;IACJ,CAAC,SAAS;MACRrB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMyB,QAAQ,GAAGA,CAAC;IAAEC,KAAK;IAAEjB,KAAK;IAAEQ,MAAM;IAAEU,IAAI;IAAEC,KAAK,GAAG;EAAU,CAAC,kBACjEnC,OAAA,CAACvB,IAAI;IAAC2D,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAO,CAAE;IAAAC,QAAA,eAC3BtC,OAAA,CAACtB,WAAW;MAAA4D,QAAA,gBACVtC,OAAA,CAACzB,GAAG;QAAC6D,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,cAAc,EAAE,eAAe;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACzFtC,OAAA,CAACzB,GAAG;UAAC6D,EAAE,EAAE;YAAED,KAAK,EAAE,GAAGA,KAAK;UAAQ,CAAE;UAAAG,QAAA,EACjCJ;QAAI;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACN9C,OAAA,CAACzB,GAAG;UAAC6D,EAAE,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEO,GAAG,EAAE;UAAI,CAAE;UAAAT,QAAA,GAC1Dd,MAAM,GAAG,CAAC,gBACTxB,OAAA,CAACR,UAAU;YAAC2C,KAAK,EAAC,SAAS;YAACa,QAAQ,EAAC;UAAO;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAC7CtB,MAAM,GAAG,CAAC,gBACZxB,OAAA,CAACP,YAAY;YAAC0C,KAAK,EAAC,OAAO;YAACa,QAAQ,EAAC;UAAO;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAC7C,IAAI,eACR9C,OAAA,CAACxB,UAAU;YACTyE,OAAO,EAAC,SAAS;YACjBd,KAAK,EAAEX,MAAM,GAAG,CAAC,GAAG,cAAc,GAAGA,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,gBAAiB;YAClF0B,UAAU,EAAE,GAAI;YAAAZ,QAAA,GAEfd,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEA,MAAM,EAAC,GACjC;UAAA;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN9C,OAAA,CAACxB,UAAU;QAACyE,OAAO,EAAC,IAAI;QAACd,KAAK,EAAE,GAAGA,KAAK,OAAQ;QAACe,UAAU,EAAE,GAAI;QAACC,YAAY;QAAAb,QAAA,EAC3E,OAAOtB,KAAK,KAAK,QAAQ,GAAG,IAAIA,KAAK,CAACoC,cAAc,CAAC,CAAC,EAAE,GAAGpC;MAAK;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eACb9C,OAAA,CAACxB,UAAU;QAACyE,OAAO,EAAC,OAAO;QAACd,KAAK,EAAC,gBAAgB;QAAAG,QAAA,EAC/CL;MAAK;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACP;EAED,IAAIxC,OAAO,IAAI,CAACI,WAAW,EAAE;IAC3B,oBACEV,OAAA,CAACzB,GAAG;MAAC6D,EAAE,EAAE;QAAEiB,CAAC,EAAE;MAAE,CAAE;MAAAf,QAAA,gBAChBtC,OAAA,CAACxB,UAAU;QAACyE,OAAO,EAAC,IAAI;QAACb,EAAE,EAAE;UAAEM,EAAE,EAAE,CAAC;UAAEQ,UAAU,EAAE;QAAI,CAAE;QAAAZ,QAAA,EAAC;MAEzD;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb9C,OAAA,CAACT,cAAc;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC;EAEV;EAEA,oBACE9C,OAAA,CAACzB,GAAG;IAAC6D,EAAE,EAAE;MAAEiB,CAAC,EAAE;IAAE,CAAE;IAAAf,QAAA,gBAChBtC,OAAA,CAACzB,GAAG;MAAC6D,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEE,cAAc,EAAE,eAAe;QAAED,UAAU,EAAE,QAAQ;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzFtC,OAAA,CAACxB,UAAU;QAACyE,OAAO,EAAC,IAAI;QAACb,EAAE,EAAE;UAAEc,UAAU,EAAE;QAAI,CAAE;QAAAZ,QAAA,EAAC;MAElD;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb9C,OAAA,CAACzB,GAAG;QAAC6D,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEO,GAAG,EAAE;QAAE,CAAE;QAAAT,QAAA,gBACzDtC,OAAA,CAACxB,UAAU;UAACyE,OAAO,EAAC,SAAS;UAACd,KAAK,EAAC,gBAAgB;UAAAG,QAAA,GAAC,gBACrC,EAAC1B,WAAW,CAAC0C,kBAAkB,CAAC,CAAC;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eACb9C,OAAA,CAACV,UAAU;UAACiE,OAAO,EAAErC,gBAAiB;UAACsC,QAAQ,EAAElD,OAAQ;UAAAgC,QAAA,eACvDtC,OAAA,CAACH,OAAO;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACb9C,OAAA,CAACpB,MAAM;UAACqE,OAAO,EAAC,UAAU;UAACQ,SAAS,eAAEzD,OAAA,CAACJ,QAAQ;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAR,QAAA,EAAC;QAEpD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9C,OAAA,CAACvB,IAAI;MAAC2D,EAAE,EAAE;QAAEM,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eAClBtC,OAAA,CAACtB,WAAW;QAAA4D,QAAA,eACVtC,OAAA,CAACzB,GAAG;UAAC6D,EAAE,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEE,cAAc,EAAE,eAAe;YAAED,UAAU,EAAE;UAAS,CAAE;UAAAF,QAAA,gBAClFtC,OAAA,CAACxB,UAAU;YAACyE,OAAO,EAAC,IAAI;YAAAX,QAAA,EAAC;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACjD9C,OAAA,CAACnB,WAAW;YAACoE,OAAO,EAAC,UAAU;YAACS,IAAI,EAAC,OAAO;YAAApB,QAAA,EACzCvB,OAAO,CAAC4C,GAAG,CAAEN,CAAC,iBACbrD,OAAA,CAACpB,MAAM;cAELqE,OAAO,EAAEzC,MAAM,KAAK6C,CAAC,CAACrC,KAAK,GAAG,WAAW,GAAG,UAAW;cACvDuC,OAAO,EAAEA,CAAA,KAAM9C,SAAS,CAAC4C,CAAC,CAACrC,KAAK,CAAE;cAAAsB,QAAA,EAEjCe,CAAC,CAACpC;YAAK,GAJHoC,CAAC,CAACrC,KAAK;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKN,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGP9C,OAAA,CAACrB,IAAI;MAACiF,SAAS;MAACC,OAAO,EAAE,CAAE;MAACzB,EAAE,EAAE;QAAEM,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACxCtC,OAAA,CAACrB,IAAI;QAACmF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA1B,QAAA,eACvBtC,OAAA,CAACgC,QAAQ;UACPC,KAAK,EAAE,mBAAA9B,aAAA,GAAkBY,OAAO,CAACkD,IAAI,CAACZ,CAAC,IAAIA,CAAC,CAACrC,KAAK,KAAKR,MAAM,CAAC,cAAAL,aAAA,uBAArCA,aAAA,CAAuCc,KAAK,GAAI;UACzED,KAAK,EAAEN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEa,KAAM;UAC1BC,MAAM,EAAEd,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEc,MAAO;UAC5BU,IAAI,eAAElC,OAAA,CAACN,WAAW;YAACsD,QAAQ,EAAC;UAAO;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvCX,KAAK,EAAC;QAAS;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACP9C,OAAA,CAACrB,IAAI;QAACmF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA1B,QAAA,eACvBtC,OAAA,CAACgC,QAAQ;UACPC,KAAK,EAAC,uBAAuB;UAC7BjB,KAAK,EAAEN,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEa,KAAK,GAAG2C,IAAI,CAACC,KAAK,CAACzD,WAAW,CAACa,KAAK,IAAIf,MAAM,KAAK,IAAI,GAAG,CAAC,GAAGA,MAAM,KAAK,KAAK,GAAG,EAAE,GAAGA,MAAM,KAAK,KAAK,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,CAAE;UAC5IgB,MAAM,EAAEd,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEc,MAAO;UAC5BU,IAAI,eAAElC,OAAA,CAACL,SAAS;YAACqD,QAAQ,EAAC;UAAO;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACrCX,KAAK,EAAC;QAAS;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACP9C,OAAA,CAACrB,IAAI;QAACmF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA1B,QAAA,eACvBtC,OAAA,CAACgC,QAAQ;UACPC,KAAK,EAAC,aAAa;UACnBjB,KAAK,EAAE,GAAG,CAAAN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEc,MAAM,KAAI,CAAC,GAAI;UACtCA,MAAM,EAAEd,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEc,MAAO;UAC5BU,IAAI,eAAElC,OAAA,CAACR,UAAU;YAACwD,QAAQ,EAAC;UAAO;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtCX,KAAK,EAAC;QAAM;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP9C,OAAA,CAACrB,IAAI;MAACiF,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAvB,QAAA,gBACzBtC,OAAA,CAACrB,IAAI;QAACmF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA1B,QAAA,eACvBtC,OAAA,CAACvB,IAAI;UAAA6D,QAAA,eACHtC,OAAA,CAACtB,WAAW;YAAA4D,QAAA,gBACVtC,OAAA,CAACxB,UAAU;cAACyE,OAAO,EAAC,IAAI;cAACE,YAAY;cAAAb,QAAA,EAAC;YAEtC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb9C,OAAA,CAACd,cAAc;cAAAoD,QAAA,eACbtC,OAAA,CAACjB,KAAK;gBAAAuD,QAAA,gBACJtC,OAAA,CAACb,SAAS;kBAAAmD,QAAA,eACRtC,OAAA,CAACZ,QAAQ;oBAAAkD,QAAA,gBACPtC,OAAA,CAACf,SAAS;sBAAAqD,QAAA,EAAC;oBAAQ;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC/B9C,OAAA,CAACf,SAAS;sBAACmF,KAAK,EAAC,OAAO;sBAAA9B,QAAA,EAAC;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC3C9C,OAAA,CAACf,SAAS;sBAACmF,KAAK,EAAC,OAAO;sBAAA9B,QAAA,EAAC;oBAAU;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC/C9C,OAAA,CAACf,SAAS;sBAACmF,KAAK,EAAC,OAAO;sBAAA9B,QAAA,EAAC;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACZ9C,OAAA,CAAChB,SAAS;kBAAAsD,QAAA,EACP5B,WAAW,aAAXA,WAAW,wBAAAN,qBAAA,GAAXM,WAAW,CAAEgB,SAAS,cAAAtB,qBAAA,uBAAtBA,qBAAA,CAAwBuD,GAAG,CAAEG,IAAI,iBAChC9D,OAAA,CAACZ,QAAQ;oBAAqBiF,KAAK;oBAAA/B,QAAA,gBACjCtC,OAAA,CAACf,SAAS;sBAAAqD,QAAA,eACRtC,OAAA,CAACxB,UAAU;wBAACyE,OAAO,EAAC,WAAW;wBAACC,UAAU,EAAE,GAAI;wBAAAZ,QAAA,EAC7CwB,IAAI,CAACnC;sBAAQ;wBAAAgB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACZ9C,OAAA,CAACf,SAAS;sBAACmF,KAAK,EAAC,OAAO;sBAAA9B,QAAA,eACtBtC,OAAA,CAACxB,UAAU;wBAACyE,OAAO,EAAC,WAAW;wBAACC,UAAU,EAAE,GAAI;wBAAAZ,QAAA,GAAC,GAC9C,EAACwB,IAAI,CAAClC,MAAM,CAACwB,cAAc,CAAC,CAAC;sBAAA;wBAAAT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACZ9C,OAAA,CAACf,SAAS;sBAACmF,KAAK,EAAC,OAAO;sBAAA9B,QAAA,eACtBtC,OAAA,CAACxB,UAAU;wBAACyE,OAAO,EAAC,OAAO;wBAAAX,QAAA,GACxBwB,IAAI,CAACjC,UAAU,CAACyC,OAAO,CAAC,CAAC,CAAC,EAAC,GAC9B;sBAAA;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACZ9C,OAAA,CAACf,SAAS;sBAACmF,KAAK,EAAC,OAAO;sBAAA9B,QAAA,eACtBtC,OAAA,CAACX,IAAI;wBACH4B,KAAK,EAAE,GAAG6C,IAAI,CAACtC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAGsC,IAAI,CAACtC,MAAM,GAAI;wBACtDW,KAAK,EAAE2B,IAAI,CAACtC,MAAM,GAAG,CAAC,GAAG,SAAS,GAAGsC,IAAI,CAACtC,MAAM,GAAG,CAAC,GAAG,OAAO,GAAG,SAAU;wBAC3EkC,IAAI,EAAC,OAAO;wBACZxB,IAAI,EAAE4B,IAAI,CAACtC,MAAM,GAAG,CAAC,gBAAGxB,OAAA,CAACR,UAAU;0BAAAmD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,GAAGgB,IAAI,CAACtC,MAAM,GAAG,CAAC,gBAAGxB,OAAA,CAACP,YAAY;0BAAAkD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,GAAG;sBAAK;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACO,CAAC;kBAAA,GAvBCgB,IAAI,CAACnC,QAAQ;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAwBlB,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP9C,OAAA,CAACrB,IAAI;QAACmF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA1B,QAAA,eACvBtC,OAAA,CAACvB,IAAI;UAAC2D,EAAE,EAAE;YAAEC,MAAM,EAAE;UAAO,CAAE;UAAAC,QAAA,eAC3BtC,OAAA,CAACtB,WAAW;YAAA4D,QAAA,gBACVtC,OAAA,CAACxB,UAAU;cAACyE,OAAO,EAAC,IAAI;cAACE,YAAY;cAAAb,QAAA,EAAC;YAEtC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb9C,OAAA,CAACzB,GAAG;cAAC6D,EAAE,EAAE;gBAAEG,OAAO,EAAE,MAAM;gBAAEgC,aAAa,EAAE,QAAQ;gBAAExB,GAAG,EAAE;cAAE,CAAE;cAAAT,QAAA,EAC3D5B,WAAW,aAAXA,WAAW,wBAAAL,qBAAA,GAAXK,WAAW,CAAEoB,YAAY,cAAAzB,qBAAA,uBAAzBA,qBAAA,CAA2BmE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACb,GAAG,CAAC,CAACc,GAAG,EAAEC,KAAK,kBACnD1E,OAAA,CAACzB,GAAG;gBAAgB6D,EAAE,EAAE;kBAAEG,OAAO,EAAE,MAAM;kBAAEE,cAAc,EAAE,eAAe;kBAAED,UAAU,EAAE;gBAAS,CAAE;gBAAAF,QAAA,gBACjGtC,OAAA,CAACxB,UAAU;kBAACyE,OAAO,EAAC,OAAO;kBAACd,KAAK,EAAC,gBAAgB;kBAAAG,QAAA,EAC/C,IAAIxB,IAAI,CAAC2D,GAAG,CAAC1C,IAAI,CAAC,CAAC4C,kBAAkB,CAAC;gBAAC;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC,eACb9C,OAAA,CAACxB,UAAU;kBAACyE,OAAO,EAAC,WAAW;kBAACC,UAAU,EAAE,GAAI;kBAAAZ,QAAA,GAAC,GAC9C,EAACmC,GAAG,CAAC7C,MAAM,CAACwB,cAAc,CAAC,CAAC;gBAAA;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC;cAAA,GANL2B,GAAG,CAAC1C,IAAI;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOb,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC5C,EAAA,CA/OID,YAAY;AAAA2E,EAAA,GAAZ3E,YAAY;AAiPlB,eAAeA,YAAY;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}