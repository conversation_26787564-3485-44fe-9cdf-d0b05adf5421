import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { CssBaseline } from '@mui/material';

// Layouts
import Layout from './components/Layout';
import AdminLayout from './pages/admin/AdminLayout';

// Customer Pages
import { LandingPage } from './pages/customer/LandingPage';
import LoginRegisterPage from './pages/customer/LoginRegisterPage';
import MenuPage from './pages/customer/MenuPage';
import CartPage from './pages/customer/CartPage';

// Admin Pages
import AdminDashboard from './pages/admin/AdminDashboard';
import AdminOrders from './pages/admin/AdminOrders';
import AdminMenu from './pages/admin/AdminMenu';
import AdminStaff from './pages/admin/AdminStaff';
import AdminRevenue from './pages/admin/AdminRevenue';
import AdminInventory from './pages/admin/AdminInventory';
import AdminAnalytics from './pages/admin/AdminAnalytics';
import AdminSettings from './pages/admin/AdminSettings';
import AddMenuItemForm from './pages/admin/AddMenuItemForm';

// Route Protection Components
import ProtectedRoute from './components/ProtectedRoute';
import AdminRoute from './components/AdminRoute';

// Error Pages
import NotFound from './pages/NotFound';

function App() {
  return (
    <>
      <CssBaseline />
      <Routes>
        {/* Customer Routes */}
        <Route path="/" element={<Layout />}>
          <Route index element={<LandingPage />} />
          <Route path="login-register" element={<LoginRegisterPage />} />
          <Route path="menu" element={<MenuPage />} />
          <Route
            path="cart"
            element={
              <ProtectedRoute>
                <CartPage />
              </ProtectedRoute>
            }
          />
        </Route>

        {/* Admin Routes */}
        <Route
          path="/admin"
          element={
            <AdminRoute>
              <AdminLayout />
            </AdminRoute>
          }
        >
          <Route index element={<AdminDashboard />} />
          <Route path="dashboard" element={<Navigate to="/admin" replace />} />
          <Route path="orders" element={<AdminOrders />} />
          <Route path="menu" element={<AdminMenu />} />
          <Route path="menu/add" element={<AddMenuItemForm />} />
          <Route path="menu/edit/:id" element={<AddMenuItemForm />} />
          <Route path="staff" element={<AdminStaff />} />
          <Route path="revenue" element={<AdminRevenue />} />
          <Route path="inventory" element={<AdminInventory />} />
          <Route path="analytics" element={<AdminAnalytics />} />
          <Route path="settings" element={<AdminSettings />} />
        </Route>

        {/* Legacy redirect for old menu/add route */}
        <Route
          path="/menu/add"
          element={
            <AdminRoute>
              <Navigate to="/admin/menu/add" replace />
            </AdminRoute>
          }
        />

        {/* 404 Page */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </>
  );
}

export default App;
