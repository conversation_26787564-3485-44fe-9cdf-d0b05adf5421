{"timestamp":"2025-06-28T14:42:39.946Z","level":"ERROR","message":"MongoDB connection failed","error":"option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Github_Repos\\cafe-mangement-system-react\\server\\node_modules\\mongodb\\lib\\connection_string.js:272:15)\n    at new MongoClient (D:\\Github_Repos\\cafe-mangement-system-react\\server\\node_modules\\mongodb\\lib\\mongo_client.js:51:63)\n    at NativeConnection.createClient (D:\\Github_Repos\\cafe-mangement-system-react\\server\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:306:14)\n    at NativeConnection.openUri (D:\\Github_Repos\\cafe-mangement-system-react\\server\\node_modules\\mongoose\\lib\\connection.js:822:34)\n    at Mongoose.connect (D:\\Github_Repos\\cafe-mangement-system-react\\server\\node_modules\\mongoose\\lib\\mongoose.js:429:15)\n    at Object.<anonymous> (D:\\Github_Repos\\cafe-mangement-system-react\\server\\index.js:146:10)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object..js (node:internal/modules/cjs/loader:1699:10)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)"}
{"timestamp":"2025-06-28T14:43:00.920Z","level":"ERROR","message":"MongoDB connection failed","error":"option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\Github_Repos\\cafe-mangement-system-react\\server\\node_modules\\mongodb\\lib\\connection_string.js:272:15)\n    at new MongoClient (D:\\Github_Repos\\cafe-mangement-system-react\\server\\node_modules\\mongodb\\lib\\mongo_client.js:51:63)\n    at NativeConnection.createClient (D:\\Github_Repos\\cafe-mangement-system-react\\server\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:306:14)\n    at NativeConnection.openUri (D:\\Github_Repos\\cafe-mangement-system-react\\server\\node_modules\\mongoose\\lib\\connection.js:822:34)\n    at Mongoose.connect (D:\\Github_Repos\\cafe-mangement-system-react\\server\\node_modules\\mongoose\\lib\\mongoose.js:429:15)\n    at Object.<anonymous> (D:\\Github_Repos\\cafe-mangement-system-react\\server\\index.js:146:10)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object..js (node:internal/modules/cjs/loader:1699:10)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)"}
{"timestamp":"2025-06-28T14:43:31.941Z","level":"ERROR","message":"Server error","error":"listen EADDRINUSE: address already in use :::4969","stack":"Error: listen EADDRINUSE: address already in use :::4969\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (D:\\Github_Repos\\cafe-mangement-system-react\\server\\node_modules\\express\\lib\\application.js:635:24)\n    at D:\\Github_Repos\\cafe-mangement-system-react\\server\\index.js:151:24\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"}
{"timestamp":"2025-06-28T14:45:17.154Z","level":"ERROR","message":"Server error","error":"listen EADDRINUSE: address already in use :::4969","stack":"Error: listen EADDRINUSE: address already in use :::4969\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (D:\\Github_Repos\\cafe-mangement-system-react\\server\\node_modules\\express\\lib\\application.js:635:24)\n    at D:\\Github_Repos\\cafe-mangement-system-react\\server\\index.js:151:24\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"}
{"timestamp":"2025-06-28T14:48:51.123Z","level":"ERROR","message":"Server error","error":"listen EADDRINUSE: address already in use :::4969","stack":"Error: listen EADDRINUSE: address already in use :::4969\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (D:\\Github_Repos\\cafe-mangement-system-react\\server\\node_modules\\express\\lib\\application.js:635:24)\n    at D:\\Github_Repos\\cafe-mangement-system-react\\server\\index.js:151:24\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"}
