// API Configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:4969/api';

// Helper function to get auth token
const getAuthToken = () => {
  return localStorage.getItem('authToken');
};

// Helper function to create headers
const createHeaders = (includeAuth = true) => {
  const headers = {
    'Content-Type': 'application/json',
  };
  
  if (includeAuth) {
    const token = getAuthToken();
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
  }
  
  return headers;
};

// Generic API request function
const apiRequest = async (endpoint, options = {}) => {
  const url = `${API_BASE_URL}${endpoint}`;
  const config = {
    headers: createHeaders(options.auth !== false),
    ...options,
  };

  try {
    const response = await fetch(url, config);
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error(`API request failed for ${endpoint}:`, error);
    throw error;
  }
};

// Authentication API
export const authAPI = {
  login: (credentials) => 
    apiRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
      auth: false,
    }),
    
  register: (userData) => 
    apiRequest('/auth/register/customer', {
      method: 'POST',
      body: JSON.stringify(userData),
      auth: false,
    }),
    
  registerStaff: (userData) => 
    apiRequest('/auth/register/staff', {
      method: 'POST',
      body: JSON.stringify(userData),
    }),
    
  updateProfile: (userData) => 
    apiRequest('/auth/profile', {
      method: 'PUT',
      body: JSON.stringify(userData),
    }),
    
  deleteProfile: () => 
    apiRequest('/auth/profile', {
      method: 'DELETE',
    }),
};

// Menu API
export const menuAPI = {
  getAll: () => apiRequest('/menu', { auth: false }),
  
  getById: (id) => apiRequest(`/menu/${id}`, { auth: false }),
  
  create: (menuData) => 
    apiRequest('/menu', {
      method: 'POST',
      body: JSON.stringify(menuData),
    }),
    
  update: (id, menuData) => 
    apiRequest(`/menu/${id}`, {
      method: 'PUT',
      body: JSON.stringify(menuData),
    }),
    
  delete: (id) => 
    apiRequest(`/menu/${id}`, {
      method: 'DELETE',
    }),
};

// Orders API
export const ordersAPI = {
  getAll: () => apiRequest('/orders'),
  
  getById: (id) => apiRequest(`/orders/${id}`),
  
  create: (orderData) => 
    apiRequest('/orders', {
      method: 'POST',
      body: JSON.stringify(orderData),
    }),
    
  update: (id, orderData) => 
    apiRequest(`/orders/${id}`, {
      method: 'PUT',
      body: JSON.stringify(orderData),
    }),
    
  updateStatus: (id, status) => 
    apiRequest(`/orders/${id}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ status }),
    }),
    
  delete: (id) => 
    apiRequest(`/orders/${id}`, {
      method: 'DELETE',
    }),
    
  getByCustomer: (customerId) => 
    apiRequest(`/orders/customer/${customerId}`),
    
  getRecent: (limit = 10) => 
    apiRequest(`/orders?limit=${limit}&sort=-createdAt`),
};

// Staff API
export const staffAPI = {
  getAll: () => apiRequest('/staff-admin'),
  
  add: (staffData) => 
    apiRequest('/staff-admin', {
      method: 'POST',
      body: JSON.stringify(staffData),
    }),
    
  update: (id, staffData) => 
    apiRequest(`/staff-admin/${id}`, {
      method: 'PUT',
      body: JSON.stringify(staffData),
    }),
    
  remove: (id) => 
    apiRequest(`/staff-admin/${id}`, {
      method: 'DELETE',
    }),
};

// Customers API
export const customersAPI = {
  getAll: () => apiRequest('/customers'),
  
  getById: (id) => apiRequest(`/customers/${id}`),
  
  update: (id, customerData) => 
    apiRequest(`/customers/${id}`, {
      method: 'PUT',
      body: JSON.stringify(customerData),
    }),
    
  delete: (id) => 
    apiRequest(`/customers/${id}`, {
      method: 'DELETE',
    }),
};

// Dashboard/Analytics API (these might need to be implemented on the backend)
export const analyticsAPI = {
  getDashboardStats: () => 
    apiRequest('/analytics/dashboard-stats').catch(() => {
      // Fallback to mock data if endpoint doesn't exist
      return {
        totalRevenue: 12426,
        ordersToday: 147,
        menuItems: 42,
        inventoryItems: 156,
        lowStockItems: 3,
        pendingOrders: 12,
        revenueChange: 12.5,
        ordersChange: 8.2,
      };
    }),
    
  getRevenueStats: (period = '30d') => 
    apiRequest(`/analytics/revenue?period=${period}`).catch(() => {
      // Mock data fallback
      return {
        total: 12426,
        change: 12.5,
        chartData: [],
      };
    }),
    
  getOrderStats: (period = '30d') => 
    apiRequest(`/analytics/orders?period=${period}`).catch(() => {
      // Mock data fallback
      return {
        total: 147,
        change: 8.2,
        chartData: [],
      };
    }),
    
  getInventoryAlerts: () => 
    apiRequest('/analytics/inventory-alerts').catch(() => {
      // Mock data fallback
      return [
        {
          id: 1,
          item: 'Coffee beans',
          currentStock: 5,
          minStock: 10,
          unit: 'lbs',
          severity: 'high',
        },
        {
          id: 2,
          item: 'Milk',
          currentStock: 2,
          minStock: 5,
          unit: 'gallons',
          severity: 'medium',
        },
      ];
    }),
};

// Health check
export const healthAPI = {
  check: () => apiRequest('/health', { auth: false }),
};

export default {
  auth: authAPI,
  menu: menuAPI,
  orders: ordersAPI,
  staff: staffAPI,
  customers: customersAPI,
  analytics: analyticsAPI,
  health: healthAPI,
};
