{"ast": null, "code": "var _jsxFileName = \"D:\\\\Github_Repos\\\\cafe-mangement-system-react\\\\cafe-management-sys\\\\src\\\\components\\\\AdminRoute.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { Box, CircularProgress, Typography, Alert, Button } from '@mui/material';\nimport { AdminPanelSettings } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AdminRoute = ({\n  children\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    isAdmin,\n    loading,\n    user\n  } = useAuth();\n  const location = useLocation();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '100vh',\n        gap: 2,\n        bgcolor: 'background.default'\n      },\n      children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 40\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        children: \"Verifying admin access...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this);\n  }\n  if (!isAuthenticated) {\n    // Redirect to login page with return url\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login-register\",\n      state: {\n        from: location,\n        requireAdmin: true\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this);\n  }\n  if (!isAdmin()) {\n    // User is authenticated but not an admin\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '100vh',\n        gap: 3,\n        bgcolor: 'background.default',\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(AdminPanelSettings, {\n        sx: {\n          fontSize: 80,\n          color: 'error.main'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          maxWidth: 500,\n          '& .MuiAlert-message': {\n            textAlign: 'center'\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Access Denied\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mb: 2\n          },\n          children: [\"You don't have permission to access the admin panel.\", user && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 17\n            }, this), \"Current role: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: user.role\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 31\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          onClick: () => window.history.back(),\n          sx: {\n            mt: 1\n          },\n          children: \"Go Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this);\n  }\n  return children;\n};\n_s(AdminRoute, \"xleGs/giz0Vm00OR0GYlgwZLSVc=\", false, function () {\n  return [useAuth, useLocation];\n});\n_c = AdminRoute;\nexport default AdminRoute;\nvar _c;\n$RefreshReg$(_c, \"AdminRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useAuth", "Box", "CircularProgress", "Typography", "<PERSON><PERSON>", "<PERSON><PERSON>", "AdminPanelSettings", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AdminRoute", "children", "_s", "isAuthenticated", "isAdmin", "loading", "user", "location", "sx", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "gap", "bgcolor", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "color", "to", "state", "from", "requireAdmin", "replace", "p", "fontSize", "severity", "max<PERSON><PERSON><PERSON>", "textAlign", "gutterBottom", "mb", "role", "onClick", "window", "history", "back", "mt", "_c", "$RefreshReg$"], "sources": ["D:/Github_Repos/cafe-mangement-system-react/cafe-management-sys/src/components/AdminRoute.jsx"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { Box, CircularProgress, Typography, Alert, Button } from '@mui/material';\nimport { AdminPanelSettings } from '@mui/icons-material';\n\nconst AdminRoute = ({ children }) => {\n  const { isAuthenticated, isAdmin, loading, user } = useAuth();\n  const location = useLocation();\n\n  if (loading) {\n    return (\n      <Box\n        sx={{\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          minHeight: '100vh',\n          gap: 2,\n          bgcolor: 'background.default',\n        }}\n      >\n        <CircularProgress size={40} />\n        <Typography variant=\"body2\" color=\"text.secondary\">\n          Verifying admin access...\n        </Typography>\n      </Box>\n    );\n  }\n\n  if (!isAuthenticated) {\n    // Redirect to login page with return url\n    return (\n      <Navigate \n        to=\"/login-register\" \n        state={{ from: location, requireAdmin: true }} \n        replace \n      />\n    );\n  }\n\n  if (!isAdmin()) {\n    // User is authenticated but not an admin\n    return (\n      <Box\n        sx={{\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          minHeight: '100vh',\n          gap: 3,\n          bgcolor: 'background.default',\n          p: 3,\n        }}\n      >\n        <AdminPanelSettings sx={{ fontSize: 80, color: 'error.main' }} />\n        <Alert \n          severity=\"error\" \n          sx={{ \n            maxWidth: 500,\n            '& .MuiAlert-message': {\n              textAlign: 'center',\n            }\n          }}\n        >\n          <Typography variant=\"h6\" gutterBottom>\n            Access Denied\n          </Typography>\n          <Typography variant=\"body2\" sx={{ mb: 2 }}>\n            You don't have permission to access the admin panel. \n            {user && (\n              <>\n                <br />\n                Current role: <strong>{user.role}</strong>\n              </>\n            )}\n          </Typography>\n          <Button \n            variant=\"contained\" \n            color=\"primary\" \n            onClick={() => window.history.back()}\n            sx={{ mt: 1 }}\n          >\n            Go Back\n          </Button>\n        </Alert>\n      </Box>\n    );\n  }\n\n  return children;\n};\n\nexport default AdminRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,GAAG,EAAEC,gBAAgB,EAAEC,UAAU,EAAEC,KAAK,EAAEC,MAAM,QAAQ,eAAe;AAChF,SAASC,kBAAkB,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzD,MAAMC,UAAU,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACnC,MAAM;IAAEC,eAAe;IAAEC,OAAO;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGjB,OAAO,CAAC,CAAC;EAC7D,MAAMkB,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAE9B,IAAIiB,OAAO,EAAE;IACX,oBACER,OAAA,CAACP,GAAG;MACFkB,EAAE,EAAE;QACFC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,OAAO;QAClBC,GAAG,EAAE,CAAC;QACNC,OAAO,EAAE;MACX,CAAE;MAAAd,QAAA,gBAEFJ,OAAA,CAACN,gBAAgB;QAACyB,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9BvB,OAAA,CAACL,UAAU;QAAC6B,OAAO,EAAC,OAAO;QAACC,KAAK,EAAC,gBAAgB;QAAArB,QAAA,EAAC;MAEnD;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,IAAI,CAACjB,eAAe,EAAE;IACpB;IACA,oBACEN,OAAA,CAACV,QAAQ;MACPoC,EAAE,EAAC,iBAAiB;MACpBC,KAAK,EAAE;QAAEC,IAAI,EAAElB,QAAQ;QAAEmB,YAAY,EAAE;MAAK,CAAE;MAC9CC,OAAO;IAAA;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEN;EAEA,IAAI,CAAChB,OAAO,CAAC,CAAC,EAAE;IACd;IACA,oBACEP,OAAA,CAACP,GAAG;MACFkB,EAAE,EAAE;QACFC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,OAAO;QAClBC,GAAG,EAAE,CAAC;QACNC,OAAO,EAAE,oBAAoB;QAC7Ba,CAAC,EAAE;MACL,CAAE;MAAA3B,QAAA,gBAEFJ,OAAA,CAACF,kBAAkB;QAACa,EAAE,EAAE;UAAEqB,QAAQ,EAAE,EAAE;UAAEP,KAAK,EAAE;QAAa;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjEvB,OAAA,CAACJ,KAAK;QACJqC,QAAQ,EAAC,OAAO;QAChBtB,EAAE,EAAE;UACFuB,QAAQ,EAAE,GAAG;UACb,qBAAqB,EAAE;YACrBC,SAAS,EAAE;UACb;QACF,CAAE;QAAA/B,QAAA,gBAEFJ,OAAA,CAACL,UAAU;UAAC6B,OAAO,EAAC,IAAI;UAACY,YAAY;UAAAhC,QAAA,EAAC;QAEtC;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvB,OAAA,CAACL,UAAU;UAAC6B,OAAO,EAAC,OAAO;UAACb,EAAE,EAAE;YAAE0B,EAAE,EAAE;UAAE,CAAE;UAAAjC,QAAA,GAAC,sDAEzC,EAACK,IAAI,iBACHT,OAAA,CAAAE,SAAA;YAAAE,QAAA,gBACEJ,OAAA;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,kBACQ,eAAAvB,OAAA;cAAAI,QAAA,EAASK,IAAI,CAAC6B;YAAI;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC;UAAA,eAC1C,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,eACbvB,OAAA,CAACH,MAAM;UACL2B,OAAO,EAAC,WAAW;UACnBC,KAAK,EAAC,SAAS;UACfc,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;UACrC/B,EAAE,EAAE;YAAEgC,EAAE,EAAE;UAAE,CAAE;UAAAvC,QAAA,EACf;QAED;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;EAEA,OAAOnB,QAAQ;AACjB,CAAC;AAACC,EAAA,CAvFIF,UAAU;EAAA,QACsCX,OAAO,EAC1CD,WAAW;AAAA;AAAAqD,EAAA,GAFxBzC,UAAU;AAyFhB,eAAeA,UAAU;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}