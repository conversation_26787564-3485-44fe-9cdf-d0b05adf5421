import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { Box, CircularProgress, Typography, Alert, Button } from '@mui/material';
import { AdminPanelSettings } from '@mui/icons-material';

const AdminRoute = ({ children }) => {
  const { isAuthenticated, isAdmin, loading, user } = useAuth();
  const location = useLocation();

  if (loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          gap: 2,
          bgcolor: 'background.default',
        }}
      >
        <CircularProgress size={40} />
        <Typography variant="body2" color="text.secondary">
          Verifying admin access...
        </Typography>
      </Box>
    );
  }

  if (!isAuthenticated) {
    // Redirect to login page with return url
    return (
      <Navigate 
        to="/login-register" 
        state={{ from: location, requireAdmin: true }} 
        replace 
      />
    );
  }

  if (!isAdmin()) {
    // User is authenticated but not an admin
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          gap: 3,
          bgcolor: 'background.default',
          p: 3,
        }}
      >
        <AdminPanelSettings sx={{ fontSize: 80, color: 'error.main' }} />
        <Alert 
          severity="error" 
          sx={{ 
            maxWidth: 500,
            '& .MuiAlert-message': {
              textAlign: 'center',
            }
          }}
        >
          <Typography variant="h6" gutterBottom>
            Access Denied
          </Typography>
          <Typography variant="body2" sx={{ mb: 2 }}>
            You don't have permission to access the admin panel. 
            {user && (
              <>
                <br />
                Current role: <strong>{user.role}</strong>
              </>
            )}
          </Typography>
          <Button 
            variant="contained" 
            color="primary" 
            onClick={() => window.history.back()}
            sx={{ mt: 1 }}
          >
            Go Back
          </Button>
        </Alert>
      </Box>
    );
  }

  return children;
};

export default AdminRoute;
