{"ast": null, "code": "// API Configuration\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:4969/api';\n\n// Helper function to get auth token (using sessionStorage for better security)\nconst getAuthToken = () => {\n  return sessionStorage.getItem('token') || localStorage.getItem('token');\n};\n\n// Helper function to create headers\nconst createHeaders = (includeAuth = true, isFormData = false) => {\n  const headers = {};\n\n  // Don't set Content-Type for FormData - browser will set it with boundary\n  if (!isFormData) {\n    headers['Content-Type'] = 'application/json';\n  }\n  if (includeAuth) {\n    const token = getAuthToken();\n    if (token) {\n      headers.Authorization = `Bearer ${token}`;\n    }\n  }\n  return headers;\n};\n\n// Generic API request function with enhanced error handling\nconst apiRequest = async (endpoint, options = {}) => {\n  const url = `${API_BASE_URL}${endpoint}`;\n  const config = {\n    headers: createHeaders(options.auth !== false, options.isFormData),\n    ...options\n  };\n  try {\n    const response = await fetch(url, config);\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n\n      // Handle specific HTTP status codes\n      switch (response.status) {\n        case 400:\n          throw new Error(errorData.message || 'Invalid request data');\n        case 401:\n          throw new Error(errorData.message || 'Authentication required');\n        case 403:\n          throw new Error(errorData.message || 'Access denied');\n        case 404:\n          throw new Error(errorData.message || 'Resource not found');\n        case 409:\n          throw new Error(errorData.message || 'Resource already exists');\n        case 429:\n          throw new Error(errorData.message || 'Too many requests. Please try again later.');\n        case 500:\n          throw new Error(errorData.message || 'Server error. Please try again later.');\n        default:\n          throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n      }\n    }\n    return await response.json();\n  } catch (error) {\n    console.error(`API request failed for ${endpoint}:`, error);\n    throw error;\n  }\n};\n\n// Authentication API - Updated to match LoginRegisterPage implementation\nexport const authAPI = {\n  // Login with enhanced error handling\n  login: credentials => apiRequest('/auth/login', {\n    method: 'POST',\n    body: JSON.stringify(credentials),\n    auth: false\n  }),\n  // Customer registration with FormData support for file uploads\n  registerCustomer: formData => apiRequest('/auth/register/customer', {\n    method: 'POST',\n    body: formData,\n    // FormData object for file upload\n    auth: false,\n    isFormData: true\n  }),\n  // Staff registration with FormData support for file uploads\n  registerStaff: formData => apiRequest('/auth/register/staff', {\n    method: 'POST',\n    body: formData,\n    // FormData object for file upload\n    auth: false,\n    isFormData: true\n  }),\n  // Profile update with FormData support\n  updateProfile: formData => apiRequest('/auth/profile', {\n    method: 'PUT',\n    body: formData,\n    isFormData: true\n  }),\n  // Delete profile\n  deleteProfile: () => apiRequest('/auth/profile', {\n    method: 'DELETE'\n  }),\n  // Logout helper function\n  logout: () => {\n    sessionStorage.removeItem('token');\n    sessionStorage.removeItem('userType');\n    sessionStorage.removeItem('user');\n    localStorage.removeItem('token'); // Clean up legacy storage\n    localStorage.removeItem('userType');\n    localStorage.removeItem('user');\n  },\n  // Get current user from storage\n  getCurrentUser: () => {\n    try {\n      const user = sessionStorage.getItem('user') || localStorage.getItem('user');\n      return user ? JSON.parse(user) : null;\n    } catch (error) {\n      console.error('Error parsing user data:', error);\n      return null;\n    }\n  },\n  // Check if user is authenticated\n  isAuthenticated: () => {\n    return !!(sessionStorage.getItem('token') || localStorage.getItem('token'));\n  },\n  // Get user type\n  getUserType: () => {\n    return sessionStorage.getItem('userType') || localStorage.getItem('userType');\n  }\n};\n\n// Menu API\nexport const menuAPI = {\n  getAll: () => apiRequest('/menu', {\n    auth: false\n  }),\n  getById: id => apiRequest(`/menu/${id}`, {\n    auth: false\n  }),\n  create: menuData => apiRequest('/menu', {\n    method: 'POST',\n    body: JSON.stringify(menuData)\n  }),\n  update: (id, menuData) => apiRequest(`/menu/${id}`, {\n    method: 'PUT',\n    body: JSON.stringify(menuData)\n  }),\n  delete: id => apiRequest(`/menu/${id}`, {\n    method: 'DELETE'\n  })\n};\n\n// Orders API\nexport const ordersAPI = {\n  getAll: () => apiRequest('/orders'),\n  getById: id => apiRequest(`/orders/${id}`),\n  create: orderData => apiRequest('/orders', {\n    method: 'POST',\n    body: JSON.stringify(orderData)\n  }),\n  update: (id, orderData) => apiRequest(`/orders/${id}`, {\n    method: 'PUT',\n    body: JSON.stringify(orderData)\n  }),\n  updateStatus: (id, status) => apiRequest(`/orders/${id}/status`, {\n    method: 'PATCH',\n    body: JSON.stringify({\n      status\n    })\n  }),\n  delete: id => apiRequest(`/orders/${id}`, {\n    method: 'DELETE'\n  }),\n  getByCustomer: customerId => apiRequest(`/orders/customer/${customerId}`),\n  getRecent: (limit = 10) => apiRequest(`/orders?limit=${limit}&sort=-createdAt`)\n};\n\n// Staff API\nexport const staffAPI = {\n  getAll: () => apiRequest('/staff-admin'),\n  add: staffData => apiRequest('/staff-admin', {\n    method: 'POST',\n    body: JSON.stringify(staffData)\n  }),\n  update: (id, staffData) => apiRequest(`/staff-admin/${id}`, {\n    method: 'PUT',\n    body: JSON.stringify(staffData)\n  }),\n  remove: id => apiRequest(`/staff-admin/${id}`, {\n    method: 'DELETE'\n  })\n};\n\n// Customers API\nexport const customersAPI = {\n  getAll: () => apiRequest('/customers'),\n  getById: id => apiRequest(`/customers/${id}`),\n  update: (id, customerData) => apiRequest(`/customers/${id}`, {\n    method: 'PUT',\n    body: JSON.stringify(customerData)\n  }),\n  delete: id => apiRequest(`/customers/${id}`, {\n    method: 'DELETE'\n  })\n};\n\n// Dashboard/Analytics API (these might need to be implemented on the backend)\nexport const analyticsAPI = {\n  getDashboardStats: () => apiRequest('/analytics/dashboard-stats').catch(() => {\n    // Fallback to mock data if endpoint doesn't exist\n    return {\n      totalRevenue: 12426,\n      ordersToday: 147,\n      menuItems: 42,\n      inventoryItems: 156,\n      lowStockItems: 3,\n      pendingOrders: 12,\n      revenueChange: 12.5,\n      ordersChange: 8.2\n    };\n  }),\n  getRevenueStats: (period = '30d') => apiRequest(`/analytics/revenue?period=${period}`).catch(() => {\n    // Mock data fallback\n    return {\n      total: 12426,\n      change: 12.5,\n      chartData: []\n    };\n  }),\n  getOrderStats: (period = '30d') => apiRequest(`/analytics/orders?period=${period}`).catch(() => {\n    // Mock data fallback\n    return {\n      total: 147,\n      change: 8.2,\n      chartData: []\n    };\n  }),\n  getInventoryAlerts: () => apiRequest('/analytics/inventory-alerts').catch(() => {\n    // Mock data fallback\n    return [{\n      id: 1,\n      item: 'Coffee beans',\n      currentStock: 5,\n      minStock: 10,\n      unit: 'lbs',\n      severity: 'high'\n    }, {\n      id: 2,\n      item: 'Milk',\n      currentStock: 2,\n      minStock: 5,\n      unit: 'gallons',\n      severity: 'medium'\n    }];\n  })\n};\n\n// Health check\nexport const healthAPI = {\n  check: () => apiRequest('/health', {\n    auth: false\n  })\n};\nexport default {\n  auth: authAPI,\n  menu: menuAPI,\n  orders: ordersAPI,\n  staff: staffAPI,\n  customers: customersAPI,\n  analytics: analyticsAPI,\n  health: healthAPI\n};", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_URL", "getAuthToken", "sessionStorage", "getItem", "localStorage", "createHeaders", "<PERSON><PERSON><PERSON>", "isFormData", "headers", "token", "Authorization", "apiRequest", "endpoint", "options", "url", "config", "auth", "response", "fetch", "ok", "errorData", "json", "catch", "status", "Error", "message", "error", "console", "authAPI", "login", "credentials", "method", "body", "JSON", "stringify", "registerCustomer", "formData", "registerStaff", "updateProfile", "deleteProfile", "logout", "removeItem", "getCurrentUser", "user", "parse", "isAuthenticated", "getUserType", "menuAPI", "getAll", "getById", "id", "create", "menuData", "update", "delete", "ordersAPI", "orderData", "updateStatus", "getByCustomer", "customerId", "getRecent", "limit", "staffAPI", "add", "staffData", "remove", "customersAPI", "customerData", "analyticsAPI", "getDashboardStats", "totalRevenue", "ordersToday", "menuItems", "inventoryItems", "lowStockItems", "pendingOrders", "revenueChange", "ordersChange", "getRevenueStats", "period", "total", "change", "chartData", "getOrderStats", "getInventoryAlerts", "item", "currentStock", "minStock", "unit", "severity", "healthAPI", "check", "menu", "orders", "staff", "customers", "analytics", "health"], "sources": ["D:/Github_Repos/cafe-mangement-system-react/cafe-management-sys/src/services/api.js"], "sourcesContent": ["// API Configuration\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:4969/api';\n\n// Helper function to get auth token (using sessionStorage for better security)\nconst getAuthToken = () => {\n  return sessionStorage.getItem('token') || localStorage.getItem('token');\n};\n\n// Helper function to create headers\nconst createHeaders = (includeAuth = true, isFormData = false) => {\n  const headers = {};\n\n  // Don't set Content-Type for FormData - browser will set it with boundary\n  if (!isFormData) {\n    headers['Content-Type'] = 'application/json';\n  }\n\n  if (includeAuth) {\n    const token = getAuthToken();\n    if (token) {\n      headers.Authorization = `Bearer ${token}`;\n    }\n  }\n\n  return headers;\n};\n\n// Generic API request function with enhanced error handling\nconst apiRequest = async (endpoint, options = {}) => {\n  const url = `${API_BASE_URL}${endpoint}`;\n  const config = {\n    headers: createHeaders(options.auth !== false, options.isFormData),\n    ...options,\n  };\n\n  try {\n    const response = await fetch(url, config);\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n\n      // Handle specific HTTP status codes\n      switch (response.status) {\n        case 400:\n          throw new Error(errorData.message || 'Invalid request data');\n        case 401:\n          throw new Error(errorData.message || 'Authentication required');\n        case 403:\n          throw new Error(errorData.message || 'Access denied');\n        case 404:\n          throw new Error(errorData.message || 'Resource not found');\n        case 409:\n          throw new Error(errorData.message || 'Resource already exists');\n        case 429:\n          throw new Error(errorData.message || 'Too many requests. Please try again later.');\n        case 500:\n          throw new Error(errorData.message || 'Server error. Please try again later.');\n        default:\n          throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n      }\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(`API request failed for ${endpoint}:`, error);\n    throw error;\n  }\n};\n\n// Authentication API - Updated to match LoginRegisterPage implementation\nexport const authAPI = {\n  // Login with enhanced error handling\n  login: (credentials) =>\n    apiRequest('/auth/login', {\n      method: 'POST',\n      body: JSON.stringify(credentials),\n      auth: false,\n    }),\n\n  // Customer registration with FormData support for file uploads\n  registerCustomer: (formData) =>\n    apiRequest('/auth/register/customer', {\n      method: 'POST',\n      body: formData, // FormData object for file upload\n      auth: false,\n      isFormData: true,\n    }),\n\n  // Staff registration with FormData support for file uploads\n  registerStaff: (formData) =>\n    apiRequest('/auth/register/staff', {\n      method: 'POST',\n      body: formData, // FormData object for file upload\n      auth: false,\n      isFormData: true,\n    }),\n\n  // Profile update with FormData support\n  updateProfile: (formData) =>\n    apiRequest('/auth/profile', {\n      method: 'PUT',\n      body: formData,\n      isFormData: true,\n    }),\n\n  // Delete profile\n  deleteProfile: () =>\n    apiRequest('/auth/profile', {\n      method: 'DELETE',\n    }),\n\n  // Logout helper function\n  logout: () => {\n    sessionStorage.removeItem('token');\n    sessionStorage.removeItem('userType');\n    sessionStorage.removeItem('user');\n    localStorage.removeItem('token'); // Clean up legacy storage\n    localStorage.removeItem('userType');\n    localStorage.removeItem('user');\n  },\n\n  // Get current user from storage\n  getCurrentUser: () => {\n    try {\n      const user = sessionStorage.getItem('user') || localStorage.getItem('user');\n      return user ? JSON.parse(user) : null;\n    } catch (error) {\n      console.error('Error parsing user data:', error);\n      return null;\n    }\n  },\n\n  // Check if user is authenticated\n  isAuthenticated: () => {\n    return !!(sessionStorage.getItem('token') || localStorage.getItem('token'));\n  },\n\n  // Get user type\n  getUserType: () => {\n    return sessionStorage.getItem('userType') || localStorage.getItem('userType');\n  },\n};\n\n// Menu API\nexport const menuAPI = {\n  getAll: () => apiRequest('/menu', { auth: false }),\n  \n  getById: (id) => apiRequest(`/menu/${id}`, { auth: false }),\n  \n  create: (menuData) => \n    apiRequest('/menu', {\n      method: 'POST',\n      body: JSON.stringify(menuData),\n    }),\n    \n  update: (id, menuData) => \n    apiRequest(`/menu/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(menuData),\n    }),\n    \n  delete: (id) => \n    apiRequest(`/menu/${id}`, {\n      method: 'DELETE',\n    }),\n};\n\n// Orders API\nexport const ordersAPI = {\n  getAll: () => apiRequest('/orders'),\n  \n  getById: (id) => apiRequest(`/orders/${id}`),\n  \n  create: (orderData) => \n    apiRequest('/orders', {\n      method: 'POST',\n      body: JSON.stringify(orderData),\n    }),\n    \n  update: (id, orderData) => \n    apiRequest(`/orders/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(orderData),\n    }),\n    \n  updateStatus: (id, status) => \n    apiRequest(`/orders/${id}/status`, {\n      method: 'PATCH',\n      body: JSON.stringify({ status }),\n    }),\n    \n  delete: (id) => \n    apiRequest(`/orders/${id}`, {\n      method: 'DELETE',\n    }),\n    \n  getByCustomer: (customerId) => \n    apiRequest(`/orders/customer/${customerId}`),\n    \n  getRecent: (limit = 10) => \n    apiRequest(`/orders?limit=${limit}&sort=-createdAt`),\n};\n\n// Staff API\nexport const staffAPI = {\n  getAll: () => apiRequest('/staff-admin'),\n  \n  add: (staffData) => \n    apiRequest('/staff-admin', {\n      method: 'POST',\n      body: JSON.stringify(staffData),\n    }),\n    \n  update: (id, staffData) => \n    apiRequest(`/staff-admin/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(staffData),\n    }),\n    \n  remove: (id) => \n    apiRequest(`/staff-admin/${id}`, {\n      method: 'DELETE',\n    }),\n};\n\n// Customers API\nexport const customersAPI = {\n  getAll: () => apiRequest('/customers'),\n  \n  getById: (id) => apiRequest(`/customers/${id}`),\n  \n  update: (id, customerData) => \n    apiRequest(`/customers/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(customerData),\n    }),\n    \n  delete: (id) => \n    apiRequest(`/customers/${id}`, {\n      method: 'DELETE',\n    }),\n};\n\n// Dashboard/Analytics API (these might need to be implemented on the backend)\nexport const analyticsAPI = {\n  getDashboardStats: () => \n    apiRequest('/analytics/dashboard-stats').catch(() => {\n      // Fallback to mock data if endpoint doesn't exist\n      return {\n        totalRevenue: 12426,\n        ordersToday: 147,\n        menuItems: 42,\n        inventoryItems: 156,\n        lowStockItems: 3,\n        pendingOrders: 12,\n        revenueChange: 12.5,\n        ordersChange: 8.2,\n      };\n    }),\n    \n  getRevenueStats: (period = '30d') => \n    apiRequest(`/analytics/revenue?period=${period}`).catch(() => {\n      // Mock data fallback\n      return {\n        total: 12426,\n        change: 12.5,\n        chartData: [],\n      };\n    }),\n    \n  getOrderStats: (period = '30d') => \n    apiRequest(`/analytics/orders?period=${period}`).catch(() => {\n      // Mock data fallback\n      return {\n        total: 147,\n        change: 8.2,\n        chartData: [],\n      };\n    }),\n    \n  getInventoryAlerts: () => \n    apiRequest('/analytics/inventory-alerts').catch(() => {\n      // Mock data fallback\n      return [\n        {\n          id: 1,\n          item: 'Coffee beans',\n          currentStock: 5,\n          minStock: 10,\n          unit: 'lbs',\n          severity: 'high',\n        },\n        {\n          id: 2,\n          item: 'Milk',\n          currentStock: 2,\n          minStock: 5,\n          unit: 'gallons',\n          severity: 'medium',\n        },\n      ];\n    }),\n};\n\n// Health check\nexport const healthAPI = {\n  check: () => apiRequest('/health', { auth: false }),\n};\n\nexport default {\n  auth: authAPI,\n  menu: menuAPI,\n  orders: ordersAPI,\n  staff: staffAPI,\n  customers: customersAPI,\n  analytics: analyticsAPI,\n  health: healthAPI,\n};\n"], "mappings": "AAAA;AACA,MAAMA,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;;AAEjF;AACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACzB,OAAOC,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC,IAAIC,YAAY,CAACD,OAAO,CAAC,OAAO,CAAC;AACzE,CAAC;;AAED;AACA,MAAME,aAAa,GAAGA,CAACC,WAAW,GAAG,IAAI,EAAEC,UAAU,GAAG,KAAK,KAAK;EAChE,MAAMC,OAAO,GAAG,CAAC,CAAC;;EAElB;EACA,IAAI,CAACD,UAAU,EAAE;IACfC,OAAO,CAAC,cAAc,CAAC,GAAG,kBAAkB;EAC9C;EAEA,IAAIF,WAAW,EAAE;IACf,MAAMG,KAAK,GAAGR,YAAY,CAAC,CAAC;IAC5B,IAAIQ,KAAK,EAAE;MACTD,OAAO,CAACE,aAAa,GAAG,UAAUD,KAAK,EAAE;IAC3C;EACF;EAEA,OAAOD,OAAO;AAChB,CAAC;;AAED;AACA,MAAMG,UAAU,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;EACnD,MAAMC,GAAG,GAAG,GAAGjB,YAAY,GAAGe,QAAQ,EAAE;EACxC,MAAMG,MAAM,GAAG;IACbP,OAAO,EAAEH,aAAa,CAACQ,OAAO,CAACG,IAAI,KAAK,KAAK,EAAEH,OAAO,CAACN,UAAU,CAAC;IAClE,GAAGM;EACL,CAAC;EAED,IAAI;IACF,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAACJ,GAAG,EAAEC,MAAM,CAAC;IAEzC,IAAI,CAACE,QAAQ,CAACE,EAAE,EAAE;MAChB,MAAMC,SAAS,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;;MAEzD;MACA,QAAQL,QAAQ,CAACM,MAAM;QACrB,KAAK,GAAG;UACN,MAAM,IAAIC,KAAK,CAACJ,SAAS,CAACK,OAAO,IAAI,sBAAsB,CAAC;QAC9D,KAAK,GAAG;UACN,MAAM,IAAID,KAAK,CAACJ,SAAS,CAACK,OAAO,IAAI,yBAAyB,CAAC;QACjE,KAAK,GAAG;UACN,MAAM,IAAID,KAAK,CAACJ,SAAS,CAACK,OAAO,IAAI,eAAe,CAAC;QACvD,KAAK,GAAG;UACN,MAAM,IAAID,KAAK,CAACJ,SAAS,CAACK,OAAO,IAAI,oBAAoB,CAAC;QAC5D,KAAK,GAAG;UACN,MAAM,IAAID,KAAK,CAACJ,SAAS,CAACK,OAAO,IAAI,yBAAyB,CAAC;QACjE,KAAK,GAAG;UACN,MAAM,IAAID,KAAK,CAACJ,SAAS,CAACK,OAAO,IAAI,4CAA4C,CAAC;QACpF,KAAK,GAAG;UACN,MAAM,IAAID,KAAK,CAACJ,SAAS,CAACK,OAAO,IAAI,uCAAuC,CAAC;QAC/E;UACE,MAAM,IAAID,KAAK,CAACJ,SAAS,CAACK,OAAO,IAAI,uBAAuBR,QAAQ,CAACM,MAAM,EAAE,CAAC;MAClF;IACF;IAEA,OAAO,MAAMN,QAAQ,CAACI,IAAI,CAAC,CAAC;EAC9B,CAAC,CAAC,OAAOK,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,0BAA0Bd,QAAQ,GAAG,EAAEc,KAAK,CAAC;IAC3D,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAME,OAAO,GAAG;EACrB;EACAC,KAAK,EAAGC,WAAW,IACjBnB,UAAU,CAAC,aAAa,EAAE;IACxBoB,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACJ,WAAW,CAAC;IACjCd,IAAI,EAAE;EACR,CAAC,CAAC;EAEJ;EACAmB,gBAAgB,EAAGC,QAAQ,IACzBzB,UAAU,CAAC,yBAAyB,EAAE;IACpCoB,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEI,QAAQ;IAAE;IAChBpB,IAAI,EAAE,KAAK;IACXT,UAAU,EAAE;EACd,CAAC,CAAC;EAEJ;EACA8B,aAAa,EAAGD,QAAQ,IACtBzB,UAAU,CAAC,sBAAsB,EAAE;IACjCoB,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEI,QAAQ;IAAE;IAChBpB,IAAI,EAAE,KAAK;IACXT,UAAU,EAAE;EACd,CAAC,CAAC;EAEJ;EACA+B,aAAa,EAAGF,QAAQ,IACtBzB,UAAU,CAAC,eAAe,EAAE;IAC1BoB,MAAM,EAAE,KAAK;IACbC,IAAI,EAAEI,QAAQ;IACd7B,UAAU,EAAE;EACd,CAAC,CAAC;EAEJ;EACAgC,aAAa,EAAEA,CAAA,KACb5B,UAAU,CAAC,eAAe,EAAE;IAC1BoB,MAAM,EAAE;EACV,CAAC,CAAC;EAEJ;EACAS,MAAM,EAAEA,CAAA,KAAM;IACZtC,cAAc,CAACuC,UAAU,CAAC,OAAO,CAAC;IAClCvC,cAAc,CAACuC,UAAU,CAAC,UAAU,CAAC;IACrCvC,cAAc,CAACuC,UAAU,CAAC,MAAM,CAAC;IACjCrC,YAAY,CAACqC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC;IAClCrC,YAAY,CAACqC,UAAU,CAAC,UAAU,CAAC;IACnCrC,YAAY,CAACqC,UAAU,CAAC,MAAM,CAAC;EACjC,CAAC;EAED;EACAC,cAAc,EAAEA,CAAA,KAAM;IACpB,IAAI;MACF,MAAMC,IAAI,GAAGzC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,IAAIC,YAAY,CAACD,OAAO,CAAC,MAAM,CAAC;MAC3E,OAAOwC,IAAI,GAAGV,IAAI,CAACW,KAAK,CAACD,IAAI,CAAC,GAAG,IAAI;IACvC,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO,IAAI;IACb;EACF,CAAC;EAED;EACAmB,eAAe,EAAEA,CAAA,KAAM;IACrB,OAAO,CAAC,EAAE3C,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC,IAAIC,YAAY,CAACD,OAAO,CAAC,OAAO,CAAC,CAAC;EAC7E,CAAC;EAED;EACA2C,WAAW,EAAEA,CAAA,KAAM;IACjB,OAAO5C,cAAc,CAACC,OAAO,CAAC,UAAU,CAAC,IAAIC,YAAY,CAACD,OAAO,CAAC,UAAU,CAAC;EAC/E;AACF,CAAC;;AAED;AACA,OAAO,MAAM4C,OAAO,GAAG;EACrBC,MAAM,EAAEA,CAAA,KAAMrC,UAAU,CAAC,OAAO,EAAE;IAAEK,IAAI,EAAE;EAAM,CAAC,CAAC;EAElDiC,OAAO,EAAGC,EAAE,IAAKvC,UAAU,CAAC,SAASuC,EAAE,EAAE,EAAE;IAAElC,IAAI,EAAE;EAAM,CAAC,CAAC;EAE3DmC,MAAM,EAAGC,QAAQ,IACfzC,UAAU,CAAC,OAAO,EAAE;IAClBoB,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACkB,QAAQ;EAC/B,CAAC,CAAC;EAEJC,MAAM,EAAEA,CAACH,EAAE,EAAEE,QAAQ,KACnBzC,UAAU,CAAC,SAASuC,EAAE,EAAE,EAAE;IACxBnB,MAAM,EAAE,KAAK;IACbC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACkB,QAAQ;EAC/B,CAAC,CAAC;EAEJE,MAAM,EAAGJ,EAAE,IACTvC,UAAU,CAAC,SAASuC,EAAE,EAAE,EAAE;IACxBnB,MAAM,EAAE;EACV,CAAC;AACL,CAAC;;AAED;AACA,OAAO,MAAMwB,SAAS,GAAG;EACvBP,MAAM,EAAEA,CAAA,KAAMrC,UAAU,CAAC,SAAS,CAAC;EAEnCsC,OAAO,EAAGC,EAAE,IAAKvC,UAAU,CAAC,WAAWuC,EAAE,EAAE,CAAC;EAE5CC,MAAM,EAAGK,SAAS,IAChB7C,UAAU,CAAC,SAAS,EAAE;IACpBoB,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACsB,SAAS;EAChC,CAAC,CAAC;EAEJH,MAAM,EAAEA,CAACH,EAAE,EAAEM,SAAS,KACpB7C,UAAU,CAAC,WAAWuC,EAAE,EAAE,EAAE;IAC1BnB,MAAM,EAAE,KAAK;IACbC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACsB,SAAS;EAChC,CAAC,CAAC;EAEJC,YAAY,EAAEA,CAACP,EAAE,EAAE3B,MAAM,KACvBZ,UAAU,CAAC,WAAWuC,EAAE,SAAS,EAAE;IACjCnB,MAAM,EAAE,OAAO;IACfC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;MAAEX;IAAO,CAAC;EACjC,CAAC,CAAC;EAEJ+B,MAAM,EAAGJ,EAAE,IACTvC,UAAU,CAAC,WAAWuC,EAAE,EAAE,EAAE;IAC1BnB,MAAM,EAAE;EACV,CAAC,CAAC;EAEJ2B,aAAa,EAAGC,UAAU,IACxBhD,UAAU,CAAC,oBAAoBgD,UAAU,EAAE,CAAC;EAE9CC,SAAS,EAAEA,CAACC,KAAK,GAAG,EAAE,KACpBlD,UAAU,CAAC,iBAAiBkD,KAAK,kBAAkB;AACvD,CAAC;;AAED;AACA,OAAO,MAAMC,QAAQ,GAAG;EACtBd,MAAM,EAAEA,CAAA,KAAMrC,UAAU,CAAC,cAAc,CAAC;EAExCoD,GAAG,EAAGC,SAAS,IACbrD,UAAU,CAAC,cAAc,EAAE;IACzBoB,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC8B,SAAS;EAChC,CAAC,CAAC;EAEJX,MAAM,EAAEA,CAACH,EAAE,EAAEc,SAAS,KACpBrD,UAAU,CAAC,gBAAgBuC,EAAE,EAAE,EAAE;IAC/BnB,MAAM,EAAE,KAAK;IACbC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC8B,SAAS;EAChC,CAAC,CAAC;EAEJC,MAAM,EAAGf,EAAE,IACTvC,UAAU,CAAC,gBAAgBuC,EAAE,EAAE,EAAE;IAC/BnB,MAAM,EAAE;EACV,CAAC;AACL,CAAC;;AAED;AACA,OAAO,MAAMmC,YAAY,GAAG;EAC1BlB,MAAM,EAAEA,CAAA,KAAMrC,UAAU,CAAC,YAAY,CAAC;EAEtCsC,OAAO,EAAGC,EAAE,IAAKvC,UAAU,CAAC,cAAcuC,EAAE,EAAE,CAAC;EAE/CG,MAAM,EAAEA,CAACH,EAAE,EAAEiB,YAAY,KACvBxD,UAAU,CAAC,cAAcuC,EAAE,EAAE,EAAE;IAC7BnB,MAAM,EAAE,KAAK;IACbC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACiC,YAAY;EACnC,CAAC,CAAC;EAEJb,MAAM,EAAGJ,EAAE,IACTvC,UAAU,CAAC,cAAcuC,EAAE,EAAE,EAAE;IAC7BnB,MAAM,EAAE;EACV,CAAC;AACL,CAAC;;AAED;AACA,OAAO,MAAMqC,YAAY,GAAG;EAC1BC,iBAAiB,EAAEA,CAAA,KACjB1D,UAAU,CAAC,4BAA4B,CAAC,CAACW,KAAK,CAAC,MAAM;IACnD;IACA,OAAO;MACLgD,YAAY,EAAE,KAAK;MACnBC,WAAW,EAAE,GAAG;MAChBC,SAAS,EAAE,EAAE;MACbC,cAAc,EAAE,GAAG;MACnBC,aAAa,EAAE,CAAC;MAChBC,aAAa,EAAE,EAAE;MACjBC,aAAa,EAAE,IAAI;MACnBC,YAAY,EAAE;IAChB,CAAC;EACH,CAAC,CAAC;EAEJC,eAAe,EAAEA,CAACC,MAAM,GAAG,KAAK,KAC9BpE,UAAU,CAAC,6BAA6BoE,MAAM,EAAE,CAAC,CAACzD,KAAK,CAAC,MAAM;IAC5D;IACA,OAAO;MACL0D,KAAK,EAAE,KAAK;MACZC,MAAM,EAAE,IAAI;MACZC,SAAS,EAAE;IACb,CAAC;EACH,CAAC,CAAC;EAEJC,aAAa,EAAEA,CAACJ,MAAM,GAAG,KAAK,KAC5BpE,UAAU,CAAC,4BAA4BoE,MAAM,EAAE,CAAC,CAACzD,KAAK,CAAC,MAAM;IAC3D;IACA,OAAO;MACL0D,KAAK,EAAE,GAAG;MACVC,MAAM,EAAE,GAAG;MACXC,SAAS,EAAE;IACb,CAAC;EACH,CAAC,CAAC;EAEJE,kBAAkB,EAAEA,CAAA,KAClBzE,UAAU,CAAC,6BAA6B,CAAC,CAACW,KAAK,CAAC,MAAM;IACpD;IACA,OAAO,CACL;MACE4B,EAAE,EAAE,CAAC;MACLmC,IAAI,EAAE,cAAc;MACpBC,YAAY,EAAE,CAAC;MACfC,QAAQ,EAAE,EAAE;MACZC,IAAI,EAAE,KAAK;MACXC,QAAQ,EAAE;IACZ,CAAC,EACD;MACEvC,EAAE,EAAE,CAAC;MACLmC,IAAI,EAAE,MAAM;MACZC,YAAY,EAAE,CAAC;MACfC,QAAQ,EAAE,CAAC;MACXC,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE;IACZ,CAAC,CACF;EACH,CAAC;AACL,CAAC;;AAED;AACA,OAAO,MAAMC,SAAS,GAAG;EACvBC,KAAK,EAAEA,CAAA,KAAMhF,UAAU,CAAC,SAAS,EAAE;IAAEK,IAAI,EAAE;EAAM,CAAC;AACpD,CAAC;AAED,eAAe;EACbA,IAAI,EAAEY,OAAO;EACbgE,IAAI,EAAE7C,OAAO;EACb8C,MAAM,EAAEtC,SAAS;EACjBuC,KAAK,EAAEhC,QAAQ;EACfiC,SAAS,EAAE7B,YAAY;EACvB8B,SAAS,EAAE5B,YAAY;EACvB6B,MAAM,EAAEP;AACV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}