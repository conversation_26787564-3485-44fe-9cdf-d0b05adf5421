{"ast": null, "code": "var _jsxFileName = \"D:\\\\Github_Repos\\\\cafe-mangement-system-react\\\\cafe-management-sys\\\\src\\\\contexts\\\\AuthContext.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  useEffect(() => {\n    // Check for stored authentication data on app load\n    const checkAuthStatus = () => {\n      try {\n        const token = localStorage.getItem('authToken');\n        const userData = localStorage.getItem('userData');\n        if (token && userData) {\n          const parsedUser = JSON.parse(userData);\n          setUser(parsedUser);\n          setIsAuthenticated(true);\n        }\n      } catch (error) {\n        console.error('Error checking auth status:', error);\n        // Clear invalid data\n        localStorage.removeItem('authToken');\n        localStorage.removeItem('userData');\n      } finally {\n        setLoading(false);\n      }\n    };\n    checkAuthStatus();\n  }, []);\n  const login = (userData, token) => {\n    try {\n      localStorage.setItem('authToken', token);\n      localStorage.setItem('userData', JSON.stringify(userData));\n      setUser(userData);\n      setIsAuthenticated(true);\n      return true;\n    } catch (error) {\n      console.error('Error during login:', error);\n      return false;\n    }\n  };\n  const logout = () => {\n    try {\n      localStorage.removeItem('authToken');\n      localStorage.removeItem('userData');\n      setUser(null);\n      setIsAuthenticated(false);\n      return true;\n    } catch (error) {\n      console.error('Error during logout:', error);\n      return false;\n    }\n  };\n  const updateUser = updatedUserData => {\n    try {\n      const newUserData = {\n        ...user,\n        ...updatedUserData\n      };\n      localStorage.setItem('userData', JSON.stringify(newUserData));\n      setUser(newUserData);\n      return true;\n    } catch (error) {\n      console.error('Error updating user:', error);\n      return false;\n    }\n  };\n  const getAuthToken = () => {\n    return localStorage.getItem('authToken');\n  };\n  const isAdmin = () => {\n    return user && (user.role === 'admin' || user.role === 'manager');\n  };\n  const isStaff = () => {\n    return user && (user.role === 'admin' || user.role === 'manager' || user.role === 'waiter');\n  };\n  const isCustomer = () => {\n    return user && user.role === 'customer';\n  };\n  const value = {\n    user,\n    loading,\n    isAuthenticated,\n    login,\n    logout,\n    updateUser,\n    getAuthToken,\n    isAdmin,\n    isStaff,\n    isCustomer\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 111,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"xBgiRagNfQVCfEr2dT2PptfN+TE=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "isAuthenticated", "setIsAuthenticated", "checkAuthStatus", "token", "localStorage", "getItem", "userData", "parsedUser", "JSON", "parse", "error", "console", "removeItem", "login", "setItem", "stringify", "logout", "updateUser", "updatedUserData", "newUserData", "getAuthToken", "isAdmin", "role", "isStaff", "isCustomer", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Github_Repos/cafe-mangement-system-react/cafe-management-sys/src/contexts/AuthContext.jsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n\n  useEffect(() => {\n    // Check for stored authentication data on app load\n    const checkAuthStatus = () => {\n      try {\n        const token = localStorage.getItem('authToken');\n        const userData = localStorage.getItem('userData');\n        \n        if (token && userData) {\n          const parsedUser = JSON.parse(userData);\n          setUser(parsedUser);\n          setIsAuthenticated(true);\n        }\n      } catch (error) {\n        console.error('Error checking auth status:', error);\n        // Clear invalid data\n        localStorage.removeItem('authToken');\n        localStorage.removeItem('userData');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    checkAuthStatus();\n  }, []);\n\n  const login = (userData, token) => {\n    try {\n      localStorage.setItem('authToken', token);\n      localStorage.setItem('userData', JSON.stringify(userData));\n      setUser(userData);\n      setIsAuthenticated(true);\n      return true;\n    } catch (error) {\n      console.error('Error during login:', error);\n      return false;\n    }\n  };\n\n  const logout = () => {\n    try {\n      localStorage.removeItem('authToken');\n      localStorage.removeItem('userData');\n      setUser(null);\n      setIsAuthenticated(false);\n      return true;\n    } catch (error) {\n      console.error('Error during logout:', error);\n      return false;\n    }\n  };\n\n  const updateUser = (updatedUserData) => {\n    try {\n      const newUserData = { ...user, ...updatedUserData };\n      localStorage.setItem('userData', JSON.stringify(newUserData));\n      setUser(newUserData);\n      return true;\n    } catch (error) {\n      console.error('Error updating user:', error);\n      return false;\n    }\n  };\n\n  const getAuthToken = () => {\n    return localStorage.getItem('authToken');\n  };\n\n  const isAdmin = () => {\n    return user && (user.role === 'admin' || user.role === 'manager');\n  };\n\n  const isStaff = () => {\n    return user && (user.role === 'admin' || user.role === 'manager' || user.role === 'waiter');\n  };\n\n  const isCustomer = () => {\n    return user && user.role === 'customer';\n  };\n\n  const value = {\n    user,\n    loading,\n    isAuthenticated,\n    login,\n    logout,\n    updateUser,\n    getAuthToken,\n    isAdmin,\n    isStaff,\n    isCustomer,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,WAAW,gBAAGN,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMO,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGR,UAAU,CAACK,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,eAAe,EAAEC,kBAAkB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAE7DC,SAAS,CAAC,MAAM;IACd;IACA,MAAMiB,eAAe,GAAGA,CAAA,KAAM;MAC5B,IAAI;QACF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;QAC/C,MAAMC,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;QAEjD,IAAIF,KAAK,IAAIG,QAAQ,EAAE;UACrB,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,CAAC;UACvCT,OAAO,CAACU,UAAU,CAAC;UACnBN,kBAAkB,CAAC,IAAI,CAAC;QAC1B;MACF,CAAC,CAAC,OAAOS,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD;QACAN,YAAY,CAACQ,UAAU,CAAC,WAAW,CAAC;QACpCR,YAAY,CAACQ,UAAU,CAAC,UAAU,CAAC;MACrC,CAAC,SAAS;QACRb,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,KAAK,GAAGA,CAACP,QAAQ,EAAEH,KAAK,KAAK;IACjC,IAAI;MACFC,YAAY,CAACU,OAAO,CAAC,WAAW,EAAEX,KAAK,CAAC;MACxCC,YAAY,CAACU,OAAO,CAAC,UAAU,EAAEN,IAAI,CAACO,SAAS,CAACT,QAAQ,CAAC,CAAC;MAC1DT,OAAO,CAACS,QAAQ,CAAC;MACjBL,kBAAkB,CAAC,IAAI,CAAC;MACxB,OAAO,IAAI;IACb,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,OAAO,KAAK;IACd;EACF,CAAC;EAED,MAAMM,MAAM,GAAGA,CAAA,KAAM;IACnB,IAAI;MACFZ,YAAY,CAACQ,UAAU,CAAC,WAAW,CAAC;MACpCR,YAAY,CAACQ,UAAU,CAAC,UAAU,CAAC;MACnCf,OAAO,CAAC,IAAI,CAAC;MACbI,kBAAkB,CAAC,KAAK,CAAC;MACzB,OAAO,IAAI;IACb,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,OAAO,KAAK;IACd;EACF,CAAC;EAED,MAAMO,UAAU,GAAIC,eAAe,IAAK;IACtC,IAAI;MACF,MAAMC,WAAW,GAAG;QAAE,GAAGvB,IAAI;QAAE,GAAGsB;MAAgB,CAAC;MACnDd,YAAY,CAACU,OAAO,CAAC,UAAU,EAAEN,IAAI,CAACO,SAAS,CAACI,WAAW,CAAC,CAAC;MAC7DtB,OAAO,CAACsB,WAAW,CAAC;MACpB,OAAO,IAAI;IACb,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,OAAO,KAAK;IACd;EACF,CAAC;EAED,MAAMU,YAAY,GAAGA,CAAA,KAAM;IACzB,OAAOhB,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;EAC1C,CAAC;EAED,MAAMgB,OAAO,GAAGA,CAAA,KAAM;IACpB,OAAOzB,IAAI,KAAKA,IAAI,CAAC0B,IAAI,KAAK,OAAO,IAAI1B,IAAI,CAAC0B,IAAI,KAAK,SAAS,CAAC;EACnE,CAAC;EAED,MAAMC,OAAO,GAAGA,CAAA,KAAM;IACpB,OAAO3B,IAAI,KAAKA,IAAI,CAAC0B,IAAI,KAAK,OAAO,IAAI1B,IAAI,CAAC0B,IAAI,KAAK,SAAS,IAAI1B,IAAI,CAAC0B,IAAI,KAAK,QAAQ,CAAC;EAC7F,CAAC;EAED,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvB,OAAO5B,IAAI,IAAIA,IAAI,CAAC0B,IAAI,KAAK,UAAU;EACzC,CAAC;EAED,MAAMG,KAAK,GAAG;IACZ7B,IAAI;IACJE,OAAO;IACPE,eAAe;IACfa,KAAK;IACLG,MAAM;IACNC,UAAU;IACVG,YAAY;IACZC,OAAO;IACPE,OAAO;IACPC;EACF,CAAC;EAED,oBACErC,OAAA,CAACC,WAAW,CAACsC,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA/B,QAAA,EAChCA;EAAQ;IAAAiC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACnC,GAAA,CAtGWF,YAAY;AAAAsC,EAAA,GAAZtC,YAAY;AAAA,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}