{"name": "server", "version": "1.0.0", "main": "index.js", "scripts": {"start": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^5.1.1", "body-parser": "^1.20.2", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.19.2", "express-rate-limit": "^7.5.1", "firebase-admin": "^12.3.0", "google-auth-library": "^9.11.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.4.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.14", "sharp": "^0.33.4", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "validator": "^13.15.15", "yamljs": "^0.3.0"}}