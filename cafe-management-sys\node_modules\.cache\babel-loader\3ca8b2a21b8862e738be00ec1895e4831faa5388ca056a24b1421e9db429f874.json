{"ast": null, "code": "var _jsxFileName = \"D:\\\\Github_Repos\\\\cafe-mangement-system-react\\\\cafe-management-sys\\\\src\\\\pages\\\\customer\\\\LoginRegisterPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Container, Typography, Button, TextField, Box, Select, MenuItem, FormControl, InputLabel, Tabs, Tab, Paper, InputAdornment, IconButton, useTheme, alpha, Avatar, CircularProgress, Chip } from '@mui/material';\nimport Swal from 'sweetalert2';\nimport { Email, Lock, Person, Phone, Visibility, VisibilityOff, Coffee, Close, ArrowForward, PhotoCamera } from '@mui/icons-material';\nimport Grid2 from '@mui/material/Unstable_Grid2';\nimport { authAPI } from '../../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoginRegisterPage = () => {\n  _s();\n  const theme = useTheme();\n  const [isLogin, setIsLogin] = useState(true);\n  const [role, setRole] = useState('customer');\n  const [showPassword, setShowPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [loginData, setLoginData] = useState({\n    email: '',\n    password: ''\n  });\n  const [registerData, setRegisterData] = useState({\n    firstName: '',\n    lastName: '',\n    phone: '',\n    email: '',\n    password: '',\n    profilePhoto: null\n  });\n  const [fieldErrors, setFieldErrors] = useState({});\n  const [profilePhotoPreview, setProfilePhotoPreview] = useState('');\n  const showSuccessPopup = message => {\n    Swal.fire({\n      icon: 'success',\n      title: 'Success!',\n      text: message,\n      confirmButtonColor: roleInfo.color,\n      timer: 3000,\n      showConfirmButton: false,\n      toast: true,\n      position: 'top-end',\n      showClass: {\n        popup: 'animate__animated animate__slideInRight'\n      },\n      hideClass: {\n        popup: 'animate__animated animate__slideOutRight'\n      }\n    });\n  };\n  const showErrorPopup = message => {\n    Swal.fire({\n      icon: 'error',\n      title: 'Oops...',\n      text: message,\n      confirmButtonColor: roleInfo.color,\n      confirmButtonText: 'Try Again',\n      showClass: {\n        popup: 'animate__animated animate__shakeX'\n      }\n    });\n  };\n  const showValidationErrorPopup = errors => {\n    const errorList = Object.values(errors).join('\\n• ');\n    Swal.fire({\n      icon: 'warning',\n      title: 'Please check your input',\n      html: `<div style=\"text-align: left;\">• ${Object.values(errors).join('<br>• ')}</div>`,\n      confirmButtonColor: roleInfo.color,\n      confirmButtonText: 'Got it'\n    });\n  };\n  const handleTabChange = (event, newValue) => {\n    setIsLogin(newValue === 'login');\n    setFieldErrors({});\n  };\n  const handleRoleChange = event => {\n    setRole(event.target.value);\n    setFieldErrors({});\n  };\n  const handleLoginChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setLoginData(prevData => ({\n      ...prevData,\n      [name]: value\n    }));\n\n    // Clear field-specific error\n    if (fieldErrors[name]) {\n      setFieldErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  const handleRegisterChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setRegisterData(prevData => ({\n      ...prevData,\n      [name]: value\n    }));\n\n    // Clear field-specific error\n    if (fieldErrors[name]) {\n      setFieldErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  const handleProfilePhotoChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      // Validate file type\n      const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];\n      if (!validTypes.includes(file.type)) {\n        showErrorPopup('Please select a valid image file (JPEG, PNG, GIF, or WebP)');\n        return;\n      }\n\n      // Validate file size (max 5MB)\n      if (file.size > 5 * 1024 * 1024) {\n        showErrorPopup('Profile photo must be less than 5MB');\n        return;\n      }\n      setRegisterData(prev => ({\n        ...prev,\n        profilePhoto: file\n      }));\n\n      // Create preview\n      const reader = new FileReader();\n      reader.onload = e => {\n        setProfilePhotoPreview(e.target.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const removeProfilePhoto = () => {\n    setRegisterData(prev => ({\n      ...prev,\n      profilePhoto: null\n    }));\n    setProfilePhotoPreview('');\n  };\n  const handleClickShowPassword = () => {\n    setShowPassword(!showPassword);\n  };\n  const validateLoginForm = () => {\n    const errors = {};\n    if (!loginData.email) {\n      errors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(loginData.email)) {\n      errors.email = 'Please enter a valid email address';\n    }\n    if (!loginData.password) {\n      errors.password = 'Password is required';\n    }\n    setFieldErrors(errors);\n\n    // Show popup for validation errors\n    if (Object.keys(errors).length > 0) {\n      showValidationErrorPopup(errors);\n    }\n    return Object.keys(errors).length === 0;\n  };\n  const validateRegisterForm = () => {\n    const errors = {};\n\n    // First name validation (2-50 characters)\n    if (!registerData.firstName.trim()) {\n      errors.firstName = 'First name is required';\n    } else if (registerData.firstName.trim().length < 2 || registerData.firstName.trim().length > 50) {\n      errors.firstName = 'First name must be between 2 and 50 characters';\n    }\n\n    // Last name validation (2-50 characters)\n    if (!registerData.lastName.trim()) {\n      errors.lastName = 'Last name is required';\n    } else if (registerData.lastName.trim().length < 2 || registerData.lastName.trim().length > 50) {\n      errors.lastName = 'Last name must be between 2 and 50 characters';\n    }\n\n    // Email validation\n    if (!registerData.email) {\n      errors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(registerData.email)) {\n      errors.email = 'Please enter a valid email address';\n    } else if (registerData.email.length > 254) {\n      errors.email = 'Email address is too long';\n    }\n\n    // Enhanced password validation to match server requirements\n    if (!registerData.password) {\n      errors.password = 'Password is required';\n    } else {\n      const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$/;\n      if (!passwordRegex.test(registerData.password)) {\n        errors.password = 'Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character';\n      }\n    }\n\n    // Phone validation (enhanced)\n    if (registerData.phone && registerData.phone.trim()) {\n      const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\n      const cleanPhone = registerData.phone.replace(/[\\s\\-\\(\\)]/g, '');\n      if (!phoneRegex.test(cleanPhone)) {\n        errors.phone = 'Please enter a valid phone number (e.g., +1234567890)';\n      }\n    }\n    setFieldErrors(errors);\n\n    // Show popup for validation errors\n    if (Object.keys(errors).length > 0) {\n      showValidationErrorPopup(errors);\n    }\n    return Object.keys(errors).length === 0;\n  };\n  const handleLoginSubmit = async () => {\n    if (!validateLoginForm()) return;\n    setLoading(true);\n    try {\n      // Use the API service for consistent error handling\n      const responseData = await authAPI.login(loginData);\n\n      // Extract data from the standardized response format\n      const {\n        data\n      } = responseData;\n      if (!data || !data.token || !data.user) {\n        throw new Error('Invalid response format from server');\n      }\n\n      // Store user data in sessionStorage\n      sessionStorage.setItem('token', data.token);\n      sessionStorage.setItem('userType', data.userType);\n      sessionStorage.setItem('user', JSON.stringify(data.user));\n      showSuccessPopup(responseData.message || 'Login successful! Redirecting to your dashboard...');\n      setLoginData({\n        email: '',\n        password: ''\n      });\n\n      // Redirect based on user type after a short delay\n      setTimeout(() => {\n        if (data.userType === 'customer') {\n          window.location.href = '/customer-dashboard';\n        } else {\n          // Staff or Admin (userType is 'staffOrAdmin')\n          const userRole = data.user.role;\n          if (userRole === 'admin') {\n            window.location.href = '/admin-dashboard';\n          } else {\n            window.location.href = '/staff-dashboard';\n          }\n        }\n      }, 1500);\n    } catch (error) {\n      console.error('Login error:', error);\n      showErrorPopup(error.message || 'An error occurred during login');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleRegisterSubmit = async () => {\n    if (!validateRegisterForm()) return;\n    setLoading(true);\n    try {\n      const formData = new FormData();\n      formData.append('firstName', registerData.firstName.trim());\n      formData.append('lastName', registerData.lastName.trim());\n      formData.append('email', registerData.email.toLowerCase().trim());\n      formData.append('password', registerData.password);\n\n      // Only append phone if it's provided and not empty\n      if (registerData.phone && registerData.phone.trim()) {\n        formData.append('phone', registerData.phone.trim());\n      }\n      if (registerData.profilePhoto) {\n        formData.append('profilePhoto', registerData.profilePhoto);\n      }\n\n      // Use the appropriate API service method\n      let responseData;\n      if (role === 'customer') {\n        responseData = await authAPI.registerCustomer(formData);\n      } else {\n        formData.append('role', role);\n        responseData = await authAPI.registerStaff(formData);\n      }\n      showSuccessPopup(responseData.message || 'Registration successful! You can now login with your new account.');\n      setRegisterData({\n        firstName: '',\n        lastName: '',\n        phone: '',\n        email: '',\n        password: '',\n        profilePhoto: null\n      });\n      setProfilePhotoPreview('');\n\n      // Switch to login tab after successful registration\n      setTimeout(() => {\n        setIsLogin(true);\n      }, 2000);\n    } catch (error) {\n      console.error('Registration error:', error);\n      showErrorPopup(error.message || 'An error occurred during registration');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Get role-specific colors and labels\n  const getRoleInfo = () => {\n    switch (role) {\n      case 'admin':\n        return {\n          color: theme.palette.error.main,\n          label: 'Administrator',\n          description: 'Full system access and management'\n        };\n      case 'staff':\n        return {\n          color: theme.palette.info.main,\n          label: 'Staff Member',\n          description: 'Order management and customer service'\n        };\n      default:\n        return {\n          color: theme.palette.primary.main,\n          label: 'Customer',\n          description: 'Browse menu and place orders'\n        };\n    }\n  };\n  const roleInfo = getRoleInfo();\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      minHeight: '100vh',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      background: `linear-gradient(135deg, \n                    ${alpha(theme.palette.primary.light, 0.15)} 0%, \n                    ${alpha(theme.palette.primary.main, 0.05)} 50%,\n                    ${theme.palette.background.default} 100%)`,\n      padding: {\n        xs: 2,\n        sm: 4\n      }\n    },\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"sm\",\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        elevation: 4,\n        sx: {\n          overflow: 'hidden',\n          borderRadius: 3,\n          boxShadow: '0 10px 40px rgba(0, 0, 0, 0.1)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            p: 3,\n            background: `linear-gradient(to right, ${roleInfo.color}, ${alpha(roleInfo.color, 0.8)})`,\n            color: 'white',\n            textAlign: 'center',\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            sx: {\n              bgcolor: 'white',\n              width: 56,\n              height: 56,\n              margin: '0 auto 16px',\n              border: '2px solid white',\n              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'\n            },\n            children: /*#__PURE__*/_jsxDEV(Coffee, {\n              fontSize: \"large\",\n              sx: {\n                color: roleInfo.color\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            component: \"h1\",\n            fontWeight: \"bold\",\n            children: \"Caf\\xE9 Management System\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              opacity: 0.9,\n              mt: 1\n            },\n            children: isLogin ? 'Sign in to your account' : 'Create a new account'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n            label: roleInfo.label,\n            size: \"small\",\n            sx: {\n              mt: 2,\n              bgcolor: 'rgba(255, 255, 255, 0.2)',\n              color: 'white',\n              fontWeight: 'bold'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n          value: isLogin ? 'login' : 'register',\n          onChange: handleTabChange,\n          variant: \"fullWidth\",\n          sx: {\n            '& .MuiTab-root': {\n              fontWeight: 600,\n              py: 2\n            },\n            '& .Mui-selected': {\n              color: roleInfo.color\n            },\n            '& .MuiTabs-indicator': {\n              backgroundColor: roleInfo.color,\n              height: 3\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Login\",\n            value: \"login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Register\",\n            value: \"register\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            p: 4,\n            background: `linear-gradient(135deg, ${alpha(roleInfo.color, 0.05)} 0%, ${alpha(roleInfo.color, 0.02)} 100%)`\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 3\n            },\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              variant: \"outlined\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                id: \"role-select-label\",\n                children: \"I am a\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                labelId: \"role-select-label\",\n                value: role,\n                label: \"I am a\",\n                onChange: handleRoleChange,\n                disabled: loading,\n                sx: {\n                  '& .MuiOutlinedInput-notchedOutline': {\n                    borderColor: alpha(roleInfo.color, 0.5)\n                  },\n                  '&:hover .MuiOutlinedInput-notchedOutline': {\n                    borderColor: roleInfo.color\n                  },\n                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\n                    borderColor: roleInfo.color\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"customer\",\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      children: \"Customer\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 498,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: \"Browse menu and place orders\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 499,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 497,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 496,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"staff\",\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      children: \"Staff Member\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 506,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: \"Order management and customer service\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 507,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 505,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 504,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"admin\",\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      children: \"Administrator\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 514,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: \"Full system access and management\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 515,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 513,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 512,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 478,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 13\n          }, this), isLogin ?\n          /*#__PURE__*/\n          // Login Form\n          _jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Email Address\",\n              name: \"email\",\n              type: \"email\",\n              margin: \"normal\",\n              variant: \"outlined\",\n              value: loginData.email,\n              onChange: handleLoginChange,\n              disabled: loading,\n              error: !!fieldErrors.email,\n              helperText: fieldErrors.email,\n              InputProps: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(Email, {\n                    color: \"action\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 542,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 23\n                }, this)\n              },\n              sx: {\n                '& .MuiOutlinedInput-root': {\n                  '&.Mui-focused fieldset': {\n                    borderColor: roleInfo.color\n                  }\n                },\n                '& .MuiInputLabel-root.Mui-focused': {\n                  color: roleInfo.color\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Password\",\n              name: \"password\",\n              type: showPassword ? 'text' : 'password',\n              margin: \"normal\",\n              variant: \"outlined\",\n              value: loginData.password,\n              onChange: handleLoginChange,\n              disabled: loading,\n              error: !!fieldErrors.password,\n              helperText: fieldErrors.password,\n              InputProps: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(Lock, {\n                    color: \"action\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 573,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 572,\n                  columnNumber: 23\n                }, this),\n                endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"end\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: handleClickShowPassword,\n                    edge: \"end\",\n                    disabled: loading,\n                    children: showPassword ? /*#__PURE__*/_jsxDEV(VisibilityOff, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 583,\n                      columnNumber: 43\n                    }, this) : /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 583,\n                      columnNumber: 63\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 578,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 577,\n                  columnNumber: 23\n                }, this)\n              },\n              sx: {\n                '& .MuiOutlinedInput-root': {\n                  '&.Mui-focused fieldset': {\n                    borderColor: roleInfo.color\n                  }\n                },\n                '& .MuiInputLabel-root.Mui-focused': {\n                  color: roleInfo.color\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mt: 1,\n                mb: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  cursor: 'pointer',\n                  color: roleInfo.color,\n                  '&:hover': {\n                    textDecoration: 'underline'\n                  }\n                },\n                children: \"Forgot password?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 601,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 600,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              size: \"large\",\n              fullWidth: true,\n              onClick: handleLoginSubmit,\n              disabled: loading,\n              sx: {\n                py: 1.5,\n                backgroundColor: roleInfo.color,\n                '&:hover': {\n                  backgroundColor: alpha(roleInfo.color, 0.9)\n                },\n                '&:disabled': {\n                  backgroundColor: alpha(roleInfo.color, 0.5)\n                },\n                boxShadow: `0 4px 12px ${alpha(roleInfo.color, 0.3)}`\n              },\n              endIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 20,\n                color: \"inherit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 630,\n                columnNumber: 38\n              }, this) : /*#__PURE__*/_jsxDEV(ArrowForward, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 630,\n                columnNumber: 87\n              }, this),\n              children: loading ? 'Signing In...' : 'Sign In'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 15\n          }, this) :\n          /*#__PURE__*/\n          // Register Form\n          _jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 3,\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                accept: \"image/*\",\n                style: {\n                  display: 'none'\n                },\n                id: \"profile-photo-upload\",\n                type: \"file\",\n                onChange: handleProfilePhotoChange,\n                disabled: loading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 640,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"profile-photo-upload\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    position: 'relative',\n                    display: 'inline-block',\n                    cursor: loading ? 'not-allowed' : 'pointer'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    src: profilePhotoPreview,\n                    sx: {\n                      width: 80,\n                      height: 80,\n                      border: `2px dashed ${alpha(roleInfo.color, 0.5)}`,\n                      backgroundColor: alpha(roleInfo.color, 0.1),\n                      '&:hover': {\n                        backgroundColor: alpha(roleInfo.color, 0.2)\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(PhotoCamera, {\n                      sx: {\n                        color: roleInfo.color\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 668,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 656,\n                    columnNumber: 23\n                  }, this), profilePhotoPreview && /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: e => {\n                      e.preventDefault();\n                      removeProfilePhoto();\n                    },\n                    sx: {\n                      position: 'absolute',\n                      top: -5,\n                      right: -5,\n                      backgroundColor: 'error.main',\n                      color: 'white',\n                      '&:hover': {\n                        backgroundColor: 'error.dark'\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Close, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 688,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 671,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 649,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                display: \"block\",\n                sx: {\n                  mt: 1,\n                  color: 'text.secondary'\n                },\n                children: profilePhotoPreview ? 'Click to change photo' : 'Click to add profile photo (optional)'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 693,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 639,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid2, {\n              container: true,\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Grid2, {\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"First Name\",\n                  name: \"firstName\",\n                  margin: \"normal\",\n                  variant: \"outlined\",\n                  value: registerData.firstName,\n                  onChange: handleRegisterChange,\n                  disabled: loading,\n                  error: !!fieldErrors.firstName,\n                  helperText: fieldErrors.firstName,\n                  InputProps: {\n                    startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                      position: \"start\",\n                      children: /*#__PURE__*/_jsxDEV(Person, {\n                        color: \"action\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 714,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 713,\n                      columnNumber: 27\n                    }, this)\n                  },\n                  sx: {\n                    '& .MuiOutlinedInput-root': {\n                      '&.Mui-focused fieldset': {\n                        borderColor: roleInfo.color\n                      }\n                    },\n                    '& .MuiInputLabel-root.Mui-focused': {\n                      color: roleInfo.color\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 700,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid2, {\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Last Name\",\n                  name: \"lastName\",\n                  margin: \"normal\",\n                  variant: \"outlined\",\n                  value: registerData.lastName,\n                  onChange: handleRegisterChange,\n                  disabled: loading,\n                  error: !!fieldErrors.lastName,\n                  helperText: fieldErrors.lastName,\n                  sx: {\n                    '& .MuiOutlinedInput-root': {\n                      '&.Mui-focused fieldset': {\n                        borderColor: roleInfo.color\n                      }\n                    },\n                    '& .MuiInputLabel-root.Mui-focused': {\n                      color: roleInfo.color\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 731,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 730,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 698,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Phone Number (Optional)\",\n              name: \"phone\",\n              margin: \"normal\",\n              variant: \"outlined\",\n              value: registerData.phone,\n              onChange: handleRegisterChange,\n              disabled: loading,\n              error: !!fieldErrors.phone,\n              helperText: fieldErrors.phone || 'e.g., +1234567890 or (*************',\n              InputProps: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(Phone, {\n                    color: \"action\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 770,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 769,\n                  columnNumber: 23\n                }, this)\n              },\n              sx: {\n                '& .MuiOutlinedInput-root': {\n                  '&.Mui-focused fieldset': {\n                    borderColor: roleInfo.color\n                  }\n                },\n                '& .MuiInputLabel-root.Mui-focused': {\n                  color: roleInfo.color\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 756,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Email Address\",\n              name: \"email\",\n              type: \"email\",\n              margin: \"normal\",\n              variant: \"outlined\",\n              value: registerData.email,\n              onChange: handleRegisterChange,\n              disabled: loading,\n              error: !!fieldErrors.email,\n              helperText: fieldErrors.email,\n              InputProps: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(Email, {\n                    color: \"action\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 801,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 800,\n                  columnNumber: 23\n                }, this)\n              },\n              sx: {\n                '& .MuiOutlinedInput-root': {\n                  '&.Mui-focused fieldset': {\n                    borderColor: roleInfo.color\n                  }\n                },\n                '& .MuiInputLabel-root.Mui-focused': {\n                  color: roleInfo.color\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 786,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Password\",\n              name: \"password\",\n              type: showPassword ? 'text' : 'password',\n              margin: \"normal\",\n              variant: \"outlined\",\n              value: registerData.password,\n              onChange: handleRegisterChange,\n              disabled: loading,\n              error: !!fieldErrors.password,\n              helperText: fieldErrors.password || 'Minimum 8 characters with uppercase, lowercase, number, and special character',\n              InputProps: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(Lock, {\n                    color: \"action\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 832,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 831,\n                  columnNumber: 23\n                }, this),\n                endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"end\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: handleClickShowPassword,\n                    edge: \"end\",\n                    disabled: loading,\n                    children: showPassword ? /*#__PURE__*/_jsxDEV(VisibilityOff, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 842,\n                      columnNumber: 43\n                    }, this) : /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 842,\n                      columnNumber: 63\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 837,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 836,\n                  columnNumber: 23\n                }, this)\n              },\n              sx: {\n                '& .MuiOutlinedInput-root': {\n                  '&.Mui-focused fieldset': {\n                    borderColor: roleInfo.color\n                  }\n                },\n                '& .MuiInputLabel-root.Mui-focused': {\n                  color: roleInfo.color\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 817,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              size: \"large\",\n              fullWidth: true,\n              sx: {\n                mt: 3,\n                py: 1.5,\n                backgroundColor: roleInfo.color,\n                '&:hover': {\n                  backgroundColor: alpha(roleInfo.color, 0.9)\n                },\n                '&:disabled': {\n                  backgroundColor: alpha(roleInfo.color, 0.5)\n                },\n                boxShadow: `0 4px 12px ${alpha(roleInfo.color, 0.3)}`\n              },\n              onClick: handleRegisterSubmit,\n              disabled: loading,\n              endIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 20,\n                color: \"inherit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 877,\n                columnNumber: 38\n              }, this) : /*#__PURE__*/_jsxDEV(ArrowForward, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 877,\n                columnNumber: 87\n              }, this),\n              children: loading ? 'Creating Account...' : 'Create Account'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 859,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 637,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            p: 3,\n            borderTop: `1px solid ${theme.palette.divider}`,\n            backgroundColor: alpha(theme.palette.background.paper, 0.5),\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: [isLogin ? \"Don't have an account?\" : \"Already have an account?\", /*#__PURE__*/_jsxDEV(Button, {\n              sx: {\n                ml: 1,\n                color: roleInfo.color,\n                fontWeight: 'bold'\n              },\n              onClick: () => setIsLogin(!isLogin),\n              disabled: loading,\n              children: isLogin ? \"Sign Up\" : \"Sign In\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 896,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 894,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 886,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 392,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 379,\n    columnNumber: 5\n  }, this);\n};\n_s(LoginRegisterPage, \"FbfPGN5EUmIoLuQdg+rXHNz8Y0Y=\", false, function () {\n  return [useTheme];\n});\n_c = LoginRegisterPage;\nexport default LoginRegisterPage;\nvar _c;\n$RefreshReg$(_c, \"LoginRegisterPage\");", "map": {"version": 3, "names": ["React", "useState", "Container", "Typography", "<PERSON><PERSON>", "TextField", "Box", "Select", "MenuItem", "FormControl", "InputLabel", "Tabs", "Tab", "Paper", "InputAdornment", "IconButton", "useTheme", "alpha", "Avatar", "CircularProgress", "Chip", "<PERSON><PERSON>", "Email", "Lock", "Person", "Phone", "Visibility", "VisibilityOff", "Coffee", "Close", "ArrowForward", "PhotoCamera", "Grid2", "authAPI", "jsxDEV", "_jsxDEV", "LoginRegisterPage", "_s", "theme", "is<PERSON>ogin", "setIsLogin", "role", "setRole", "showPassword", "setShowPassword", "loading", "setLoading", "loginData", "setLoginData", "email", "password", "registerData", "setRegisterData", "firstName", "lastName", "phone", "profilePhoto", "fieldErrors", "setFieldErrors", "profilePhotoPreview", "setProfilePhotoPreview", "showSuccessPopup", "message", "fire", "icon", "title", "text", "confirmButtonColor", "roleInfo", "color", "timer", "showConfirmButton", "toast", "position", "showClass", "popup", "hideClass", "showErrorPopup", "confirmButtonText", "showValidationErrorPopup", "errors", "errorList", "Object", "values", "join", "html", "handleTabChange", "event", "newValue", "handleRoleChange", "target", "value", "handleLoginChange", "e", "name", "prevData", "prev", "handleRegisterChange", "handleProfilePhotoChange", "file", "files", "validTypes", "includes", "type", "size", "reader", "FileReader", "onload", "result", "readAsDataURL", "removeProfilePhoto", "handleClickShowPassword", "validateLoginForm", "test", "keys", "length", "validateRegisterForm", "trim", "passwordRegex", "phoneRegex", "cleanPhone", "replace", "handleLoginSubmit", "responseData", "login", "data", "token", "user", "Error", "sessionStorage", "setItem", "userType", "JSON", "stringify", "setTimeout", "window", "location", "href", "userRole", "error", "console", "handleRegisterSubmit", "formData", "FormData", "append", "toLowerCase", "registerCustomer", "registerStaff", "getRoleInfo", "palette", "main", "label", "description", "info", "primary", "sx", "minHeight", "display", "alignItems", "justifyContent", "background", "light", "default", "padding", "xs", "sm", "children", "max<PERSON><PERSON><PERSON>", "elevation", "overflow", "borderRadius", "boxShadow", "p", "textAlign", "bgcolor", "width", "height", "margin", "border", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "component", "fontWeight", "opacity", "mt", "onChange", "py", "backgroundColor", "mb", "fullWidth", "id", "labelId", "disabled", "borderColor", "helperText", "InputProps", "startAdornment", "endAdornment", "onClick", "edge", "cursor", "textDecoration", "endIcon", "accept", "style", "htmlFor", "src", "preventDefault", "top", "right", "container", "spacing", "borderTop", "divider", "paper", "ml", "_c", "$RefreshReg$"], "sources": ["D:/Github_Repos/cafe-mangement-system-react/cafe-management-sys/src/pages/customer/LoginRegisterPage.jsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport {\r\n  Con<PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>,\r\n  Button,\r\n  TextField,\r\n  Box,\r\n  Select,\r\n  MenuItem,\r\n  FormControl,\r\n  InputLabel,\r\n  Tabs,\r\n  Tab,\r\n  Paper,\r\n  InputAdornment,\r\n  IconButton,\r\n  useTheme,\r\n  alpha,\r\n  Avatar,\r\n  CircularProgress,\r\n  Chip\r\n} from '@mui/material';\r\nimport Swal from 'sweetalert2';\r\nimport {\r\n  Email,\r\n  Lock,\r\n  Person,\r\n  Phone,\r\n  Visibility,\r\n  VisibilityOff,\r\n  Coffee,\r\n  Close,\r\n  ArrowForward,\r\n  PhotoCamera,\r\n} from '@mui/icons-material';\r\n\r\nimport Grid2 from '@mui/material/Unstable_Grid2';\r\nimport { authAPI } from '../../services/api';\r\n\r\nconst LoginRegisterPage = () => {\r\n  const theme = useTheme();\r\n  const [isLogin, setIsLogin] = useState(true);\r\n  const [role, setRole] = useState('customer');\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  const [loginData, setLoginData] = useState({\r\n    email: '',\r\n    password: ''\r\n  });\r\n\r\n  const [registerData, setRegisterData] = useState({\r\n    firstName: '',\r\n    lastName: '',\r\n    phone: '',\r\n    email: '',\r\n    password: '',\r\n    profilePhoto: null\r\n  });\r\n\r\n  const [fieldErrors, setFieldErrors] = useState({});\r\n  const [profilePhotoPreview, setProfilePhotoPreview] = useState('');\r\n\r\n  const showSuccessPopup = (message) => {\r\n    Swal.fire({\r\n      icon: 'success',\r\n      title: 'Success!',\r\n      text: message,\r\n      confirmButtonColor: roleInfo.color,\r\n      timer: 3000,\r\n      showConfirmButton: false,\r\n      toast: true,\r\n      position: 'top-end',\r\n      showClass: {\r\n        popup: 'animate__animated animate__slideInRight'\r\n      },\r\n      hideClass: {\r\n        popup: 'animate__animated animate__slideOutRight'\r\n      }\r\n    });\r\n  };\r\n\r\n  const showErrorPopup = (message) => {\r\n    Swal.fire({\r\n      icon: 'error',\r\n      title: 'Oops...',\r\n      text: message,\r\n      confirmButtonColor: roleInfo.color,\r\n      confirmButtonText: 'Try Again',\r\n      showClass: {\r\n        popup: 'animate__animated animate__shakeX'\r\n      }\r\n    });\r\n  };\r\n\r\n  const showValidationErrorPopup = (errors) => {\r\n    const errorList = Object.values(errors).join('\\n• ');\r\n    Swal.fire({\r\n      icon: 'warning',\r\n      title: 'Please check your input',\r\n      html: `<div style=\"text-align: left;\">• ${Object.values(errors).join('<br>• ')}</div>`,\r\n      confirmButtonColor: roleInfo.color,\r\n      confirmButtonText: 'Got it'\r\n    });\r\n  };\r\n  const handleTabChange = (event, newValue) => {\r\n    setIsLogin(newValue === 'login');\r\n    setFieldErrors({});\r\n  };\r\n\r\n  const handleRoleChange = (event) => {\r\n    setRole(event.target.value);\r\n    setFieldErrors({});\r\n  };\r\n\r\n  const handleLoginChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setLoginData((prevData) => ({ ...prevData, [name]: value }));\r\n\r\n    // Clear field-specific error\r\n    if (fieldErrors[name]) {\r\n      setFieldErrors(prev => ({ ...prev, [name]: '' }));\r\n    }\r\n  };\r\n\r\n  const handleRegisterChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setRegisterData((prevData) => ({ ...prevData, [name]: value }));\r\n\r\n    // Clear field-specific error\r\n    if (fieldErrors[name]) {\r\n      setFieldErrors(prev => ({ ...prev, [name]: '' }));\r\n    }\r\n  };\r\n\r\n  const handleProfilePhotoChange = (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      // Validate file type\r\n      const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];\r\n      if (!validTypes.includes(file.type)) {\r\n        showErrorPopup('Please select a valid image file (JPEG, PNG, GIF, or WebP)');\r\n        return;\r\n      }\r\n\r\n      // Validate file size (max 5MB)\r\n      if (file.size > 5 * 1024 * 1024) {\r\n        showErrorPopup('Profile photo must be less than 5MB');\r\n        return;\r\n      }\r\n\r\n      setRegisterData(prev => ({ ...prev, profilePhoto: file }));\r\n\r\n      // Create preview\r\n      const reader = new FileReader();\r\n      reader.onload = (e) => {\r\n        setProfilePhotoPreview(e.target.result);\r\n      };\r\n      reader.readAsDataURL(file);\r\n\r\n    }\r\n  };\r\n\r\n  const removeProfilePhoto = () => {\r\n    setRegisterData(prev => ({ ...prev, profilePhoto: null }));\r\n    setProfilePhotoPreview('');\r\n  };\r\n\r\n  const handleClickShowPassword = () => {\r\n    setShowPassword(!showPassword);\r\n  };\r\n\r\n  const validateLoginForm = () => {\r\n    const errors = {};\r\n\r\n    if (!loginData.email) {\r\n      errors.email = 'Email is required';\r\n    } else if (!/\\S+@\\S+\\.\\S+/.test(loginData.email)) {\r\n      errors.email = 'Please enter a valid email address';\r\n    }\r\n\r\n    if (!loginData.password) {\r\n      errors.password = 'Password is required';\r\n    }\r\n\r\n    setFieldErrors(errors);\r\n\r\n    // Show popup for validation errors\r\n    if (Object.keys(errors).length > 0) {\r\n      showValidationErrorPopup(errors);\r\n    }\r\n\r\n    return Object.keys(errors).length === 0;\r\n  };\r\n\r\n  const validateRegisterForm = () => {\r\n    const errors = {};\r\n\r\n    // First name validation (2-50 characters)\r\n    if (!registerData.firstName.trim()) {\r\n      errors.firstName = 'First name is required';\r\n    } else if (registerData.firstName.trim().length < 2 || registerData.firstName.trim().length > 50) {\r\n      errors.firstName = 'First name must be between 2 and 50 characters';\r\n    }\r\n\r\n    // Last name validation (2-50 characters)\r\n    if (!registerData.lastName.trim()) {\r\n      errors.lastName = 'Last name is required';\r\n    } else if (registerData.lastName.trim().length < 2 || registerData.lastName.trim().length > 50) {\r\n      errors.lastName = 'Last name must be between 2 and 50 characters';\r\n    }\r\n\r\n    // Email validation\r\n    if (!registerData.email) {\r\n      errors.email = 'Email is required';\r\n    } else if (!/\\S+@\\S+\\.\\S+/.test(registerData.email)) {\r\n      errors.email = 'Please enter a valid email address';\r\n    } else if (registerData.email.length > 254) {\r\n      errors.email = 'Email address is too long';\r\n    }\r\n\r\n    // Enhanced password validation to match server requirements\r\n    if (!registerData.password) {\r\n      errors.password = 'Password is required';\r\n    } else {\r\n      const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$/;\r\n      if (!passwordRegex.test(registerData.password)) {\r\n        errors.password = 'Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character';\r\n      }\r\n    }\r\n\r\n    // Phone validation (enhanced)\r\n    if (registerData.phone && registerData.phone.trim()) {\r\n      const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\r\n      const cleanPhone = registerData.phone.replace(/[\\s\\-\\(\\)]/g, '');\r\n      if (!phoneRegex.test(cleanPhone)) {\r\n        errors.phone = 'Please enter a valid phone number (e.g., +1234567890)';\r\n      }\r\n    }\r\n\r\n    setFieldErrors(errors);\r\n\r\n    // Show popup for validation errors\r\n    if (Object.keys(errors).length > 0) {\r\n      showValidationErrorPopup(errors);\r\n    }\r\n\r\n    return Object.keys(errors).length === 0;\r\n  };\r\n\r\n  const handleLoginSubmit = async () => {\r\n    if (!validateLoginForm()) return;\r\n\r\n    setLoading(true);\r\n\r\n    try {\r\n      // Use the API service for consistent error handling\r\n      const responseData = await authAPI.login(loginData);\r\n\r\n      // Extract data from the standardized response format\r\n      const { data } = responseData;\r\n\r\n      if (!data || !data.token || !data.user) {\r\n        throw new Error('Invalid response format from server');\r\n      }\r\n\r\n      // Store user data in sessionStorage\r\n      sessionStorage.setItem('token', data.token);\r\n      sessionStorage.setItem('userType', data.userType);\r\n      sessionStorage.setItem('user', JSON.stringify(data.user));\r\n\r\n      showSuccessPopup(responseData.message || 'Login successful! Redirecting to your dashboard...');\r\n      setLoginData({ email: '', password: '' });\r\n\r\n      // Redirect based on user type after a short delay\r\n      setTimeout(() => {\r\n        if (data.userType === 'customer') {\r\n          window.location.href = '/customer-dashboard';\r\n        } else {\r\n          // Staff or Admin (userType is 'staffOrAdmin')\r\n          const userRole = data.user.role;\r\n          if (userRole === 'admin') {\r\n            window.location.href = '/admin-dashboard';\r\n          } else {\r\n            window.location.href = '/staff-dashboard';\r\n          }\r\n        }\r\n      }, 1500);\r\n\r\n    } catch (error) {\r\n      console.error('Login error:', error);\r\n      showErrorPopup(error.message || 'An error occurred during login');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleRegisterSubmit = async () => {\r\n    if (!validateRegisterForm()) return;\r\n\r\n    setLoading(true);\r\n\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append('firstName', registerData.firstName.trim());\r\n      formData.append('lastName', registerData.lastName.trim());\r\n      formData.append('email', registerData.email.toLowerCase().trim());\r\n      formData.append('password', registerData.password);\r\n\r\n      // Only append phone if it's provided and not empty\r\n      if (registerData.phone && registerData.phone.trim()) {\r\n        formData.append('phone', registerData.phone.trim());\r\n      }\r\n\r\n      if (registerData.profilePhoto) {\r\n        formData.append('profilePhoto', registerData.profilePhoto);\r\n      }\r\n\r\n      // Use the appropriate API service method\r\n      let responseData;\r\n      if (role === 'customer') {\r\n        responseData = await authAPI.registerCustomer(formData);\r\n      } else {\r\n        formData.append('role', role);\r\n        responseData = await authAPI.registerStaff(formData);\r\n      }\r\n\r\n      showSuccessPopup(responseData.message || 'Registration successful! You can now login with your new account.');\r\n      setRegisterData({\r\n        firstName: '',\r\n        lastName: '',\r\n        phone: '',\r\n        email: '',\r\n        password: '',\r\n        profilePhoto: null\r\n      });\r\n      setProfilePhotoPreview('');\r\n\r\n      // Switch to login tab after successful registration\r\n      setTimeout(() => {\r\n        setIsLogin(true);\r\n      }, 2000);\r\n\r\n    } catch (error) {\r\n      console.error('Registration error:', error);\r\n      showErrorPopup(error.message || 'An error occurred during registration');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Get role-specific colors and labels\r\n  const getRoleInfo = () => {\r\n    switch (role) {\r\n      case 'admin':\r\n        return {\r\n          color: theme.palette.error.main,\r\n          label: 'Administrator',\r\n          description: 'Full system access and management'\r\n        };\r\n      case 'staff':\r\n        return {\r\n          color: theme.palette.info.main,\r\n          label: 'Staff Member',\r\n          description: 'Order management and customer service'\r\n        };\r\n      default:\r\n        return {\r\n          color: theme.palette.primary.main,\r\n          label: 'Customer',\r\n          description: 'Browse menu and place orders'\r\n        };\r\n    }\r\n  };\r\n\r\n  const roleInfo = getRoleInfo();\r\n\r\n  return (\r\n    <Box\r\n      sx={{\r\n        minHeight: '100vh',\r\n        display: 'flex',\r\n        alignItems: 'center',\r\n        justifyContent: 'center',\r\n        background: `linear-gradient(135deg, \r\n                    ${alpha(theme.palette.primary.light, 0.15)} 0%, \r\n                    ${alpha(theme.palette.primary.main, 0.05)} 50%,\r\n                    ${theme.palette.background.default} 100%)`,\r\n        padding: { xs: 2, sm: 4 }\r\n      }}\r\n    >\r\n      <Container maxWidth=\"sm\">\r\n        <Paper\r\n          elevation={4}\r\n          sx={{\r\n            overflow: 'hidden',\r\n            borderRadius: 3,\r\n            boxShadow: '0 10px 40px rgba(0, 0, 0, 0.1)'\r\n          }}\r\n        >\r\n          {/* Header Section */}\r\n          <Box\r\n            sx={{\r\n              p: 3,\r\n              background: `linear-gradient(to right, ${roleInfo.color}, ${alpha(roleInfo.color, 0.8)})`,\r\n              color: 'white',\r\n              textAlign: 'center',\r\n              position: 'relative'\r\n            }}\r\n          >\r\n            <Avatar\r\n              sx={{\r\n                bgcolor: 'white',\r\n                width: 56,\r\n                height: 56,\r\n                margin: '0 auto 16px',\r\n                border: '2px solid white',\r\n                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'\r\n              }}\r\n            >\r\n              <Coffee fontSize=\"large\" sx={{ color: roleInfo.color }} />\r\n            </Avatar>\r\n            <Typography variant=\"h5\" component=\"h1\" fontWeight=\"bold\">\r\n              Café Management System\r\n            </Typography>\r\n            <Typography variant=\"body2\" sx={{ opacity: 0.9, mt: 1 }}>\r\n              {isLogin ? 'Sign in to your account' : 'Create a new account'}\r\n            </Typography>\r\n\r\n            {/* Role Chip */}\r\n            <Chip\r\n              label={roleInfo.label}\r\n              size=\"small\"\r\n              sx={{\r\n                mt: 2,\r\n                bgcolor: 'rgba(255, 255, 255, 0.2)',\r\n                color: 'white',\r\n                fontWeight: 'bold'\r\n              }}\r\n            />\r\n          </Box>\r\n\r\n          {/* Tabs */}\r\n          <Tabs\r\n            value={isLogin ? 'login' : 'register'}\r\n            onChange={handleTabChange}\r\n            variant=\"fullWidth\"\r\n            sx={{\r\n              '& .MuiTab-root': {\r\n                fontWeight: 600,\r\n                py: 2\r\n              },\r\n              '& .Mui-selected': {\r\n                color: roleInfo.color,\r\n              },\r\n              '& .MuiTabs-indicator': {\r\n                backgroundColor: roleInfo.color,\r\n                height: 3\r\n              }\r\n            }}\r\n          >\r\n            <Tab label=\"Login\" value=\"login\" />\r\n            <Tab label=\"Register\" value=\"register\" />\r\n          </Tabs>\r\n\r\n\r\n          {/* Form Content */}\r\n          <Box\r\n            sx={{\r\n              p: 4,\r\n              background: `linear-gradient(135deg, ${alpha(roleInfo.color, 0.05)} 0%, ${alpha(roleInfo.color, 0.02)} 100%)`\r\n            }}\r\n          >\r\n            {/* Role Selection */}\r\n            <Box sx={{ mb: 3 }}>\r\n              <FormControl fullWidth variant=\"outlined\">\r\n                <InputLabel id=\"role-select-label\">I am a</InputLabel>\r\n                <Select\r\n                  labelId=\"role-select-label\"\r\n                  value={role}\r\n                  label=\"I am a\"\r\n                  onChange={handleRoleChange}\r\n                  disabled={loading}\r\n                  sx={{\r\n                    '& .MuiOutlinedInput-notchedOutline': {\r\n                      borderColor: alpha(roleInfo.color, 0.5),\r\n                    },\r\n                    '&:hover .MuiOutlinedInput-notchedOutline': {\r\n                      borderColor: roleInfo.color,\r\n                    },\r\n                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\r\n                      borderColor: roleInfo.color,\r\n                    }\r\n                  }}\r\n                >\r\n                  <MenuItem value=\"customer\">\r\n                    <Box>\r\n                      <Typography variant=\"body1\">Customer</Typography>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">\r\n                        Browse menu and place orders\r\n                      </Typography>\r\n                    </Box>\r\n                  </MenuItem>\r\n                  <MenuItem value=\"staff\">\r\n                    <Box>\r\n                      <Typography variant=\"body1\">Staff Member</Typography>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">\r\n                        Order management and customer service\r\n                      </Typography>\r\n                    </Box>\r\n                  </MenuItem>\r\n                  <MenuItem value=\"admin\">\r\n                    <Box>\r\n                      <Typography variant=\"body1\">Administrator</Typography>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">\r\n                        Full system access and management\r\n                      </Typography>\r\n                    </Box>\r\n                  </MenuItem>\r\n                </Select>\r\n              </FormControl>\r\n            </Box>\r\n\r\n            {isLogin ? (\r\n              // Login Form\r\n              <Box>\r\n                <TextField\r\n                  fullWidth\r\n                  label=\"Email Address\"\r\n                  name=\"email\"\r\n                  type=\"email\"\r\n                  margin=\"normal\"\r\n                  variant=\"outlined\"\r\n                  value={loginData.email}\r\n                  onChange={handleLoginChange}\r\n                  disabled={loading}\r\n                  error={!!fieldErrors.email}\r\n                  helperText={fieldErrors.email}\r\n                  InputProps={{\r\n                    startAdornment: (\r\n                      <InputAdornment position=\"start\">\r\n                        <Email color=\"action\" />\r\n                      </InputAdornment>\r\n                    ),\r\n                  }}\r\n                  sx={{\r\n                    '& .MuiOutlinedInput-root': {\r\n                      '&.Mui-focused fieldset': {\r\n                        borderColor: roleInfo.color,\r\n                      }\r\n                    },\r\n                    '& .MuiInputLabel-root.Mui-focused': {\r\n                      color: roleInfo.color,\r\n                    }\r\n                  }}\r\n                />\r\n\r\n                <TextField\r\n                  fullWidth\r\n                  label=\"Password\"\r\n                  name=\"password\"\r\n                  type={showPassword ? 'text' : 'password'}\r\n                  margin=\"normal\"\r\n                  variant=\"outlined\"\r\n                  value={loginData.password}\r\n                  onChange={handleLoginChange}\r\n                  disabled={loading}\r\n                  error={!!fieldErrors.password}\r\n                  helperText={fieldErrors.password}\r\n                  InputProps={{\r\n                    startAdornment: (\r\n                      <InputAdornment position=\"start\">\r\n                        <Lock color=\"action\" />\r\n                      </InputAdornment>\r\n                    ),\r\n                    endAdornment: (\r\n                      <InputAdornment position=\"end\">\r\n                        <IconButton\r\n                          onClick={handleClickShowPassword}\r\n                          edge=\"end\"\r\n                          disabled={loading}\r\n                        >\r\n                          {showPassword ? <VisibilityOff /> : <Visibility />}\r\n                        </IconButton>\r\n                      </InputAdornment>\r\n                    )\r\n                  }}\r\n                  sx={{\r\n                    '& .MuiOutlinedInput-root': {\r\n                      '&.Mui-focused fieldset': {\r\n                        borderColor: roleInfo.color,\r\n                      }\r\n                    },\r\n                    '& .MuiInputLabel-root.Mui-focused': {\r\n                      color: roleInfo.color,\r\n                    }\r\n                  }}\r\n                />\r\n\r\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 1, mb: 2 }}>\r\n                  <Typography\r\n                    variant=\"body2\"\r\n                    sx={{\r\n                      cursor: 'pointer',\r\n                      color: roleInfo.color,\r\n                      '&:hover': { textDecoration: 'underline' }\r\n                    }}\r\n                  >\r\n                    Forgot password?\r\n                  </Typography>\r\n                </Box>\r\n\r\n                <Button\r\n                  variant=\"contained\"\r\n                  size=\"large\"\r\n                  fullWidth\r\n                  onClick={handleLoginSubmit}\r\n                  disabled={loading}\r\n                  sx={{\r\n                    py: 1.5,\r\n                    backgroundColor: roleInfo.color,\r\n                    '&:hover': {\r\n                      backgroundColor: alpha(roleInfo.color, 0.9),\r\n                    },\r\n                    '&:disabled': {\r\n                      backgroundColor: alpha(roleInfo.color, 0.5),\r\n                    },\r\n                    boxShadow: `0 4px 12px ${alpha(roleInfo.color, 0.3)}`\r\n                  }}\r\n                  endIcon={loading ? <CircularProgress size={20} color=\"inherit\" /> : <ArrowForward />}\r\n                >\r\n                  {loading ? 'Signing In...' : 'Sign In'}\r\n                </Button>\r\n              </Box>\r\n            ) : (\r\n              // Register Form\r\n              <Box>\r\n                {/* Profile Photo Upload */}\r\n                <Box sx={{ mb: 3, textAlign: 'center' }}>\r\n                  <input\r\n                    accept=\"image/*\"\r\n                    style={{ display: 'none' }}\r\n                    id=\"profile-photo-upload\"\r\n                    type=\"file\"\r\n                    onChange={handleProfilePhotoChange}\r\n                    disabled={loading}\r\n                  />\r\n                  <label htmlFor=\"profile-photo-upload\">\r\n                    <Box\r\n                      sx={{\r\n                        position: 'relative',\r\n                        display: 'inline-block',\r\n                        cursor: loading ? 'not-allowed' : 'pointer'\r\n                      }}\r\n                    >\r\n                      <Avatar\r\n                        src={profilePhotoPreview}\r\n                        sx={{\r\n                          width: 80,\r\n                          height: 80,\r\n                          border: `2px dashed ${alpha(roleInfo.color, 0.5)}`,\r\n                          backgroundColor: alpha(roleInfo.color, 0.1),\r\n                          '&:hover': {\r\n                            backgroundColor: alpha(roleInfo.color, 0.2),\r\n                          }\r\n                        }}\r\n                      >\r\n                        <PhotoCamera sx={{ color: roleInfo.color }} />\r\n                      </Avatar>\r\n                      {profilePhotoPreview && (\r\n                        <IconButton\r\n                          size=\"small\"\r\n                          onClick={(e) => {\r\n                            e.preventDefault();\r\n                            removeProfilePhoto();\r\n                          }}\r\n                          sx={{\r\n                            position: 'absolute',\r\n                            top: -5,\r\n                            right: -5,\r\n                            backgroundColor: 'error.main',\r\n                            color: 'white',\r\n                            '&:hover': {\r\n                              backgroundColor: 'error.dark',\r\n                            }\r\n                          }}\r\n                        >\r\n                          <Close fontSize=\"small\" />\r\n                        </IconButton>\r\n                      )}\r\n                    </Box>\r\n                  </label>\r\n                  <Typography variant=\"caption\" display=\"block\" sx={{ mt: 1, color: 'text.secondary' }}>\r\n                    {profilePhotoPreview ? 'Click to change photo' : 'Click to add profile photo (optional)'}\r\n                  </Typography>\r\n                </Box>\r\n\r\n                <Grid2 container spacing={2}>\r\n                  <Grid2 xs={12} sm={6}>\r\n                    <TextField\r\n                      fullWidth\r\n                      label=\"First Name\"\r\n                      name=\"firstName\"\r\n                      margin=\"normal\"\r\n                      variant=\"outlined\"\r\n                      value={registerData.firstName}\r\n                      onChange={handleRegisterChange}\r\n                      disabled={loading}\r\n                      error={!!fieldErrors.firstName}\r\n                      helperText={fieldErrors.firstName}\r\n                      InputProps={{\r\n                        startAdornment: (\r\n                          <InputAdornment position=\"start\">\r\n                            <Person color=\"action\" />\r\n                          </InputAdornment>\r\n                        ),\r\n                      }}\r\n                      sx={{\r\n                        '& .MuiOutlinedInput-root': {\r\n                          '&.Mui-focused fieldset': {\r\n                            borderColor: roleInfo.color,\r\n                          }\r\n                        },\r\n                        '& .MuiInputLabel-root.Mui-focused': {\r\n                          color: roleInfo.color,\r\n                        }\r\n                      }}\r\n                    />\r\n                  </Grid2>\r\n                  <Grid2 xs={12} sm={6}>\r\n                    <TextField\r\n                      fullWidth\r\n                      label=\"Last Name\"\r\n                      name=\"lastName\"\r\n                      margin=\"normal\"\r\n                      variant=\"outlined\"\r\n                      value={registerData.lastName}\r\n                      onChange={handleRegisterChange}\r\n                      disabled={loading}\r\n                      error={!!fieldErrors.lastName}\r\n                      helperText={fieldErrors.lastName}\r\n                      sx={{\r\n                        '& .MuiOutlinedInput-root': {\r\n                          '&.Mui-focused fieldset': {\r\n                            borderColor: roleInfo.color,\r\n                          }\r\n                        },\r\n                        '& .MuiInputLabel-root.Mui-focused': {\r\n                          color: roleInfo.color,\r\n                        }\r\n                      }}\r\n                    />\r\n                  </Grid2>\r\n                </Grid2>\r\n\r\n                <TextField\r\n                  fullWidth\r\n                  label=\"Phone Number (Optional)\"\r\n                  name=\"phone\"\r\n                  margin=\"normal\"\r\n                  variant=\"outlined\"\r\n                  value={registerData.phone}\r\n                  onChange={handleRegisterChange}\r\n                  disabled={loading}\r\n                  error={!!fieldErrors.phone}\r\n                  helperText={fieldErrors.phone || 'e.g., +1234567890 or (*************'}\r\n                  InputProps={{\r\n                    startAdornment: (\r\n                      <InputAdornment position=\"start\">\r\n                        <Phone color=\"action\" />\r\n                      </InputAdornment>\r\n                    ),\r\n                  }}\r\n                  sx={{\r\n                    '& .MuiOutlinedInput-root': {\r\n                      '&.Mui-focused fieldset': {\r\n                        borderColor: roleInfo.color,\r\n                      }\r\n                    },\r\n                    '& .MuiInputLabel-root.Mui-focused': {\r\n                      color: roleInfo.color,\r\n                    }\r\n                  }}\r\n                />\r\n\r\n                <TextField\r\n                  fullWidth\r\n                  label=\"Email Address\"\r\n                  name=\"email\"\r\n                  type=\"email\"\r\n                  margin=\"normal\"\r\n                  variant=\"outlined\"\r\n                  value={registerData.email}\r\n                  onChange={handleRegisterChange}\r\n                  disabled={loading}\r\n                  error={!!fieldErrors.email}\r\n                  helperText={fieldErrors.email}\r\n                  InputProps={{\r\n                    startAdornment: (\r\n                      <InputAdornment position=\"start\">\r\n                        <Email color=\"action\" />\r\n                      </InputAdornment>\r\n                    ),\r\n                  }}\r\n                  sx={{\r\n                    '& .MuiOutlinedInput-root': {\r\n                      '&.Mui-focused fieldset': {\r\n                        borderColor: roleInfo.color,\r\n                      }\r\n                    },\r\n                    '& .MuiInputLabel-root.Mui-focused': {\r\n                      color: roleInfo.color,\r\n                    }\r\n                  }}\r\n                />\r\n\r\n                <TextField\r\n                  fullWidth\r\n                  label=\"Password\"\r\n                  name=\"password\"\r\n                  type={showPassword ? 'text' : 'password'}\r\n                  margin=\"normal\"\r\n                  variant=\"outlined\"\r\n                  value={registerData.password}\r\n                  onChange={handleRegisterChange}\r\n                  disabled={loading}\r\n                  error={!!fieldErrors.password}\r\n                  helperText={fieldErrors.password || 'Minimum 8 characters with uppercase, lowercase, number, and special character'}\r\n                  InputProps={{\r\n                    startAdornment: (\r\n                      <InputAdornment position=\"start\">\r\n                        <Lock color=\"action\" />\r\n                      </InputAdornment>\r\n                    ),\r\n                    endAdornment: (\r\n                      <InputAdornment position=\"end\">\r\n                        <IconButton\r\n                          onClick={handleClickShowPassword}\r\n                          edge=\"end\"\r\n                          disabled={loading}\r\n                        >\r\n                          {showPassword ? <VisibilityOff /> : <Visibility />}\r\n                        </IconButton>\r\n                      </InputAdornment>\r\n                    )\r\n                  }}\r\n                  sx={{\r\n                    '& .MuiOutlinedInput-root': {\r\n                      '&.Mui-focused fieldset': {\r\n                        borderColor: roleInfo.color,\r\n                      }\r\n                    },\r\n                    '& .MuiInputLabel-root.Mui-focused': {\r\n                      color: roleInfo.color,\r\n                    }\r\n                  }}\r\n                />\r\n\r\n                <Button\r\n                  variant=\"contained\"\r\n                  size=\"large\"\r\n                  fullWidth\r\n                  sx={{\r\n                    mt: 3,\r\n                    py: 1.5,\r\n                    backgroundColor: roleInfo.color,\r\n                    '&:hover': {\r\n                      backgroundColor: alpha(roleInfo.color, 0.9),\r\n                    },\r\n                    '&:disabled': {\r\n                      backgroundColor: alpha(roleInfo.color, 0.5),\r\n                    },\r\n                    boxShadow: `0 4px 12px ${alpha(roleInfo.color, 0.3)}`\r\n                  }}\r\n                  onClick={handleRegisterSubmit}\r\n                  disabled={loading}\r\n                  endIcon={loading ? <CircularProgress size={20} color=\"inherit\" /> : <ArrowForward />}\r\n                >\r\n                  {loading ? 'Creating Account...' : 'Create Account'}\r\n                </Button>\r\n              </Box>\r\n            )}\r\n          </Box>\r\n\r\n          {/* Footer */}\r\n          <Box\r\n            sx={{\r\n              p: 3,\r\n              borderTop: `1px solid ${theme.palette.divider}`,\r\n              backgroundColor: alpha(theme.palette.background.paper, 0.5),\r\n              textAlign: 'center'\r\n            }}\r\n          >\r\n            <Typography variant=\"body2\" color=\"text.secondary\">\r\n              {isLogin ? \"Don't have an account?\" : \"Already have an account?\"}\r\n              <Button\r\n                sx={{\r\n                  ml: 1,\r\n                  color: roleInfo.color,\r\n                  fontWeight: 'bold',\r\n                }}\r\n                onClick={() => setIsLogin(!isLogin)}\r\n                disabled={loading}\r\n              >\r\n                {isLogin ? \"Sign Up\" : \"Sign In\"}\r\n              </Button>\r\n            </Typography>\r\n          </Box>\r\n        </Paper>\r\n      </Container>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default LoginRegisterPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,SAAS,EACTC,UAAU,EACVC,MAAM,EACNC,SAAS,EACTC,GAAG,EACHC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,IAAI,EACJC,GAAG,EACHC,KAAK,EACLC,cAAc,EACdC,UAAU,EACVC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,gBAAgB,EAChBC,IAAI,QACC,eAAe;AACtB,OAAOC,IAAI,MAAM,aAAa;AAC9B,SACEC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,aAAa,EACbC,MAAM,EACNC,KAAK,EACLC,YAAY,EACZC,WAAW,QACN,qBAAqB;AAE5B,OAAOC,KAAK,MAAM,8BAA8B;AAChD,SAASC,OAAO,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,KAAK,GAAGtB,QAAQ,CAAC,CAAC;EACxB,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwC,IAAI,EAAEC,OAAO,CAAC,GAAGzC,QAAQ,CAAC,UAAU,CAAC;EAC5C,MAAM,CAAC0C,YAAY,EAAEC,eAAe,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4C,OAAO,EAAEC,UAAU,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAM,CAAC8C,SAAS,EAAEC,YAAY,CAAC,GAAG/C,QAAQ,CAAC;IACzCgD,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAC;IAC/CoD,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTN,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZM,YAAY,EAAE;EAChB,CAAC,CAAC;EAEF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGzD,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM,CAAC0D,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EAElE,MAAM4D,gBAAgB,GAAIC,OAAO,IAAK;IACpCzC,IAAI,CAAC0C,IAAI,CAAC;MACRC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,UAAU;MACjBC,IAAI,EAAEJ,OAAO;MACbK,kBAAkB,EAAEC,QAAQ,CAACC,KAAK;MAClCC,KAAK,EAAE,IAAI;MACXC,iBAAiB,EAAE,KAAK;MACxBC,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE,SAAS;MACnBC,SAAS,EAAE;QACTC,KAAK,EAAE;MACT,CAAC;MACDC,SAAS,EAAE;QACTD,KAAK,EAAE;MACT;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,cAAc,GAAIf,OAAO,IAAK;IAClCzC,IAAI,CAAC0C,IAAI,CAAC;MACRC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAEJ,OAAO;MACbK,kBAAkB,EAAEC,QAAQ,CAACC,KAAK;MAClCS,iBAAiB,EAAE,WAAW;MAC9BJ,SAAS,EAAE;QACTC,KAAK,EAAE;MACT;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMI,wBAAwB,GAAIC,MAAM,IAAK;IAC3C,MAAMC,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACH,MAAM,CAAC,CAACI,IAAI,CAAC,MAAM,CAAC;IACpD/D,IAAI,CAAC0C,IAAI,CAAC;MACRC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,yBAAyB;MAChCoB,IAAI,EAAE,oCAAoCH,MAAM,CAACC,MAAM,CAACH,MAAM,CAAC,CAACI,IAAI,CAAC,QAAQ,CAAC,QAAQ;MACtFjB,kBAAkB,EAAEC,QAAQ,CAACC,KAAK;MAClCS,iBAAiB,EAAE;IACrB,CAAC,CAAC;EACJ,CAAC;EACD,MAAMQ,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3ChD,UAAU,CAACgD,QAAQ,KAAK,OAAO,CAAC;IAChC9B,cAAc,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC;EAED,MAAM+B,gBAAgB,GAAIF,KAAK,IAAK;IAClC7C,OAAO,CAAC6C,KAAK,CAACG,MAAM,CAACC,KAAK,CAAC;IAC3BjC,cAAc,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC;EAED,MAAMkC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEH;IAAM,CAAC,GAAGE,CAAC,CAACH,MAAM;IAChC1C,YAAY,CAAE+C,QAAQ,KAAM;MAAE,GAAGA,QAAQ;MAAE,CAACD,IAAI,GAAGH;IAAM,CAAC,CAAC,CAAC;;IAE5D;IACA,IAAIlC,WAAW,CAACqC,IAAI,CAAC,EAAE;MACrBpC,cAAc,CAACsC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACF,IAAI,GAAG;MAAG,CAAC,CAAC,CAAC;IACnD;EACF,CAAC;EAED,MAAMG,oBAAoB,GAAIJ,CAAC,IAAK;IAClC,MAAM;MAAEC,IAAI;MAAEH;IAAM,CAAC,GAAGE,CAAC,CAACH,MAAM;IAChCtC,eAAe,CAAE2C,QAAQ,KAAM;MAAE,GAAGA,QAAQ;MAAE,CAACD,IAAI,GAAGH;IAAM,CAAC,CAAC,CAAC;;IAE/D;IACA,IAAIlC,WAAW,CAACqC,IAAI,CAAC,EAAE;MACrBpC,cAAc,CAACsC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACF,IAAI,GAAG;MAAG,CAAC,CAAC,CAAC;IACnD;EACF,CAAC;EAED,MAAMI,wBAAwB,GAAIL,CAAC,IAAK;IACtC,MAAMM,IAAI,GAAGN,CAAC,CAACH,MAAM,CAACU,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACR;MACA,MAAME,UAAU,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;MACzE,IAAI,CAACA,UAAU,CAACC,QAAQ,CAACH,IAAI,CAACI,IAAI,CAAC,EAAE;QACnC1B,cAAc,CAAC,4DAA4D,CAAC;QAC5E;MACF;;MAEA;MACA,IAAIsB,IAAI,CAACK,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;QAC/B3B,cAAc,CAAC,qCAAqC,CAAC;QACrD;MACF;MAEAzB,eAAe,CAAC4C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAExC,YAAY,EAAE2C;MAAK,CAAC,CAAC,CAAC;;MAE1D;MACA,MAAMM,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAId,CAAC,IAAK;QACrBjC,sBAAsB,CAACiC,CAAC,CAACH,MAAM,CAACkB,MAAM,CAAC;MACzC,CAAC;MACDH,MAAM,CAACI,aAAa,CAACV,IAAI,CAAC;IAE5B;EACF,CAAC;EAED,MAAMW,kBAAkB,GAAGA,CAAA,KAAM;IAC/B1D,eAAe,CAAC4C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAExC,YAAY,EAAE;IAAK,CAAC,CAAC,CAAC;IAC1DI,sBAAsB,CAAC,EAAE,CAAC;EAC5B,CAAC;EAED,MAAMmD,uBAAuB,GAAGA,CAAA,KAAM;IACpCnE,eAAe,CAAC,CAACD,YAAY,CAAC;EAChC,CAAC;EAED,MAAMqE,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMhC,MAAM,GAAG,CAAC,CAAC;IAEjB,IAAI,CAACjC,SAAS,CAACE,KAAK,EAAE;MACpB+B,MAAM,CAAC/B,KAAK,GAAG,mBAAmB;IACpC,CAAC,MAAM,IAAI,CAAC,cAAc,CAACgE,IAAI,CAAClE,SAAS,CAACE,KAAK,CAAC,EAAE;MAChD+B,MAAM,CAAC/B,KAAK,GAAG,oCAAoC;IACrD;IAEA,IAAI,CAACF,SAAS,CAACG,QAAQ,EAAE;MACvB8B,MAAM,CAAC9B,QAAQ,GAAG,sBAAsB;IAC1C;IAEAQ,cAAc,CAACsB,MAAM,CAAC;;IAEtB;IACA,IAAIE,MAAM,CAACgC,IAAI,CAAClC,MAAM,CAAC,CAACmC,MAAM,GAAG,CAAC,EAAE;MAClCpC,wBAAwB,CAACC,MAAM,CAAC;IAClC;IAEA,OAAOE,MAAM,CAACgC,IAAI,CAAClC,MAAM,CAAC,CAACmC,MAAM,KAAK,CAAC;EACzC,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMpC,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAAC7B,YAAY,CAACE,SAAS,CAACgE,IAAI,CAAC,CAAC,EAAE;MAClCrC,MAAM,CAAC3B,SAAS,GAAG,wBAAwB;IAC7C,CAAC,MAAM,IAAIF,YAAY,CAACE,SAAS,CAACgE,IAAI,CAAC,CAAC,CAACF,MAAM,GAAG,CAAC,IAAIhE,YAAY,CAACE,SAAS,CAACgE,IAAI,CAAC,CAAC,CAACF,MAAM,GAAG,EAAE,EAAE;MAChGnC,MAAM,CAAC3B,SAAS,GAAG,gDAAgD;IACrE;;IAEA;IACA,IAAI,CAACF,YAAY,CAACG,QAAQ,CAAC+D,IAAI,CAAC,CAAC,EAAE;MACjCrC,MAAM,CAAC1B,QAAQ,GAAG,uBAAuB;IAC3C,CAAC,MAAM,IAAIH,YAAY,CAACG,QAAQ,CAAC+D,IAAI,CAAC,CAAC,CAACF,MAAM,GAAG,CAAC,IAAIhE,YAAY,CAACG,QAAQ,CAAC+D,IAAI,CAAC,CAAC,CAACF,MAAM,GAAG,EAAE,EAAE;MAC9FnC,MAAM,CAAC1B,QAAQ,GAAG,+CAA+C;IACnE;;IAEA;IACA,IAAI,CAACH,YAAY,CAACF,KAAK,EAAE;MACvB+B,MAAM,CAAC/B,KAAK,GAAG,mBAAmB;IACpC,CAAC,MAAM,IAAI,CAAC,cAAc,CAACgE,IAAI,CAAC9D,YAAY,CAACF,KAAK,CAAC,EAAE;MACnD+B,MAAM,CAAC/B,KAAK,GAAG,oCAAoC;IACrD,CAAC,MAAM,IAAIE,YAAY,CAACF,KAAK,CAACkE,MAAM,GAAG,GAAG,EAAE;MAC1CnC,MAAM,CAAC/B,KAAK,GAAG,2BAA2B;IAC5C;;IAEA;IACA,IAAI,CAACE,YAAY,CAACD,QAAQ,EAAE;MAC1B8B,MAAM,CAAC9B,QAAQ,GAAG,sBAAsB;IAC1C,CAAC,MAAM;MACL,MAAMoE,aAAa,GAAG,sEAAsE;MAC5F,IAAI,CAACA,aAAa,CAACL,IAAI,CAAC9D,YAAY,CAACD,QAAQ,CAAC,EAAE;QAC9C8B,MAAM,CAAC9B,QAAQ,GAAG,6GAA6G;MACjI;IACF;;IAEA;IACA,IAAIC,YAAY,CAACI,KAAK,IAAIJ,YAAY,CAACI,KAAK,CAAC8D,IAAI,CAAC,CAAC,EAAE;MACnD,MAAME,UAAU,GAAG,wBAAwB;MAC3C,MAAMC,UAAU,GAAGrE,YAAY,CAACI,KAAK,CAACkE,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;MAChE,IAAI,CAACF,UAAU,CAACN,IAAI,CAACO,UAAU,CAAC,EAAE;QAChCxC,MAAM,CAACzB,KAAK,GAAG,uDAAuD;MACxE;IACF;IAEAG,cAAc,CAACsB,MAAM,CAAC;;IAEtB;IACA,IAAIE,MAAM,CAACgC,IAAI,CAAClC,MAAM,CAAC,CAACmC,MAAM,GAAG,CAAC,EAAE;MAClCpC,wBAAwB,CAACC,MAAM,CAAC;IAClC;IAEA,OAAOE,MAAM,CAACgC,IAAI,CAAClC,MAAM,CAAC,CAACmC,MAAM,KAAK,CAAC;EACzC,CAAC;EAED,MAAMO,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACV,iBAAiB,CAAC,CAAC,EAAE;IAE1BlE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF;MACA,MAAM6E,YAAY,GAAG,MAAM1F,OAAO,CAAC2F,KAAK,CAAC7E,SAAS,CAAC;;MAEnD;MACA,MAAM;QAAE8E;MAAK,CAAC,GAAGF,YAAY;MAE7B,IAAI,CAACE,IAAI,IAAI,CAACA,IAAI,CAACC,KAAK,IAAI,CAACD,IAAI,CAACE,IAAI,EAAE;QACtC,MAAM,IAAIC,KAAK,CAAC,qCAAqC,CAAC;MACxD;;MAEA;MACAC,cAAc,CAACC,OAAO,CAAC,OAAO,EAAEL,IAAI,CAACC,KAAK,CAAC;MAC3CG,cAAc,CAACC,OAAO,CAAC,UAAU,EAAEL,IAAI,CAACM,QAAQ,CAAC;MACjDF,cAAc,CAACC,OAAO,CAAC,MAAM,EAAEE,IAAI,CAACC,SAAS,CAACR,IAAI,CAACE,IAAI,CAAC,CAAC;MAEzDlE,gBAAgB,CAAC8D,YAAY,CAAC7D,OAAO,IAAI,oDAAoD,CAAC;MAC9Fd,YAAY,CAAC;QAAEC,KAAK,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG,CAAC,CAAC;;MAEzC;MACAoF,UAAU,CAAC,MAAM;QACf,IAAIT,IAAI,CAACM,QAAQ,KAAK,UAAU,EAAE;UAChCI,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,qBAAqB;QAC9C,CAAC,MAAM;UACL;UACA,MAAMC,QAAQ,GAAGb,IAAI,CAACE,IAAI,CAACtF,IAAI;UAC/B,IAAIiG,QAAQ,KAAK,OAAO,EAAE;YACxBH,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,kBAAkB;UAC3C,CAAC,MAAM;YACLF,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,kBAAkB;UAC3C;QACF;MACF,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC9D,cAAc,CAAC8D,KAAK,CAAC7E,OAAO,IAAI,gCAAgC,CAAC;IACnE,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+F,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACzB,oBAAoB,CAAC,CAAC,EAAE;IAE7BtE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMgG,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAE7F,YAAY,CAACE,SAAS,CAACgE,IAAI,CAAC,CAAC,CAAC;MAC3DyB,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAE7F,YAAY,CAACG,QAAQ,CAAC+D,IAAI,CAAC,CAAC,CAAC;MACzDyB,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE7F,YAAY,CAACF,KAAK,CAACgG,WAAW,CAAC,CAAC,CAAC5B,IAAI,CAAC,CAAC,CAAC;MACjEyB,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAE7F,YAAY,CAACD,QAAQ,CAAC;;MAElD;MACA,IAAIC,YAAY,CAACI,KAAK,IAAIJ,YAAY,CAACI,KAAK,CAAC8D,IAAI,CAAC,CAAC,EAAE;QACnDyB,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE7F,YAAY,CAACI,KAAK,CAAC8D,IAAI,CAAC,CAAC,CAAC;MACrD;MAEA,IAAIlE,YAAY,CAACK,YAAY,EAAE;QAC7BsF,QAAQ,CAACE,MAAM,CAAC,cAAc,EAAE7F,YAAY,CAACK,YAAY,CAAC;MAC5D;;MAEA;MACA,IAAImE,YAAY;MAChB,IAAIlF,IAAI,KAAK,UAAU,EAAE;QACvBkF,YAAY,GAAG,MAAM1F,OAAO,CAACiH,gBAAgB,CAACJ,QAAQ,CAAC;MACzD,CAAC,MAAM;QACLA,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEvG,IAAI,CAAC;QAC7BkF,YAAY,GAAG,MAAM1F,OAAO,CAACkH,aAAa,CAACL,QAAQ,CAAC;MACtD;MAEAjF,gBAAgB,CAAC8D,YAAY,CAAC7D,OAAO,IAAI,mEAAmE,CAAC;MAC7GV,eAAe,CAAC;QACdC,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,EAAE;QACTN,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZM,YAAY,EAAE;MAChB,CAAC,CAAC;MACFI,sBAAsB,CAAC,EAAE,CAAC;;MAE1B;MACA0E,UAAU,CAAC,MAAM;QACf9F,UAAU,CAAC,IAAI,CAAC;MAClB,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAOmG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C9D,cAAc,CAAC8D,KAAK,CAAC7E,OAAO,IAAI,uCAAuC,CAAC;IAC1E,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMsG,WAAW,GAAGA,CAAA,KAAM;IACxB,QAAQ3G,IAAI;MACV,KAAK,OAAO;QACV,OAAO;UACL4B,KAAK,EAAE/B,KAAK,CAAC+G,OAAO,CAACV,KAAK,CAACW,IAAI;UAC/BC,KAAK,EAAE,eAAe;UACtBC,WAAW,EAAE;QACf,CAAC;MACH,KAAK,OAAO;QACV,OAAO;UACLnF,KAAK,EAAE/B,KAAK,CAAC+G,OAAO,CAACI,IAAI,CAACH,IAAI;UAC9BC,KAAK,EAAE,cAAc;UACrBC,WAAW,EAAE;QACf,CAAC;MACH;QACE,OAAO;UACLnF,KAAK,EAAE/B,KAAK,CAAC+G,OAAO,CAACK,OAAO,CAACJ,IAAI;UACjCC,KAAK,EAAE,UAAU;UACjBC,WAAW,EAAE;QACf,CAAC;IACL;EACF,CAAC;EAED,MAAMpF,QAAQ,GAAGgF,WAAW,CAAC,CAAC;EAE9B,oBACEjH,OAAA,CAAC7B,GAAG;IACFqJ,EAAE,EAAE;MACFC,SAAS,EAAE,OAAO;MAClBC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE;AACpB,sBAAsB/I,KAAK,CAACqB,KAAK,CAAC+G,OAAO,CAACK,OAAO,CAACO,KAAK,EAAE,IAAI,CAAC;AAC9D,sBAAsBhJ,KAAK,CAACqB,KAAK,CAAC+G,OAAO,CAACK,OAAO,CAACJ,IAAI,EAAE,IAAI,CAAC;AAC7D,sBAAsBhH,KAAK,CAAC+G,OAAO,CAACW,UAAU,CAACE,OAAO,QAAQ;MACtDC,OAAO,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE;IAC1B,CAAE;IAAAC,QAAA,eAEFnI,OAAA,CAACjC,SAAS;MAACqK,QAAQ,EAAC,IAAI;MAAAD,QAAA,eACtBnI,OAAA,CAACtB,KAAK;QACJ2J,SAAS,EAAE,CAAE;QACbb,EAAE,EAAE;UACFc,QAAQ,EAAE,QAAQ;UAClBC,YAAY,EAAE,CAAC;UACfC,SAAS,EAAE;QACb,CAAE;QAAAL,QAAA,gBAGFnI,OAAA,CAAC7B,GAAG;UACFqJ,EAAE,EAAE;YACFiB,CAAC,EAAE,CAAC;YACJZ,UAAU,EAAE,6BAA6B5F,QAAQ,CAACC,KAAK,KAAKpD,KAAK,CAACmD,QAAQ,CAACC,KAAK,EAAE,GAAG,CAAC,GAAG;YACzFA,KAAK,EAAE,OAAO;YACdwG,SAAS,EAAE,QAAQ;YACnBpG,QAAQ,EAAE;UACZ,CAAE;UAAA6F,QAAA,gBAEFnI,OAAA,CAACjB,MAAM;YACLyI,EAAE,EAAE;cACFmB,OAAO,EAAE,OAAO;cAChBC,KAAK,EAAE,EAAE;cACTC,MAAM,EAAE,EAAE;cACVC,MAAM,EAAE,aAAa;cACrBC,MAAM,EAAE,iBAAiB;cACzBP,SAAS,EAAE;YACb,CAAE;YAAAL,QAAA,eAEFnI,OAAA,CAACP,MAAM;cAACuJ,QAAQ,EAAC,OAAO;cAACxB,EAAE,EAAE;gBAAEtF,KAAK,EAAED,QAAQ,CAACC;cAAM;YAAE;cAAA+G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACTpJ,OAAA,CAAChC,UAAU;YAACqL,OAAO,EAAC,IAAI;YAACC,SAAS,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAAApB,QAAA,EAAC;UAE1D;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbpJ,OAAA,CAAChC,UAAU;YAACqL,OAAO,EAAC,OAAO;YAAC7B,EAAE,EAAE;cAAEgC,OAAO,EAAE,GAAG;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAtB,QAAA,EACrD/H,OAAO,GAAG,yBAAyB,GAAG;UAAsB;YAAA6I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eAGbpJ,OAAA,CAACf,IAAI;YACHmI,KAAK,EAAEnF,QAAQ,CAACmF,KAAM;YACtB/C,IAAI,EAAC,OAAO;YACZmD,EAAE,EAAE;cACFiC,EAAE,EAAE,CAAC;cACLd,OAAO,EAAE,0BAA0B;cACnCzG,KAAK,EAAE,OAAO;cACdqH,UAAU,EAAE;YACd;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNpJ,OAAA,CAACxB,IAAI;UACHgF,KAAK,EAAEpD,OAAO,GAAG,OAAO,GAAG,UAAW;UACtCsJ,QAAQ,EAAEvG,eAAgB;UAC1BkG,OAAO,EAAC,WAAW;UACnB7B,EAAE,EAAE;YACF,gBAAgB,EAAE;cAChB+B,UAAU,EAAE,GAAG;cACfI,EAAE,EAAE;YACN,CAAC;YACD,iBAAiB,EAAE;cACjBzH,KAAK,EAAED,QAAQ,CAACC;YAClB,CAAC;YACD,sBAAsB,EAAE;cACtB0H,eAAe,EAAE3H,QAAQ,CAACC,KAAK;cAC/B2G,MAAM,EAAE;YACV;UACF,CAAE;UAAAV,QAAA,gBAEFnI,OAAA,CAACvB,GAAG;YAAC2I,KAAK,EAAC,OAAO;YAAC5D,KAAK,EAAC;UAAO;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnCpJ,OAAA,CAACvB,GAAG;YAAC2I,KAAK,EAAC,UAAU;YAAC5D,KAAK,EAAC;UAAU;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eAIPpJ,OAAA,CAAC7B,GAAG;UACFqJ,EAAE,EAAE;YACFiB,CAAC,EAAE,CAAC;YACJZ,UAAU,EAAE,2BAA2B/I,KAAK,CAACmD,QAAQ,CAACC,KAAK,EAAE,IAAI,CAAC,QAAQpD,KAAK,CAACmD,QAAQ,CAACC,KAAK,EAAE,IAAI,CAAC;UACvG,CAAE;UAAAiG,QAAA,gBAGFnI,OAAA,CAAC7B,GAAG;YAACqJ,EAAE,EAAE;cAAEqC,EAAE,EAAE;YAAE,CAAE;YAAA1B,QAAA,eACjBnI,OAAA,CAAC1B,WAAW;cAACwL,SAAS;cAACT,OAAO,EAAC,UAAU;cAAAlB,QAAA,gBACvCnI,OAAA,CAACzB,UAAU;gBAACwL,EAAE,EAAC,mBAAmB;gBAAA5B,QAAA,EAAC;cAAM;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACtDpJ,OAAA,CAAC5B,MAAM;gBACL4L,OAAO,EAAC,mBAAmB;gBAC3BxG,KAAK,EAAElD,IAAK;gBACZ8G,KAAK,EAAC,QAAQ;gBACdsC,QAAQ,EAAEpG,gBAAiB;gBAC3B2G,QAAQ,EAAEvJ,OAAQ;gBAClB8G,EAAE,EAAE;kBACF,oCAAoC,EAAE;oBACpC0C,WAAW,EAAEpL,KAAK,CAACmD,QAAQ,CAACC,KAAK,EAAE,GAAG;kBACxC,CAAC;kBACD,0CAA0C,EAAE;oBAC1CgI,WAAW,EAAEjI,QAAQ,CAACC;kBACxB,CAAC;kBACD,gDAAgD,EAAE;oBAChDgI,WAAW,EAAEjI,QAAQ,CAACC;kBACxB;gBACF,CAAE;gBAAAiG,QAAA,gBAEFnI,OAAA,CAAC3B,QAAQ;kBAACmF,KAAK,EAAC,UAAU;kBAAA2E,QAAA,eACxBnI,OAAA,CAAC7B,GAAG;oBAAAgK,QAAA,gBACFnI,OAAA,CAAChC,UAAU;sBAACqL,OAAO,EAAC,OAAO;sBAAAlB,QAAA,EAAC;oBAAQ;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACjDpJ,OAAA,CAAChC,UAAU;sBAACqL,OAAO,EAAC,SAAS;sBAACnH,KAAK,EAAC,gBAAgB;sBAAAiG,QAAA,EAAC;oBAErD;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACXpJ,OAAA,CAAC3B,QAAQ;kBAACmF,KAAK,EAAC,OAAO;kBAAA2E,QAAA,eACrBnI,OAAA,CAAC7B,GAAG;oBAAAgK,QAAA,gBACFnI,OAAA,CAAChC,UAAU;sBAACqL,OAAO,EAAC,OAAO;sBAAAlB,QAAA,EAAC;oBAAY;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACrDpJ,OAAA,CAAChC,UAAU;sBAACqL,OAAO,EAAC,SAAS;sBAACnH,KAAK,EAAC,gBAAgB;sBAAAiG,QAAA,EAAC;oBAErD;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACXpJ,OAAA,CAAC3B,QAAQ;kBAACmF,KAAK,EAAC,OAAO;kBAAA2E,QAAA,eACrBnI,OAAA,CAAC7B,GAAG;oBAAAgK,QAAA,gBACFnI,OAAA,CAAChC,UAAU;sBAACqL,OAAO,EAAC,OAAO;sBAAAlB,QAAA,EAAC;oBAAa;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACtDpJ,OAAA,CAAChC,UAAU;sBAACqL,OAAO,EAAC,SAAS;sBAACnH,KAAK,EAAC,gBAAgB;sBAAAiG,QAAA,EAAC;oBAErD;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,EAELhJ,OAAO;UAAA;UACN;UACAJ,OAAA,CAAC7B,GAAG;YAAAgK,QAAA,gBACFnI,OAAA,CAAC9B,SAAS;cACR4L,SAAS;cACT1C,KAAK,EAAC,eAAe;cACrBzD,IAAI,EAAC,OAAO;cACZS,IAAI,EAAC,OAAO;cACZ0E,MAAM,EAAC,QAAQ;cACfO,OAAO,EAAC,UAAU;cAClB7F,KAAK,EAAE5C,SAAS,CAACE,KAAM;cACvB4I,QAAQ,EAAEjG,iBAAkB;cAC5BwG,QAAQ,EAAEvJ,OAAQ;cAClB8F,KAAK,EAAE,CAAC,CAAClF,WAAW,CAACR,KAAM;cAC3BqJ,UAAU,EAAE7I,WAAW,CAACR,KAAM;cAC9BsJ,UAAU,EAAE;gBACVC,cAAc,eACZrK,OAAA,CAACrB,cAAc;kBAAC2D,QAAQ,EAAC,OAAO;kBAAA6F,QAAA,eAC9BnI,OAAA,CAACb,KAAK;oBAAC+C,KAAK,EAAC;kBAAQ;oBAAA+G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAEpB,CAAE;cACF5B,EAAE,EAAE;gBACF,0BAA0B,EAAE;kBAC1B,wBAAwB,EAAE;oBACxB0C,WAAW,EAAEjI,QAAQ,CAACC;kBACxB;gBACF,CAAC;gBACD,mCAAmC,EAAE;kBACnCA,KAAK,EAAED,QAAQ,CAACC;gBAClB;cACF;YAAE;cAAA+G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEFpJ,OAAA,CAAC9B,SAAS;cACR4L,SAAS;cACT1C,KAAK,EAAC,UAAU;cAChBzD,IAAI,EAAC,UAAU;cACfS,IAAI,EAAE5D,YAAY,GAAG,MAAM,GAAG,UAAW;cACzCsI,MAAM,EAAC,QAAQ;cACfO,OAAO,EAAC,UAAU;cAClB7F,KAAK,EAAE5C,SAAS,CAACG,QAAS;cAC1B2I,QAAQ,EAAEjG,iBAAkB;cAC5BwG,QAAQ,EAAEvJ,OAAQ;cAClB8F,KAAK,EAAE,CAAC,CAAClF,WAAW,CAACP,QAAS;cAC9BoJ,UAAU,EAAE7I,WAAW,CAACP,QAAS;cACjCqJ,UAAU,EAAE;gBACVC,cAAc,eACZrK,OAAA,CAACrB,cAAc;kBAAC2D,QAAQ,EAAC,OAAO;kBAAA6F,QAAA,eAC9BnI,OAAA,CAACZ,IAAI;oBAAC8C,KAAK,EAAC;kBAAQ;oBAAA+G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CACjB;gBACDkB,YAAY,eACVtK,OAAA,CAACrB,cAAc;kBAAC2D,QAAQ,EAAC,KAAK;kBAAA6F,QAAA,eAC5BnI,OAAA,CAACpB,UAAU;oBACT2L,OAAO,EAAE3F,uBAAwB;oBACjC4F,IAAI,EAAC,KAAK;oBACVP,QAAQ,EAAEvJ,OAAQ;oBAAAyH,QAAA,EAEjB3H,YAAY,gBAAGR,OAAA,CAACR,aAAa;sBAAAyJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAGpJ,OAAA,CAACT,UAAU;sBAAA0J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAEpB,CAAE;cACF5B,EAAE,EAAE;gBACF,0BAA0B,EAAE;kBAC1B,wBAAwB,EAAE;oBACxB0C,WAAW,EAAEjI,QAAQ,CAACC;kBACxB;gBACF,CAAC;gBACD,mCAAmC,EAAE;kBACnCA,KAAK,EAAED,QAAQ,CAACC;gBAClB;cACF;YAAE;cAAA+G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEFpJ,OAAA,CAAC7B,GAAG;cAACqJ,EAAE,EAAE;gBAAEE,OAAO,EAAE,MAAM;gBAAEE,cAAc,EAAE,UAAU;gBAAE6B,EAAE,EAAE,CAAC;gBAAEI,EAAE,EAAE;cAAE,CAAE;cAAA1B,QAAA,eACrEnI,OAAA,CAAChC,UAAU;gBACTqL,OAAO,EAAC,OAAO;gBACf7B,EAAE,EAAE;kBACFiD,MAAM,EAAE,SAAS;kBACjBvI,KAAK,EAAED,QAAQ,CAACC,KAAK;kBACrB,SAAS,EAAE;oBAAEwI,cAAc,EAAE;kBAAY;gBAC3C,CAAE;gBAAAvC,QAAA,EACH;cAED;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENpJ,OAAA,CAAC/B,MAAM;cACLoL,OAAO,EAAC,WAAW;cACnBhF,IAAI,EAAC,OAAO;cACZyF,SAAS;cACTS,OAAO,EAAEhF,iBAAkB;cAC3B0E,QAAQ,EAAEvJ,OAAQ;cAClB8G,EAAE,EAAE;gBACFmC,EAAE,EAAE,GAAG;gBACPC,eAAe,EAAE3H,QAAQ,CAACC,KAAK;gBAC/B,SAAS,EAAE;kBACT0H,eAAe,EAAE9K,KAAK,CAACmD,QAAQ,CAACC,KAAK,EAAE,GAAG;gBAC5C,CAAC;gBACD,YAAY,EAAE;kBACZ0H,eAAe,EAAE9K,KAAK,CAACmD,QAAQ,CAACC,KAAK,EAAE,GAAG;gBAC5C,CAAC;gBACDsG,SAAS,EAAE,cAAc1J,KAAK,CAACmD,QAAQ,CAACC,KAAK,EAAE,GAAG,CAAC;cACrD,CAAE;cACFyI,OAAO,EAAEjK,OAAO,gBAAGV,OAAA,CAAChB,gBAAgB;gBAACqF,IAAI,EAAE,EAAG;gBAACnC,KAAK,EAAC;cAAS;gBAAA+G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGpJ,OAAA,CAACL,YAAY;gBAAAsJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAjB,QAAA,EAEpFzH,OAAO,GAAG,eAAe,GAAG;YAAS;cAAAuI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;UAAA;UAEN;UACApJ,OAAA,CAAC7B,GAAG;YAAAgK,QAAA,gBAEFnI,OAAA,CAAC7B,GAAG;cAACqJ,EAAE,EAAE;gBAAEqC,EAAE,EAAE,CAAC;gBAAEnB,SAAS,EAAE;cAAS,CAAE;cAAAP,QAAA,gBACtCnI,OAAA;gBACE4K,MAAM,EAAC,SAAS;gBAChBC,KAAK,EAAE;kBAAEnD,OAAO,EAAE;gBAAO,CAAE;gBAC3BqC,EAAE,EAAC,sBAAsB;gBACzB3F,IAAI,EAAC,MAAM;gBACXsF,QAAQ,EAAE3F,wBAAyB;gBACnCkG,QAAQ,EAAEvJ;cAAQ;gBAAAuI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eACFpJ,OAAA;gBAAO8K,OAAO,EAAC,sBAAsB;gBAAA3C,QAAA,eACnCnI,OAAA,CAAC7B,GAAG;kBACFqJ,EAAE,EAAE;oBACFlF,QAAQ,EAAE,UAAU;oBACpBoF,OAAO,EAAE,cAAc;oBACvB+C,MAAM,EAAE/J,OAAO,GAAG,aAAa,GAAG;kBACpC,CAAE;kBAAAyH,QAAA,gBAEFnI,OAAA,CAACjB,MAAM;oBACLgM,GAAG,EAAEvJ,mBAAoB;oBACzBgG,EAAE,EAAE;sBACFoB,KAAK,EAAE,EAAE;sBACTC,MAAM,EAAE,EAAE;sBACVE,MAAM,EAAE,cAAcjK,KAAK,CAACmD,QAAQ,CAACC,KAAK,EAAE,GAAG,CAAC,EAAE;sBAClD0H,eAAe,EAAE9K,KAAK,CAACmD,QAAQ,CAACC,KAAK,EAAE,GAAG,CAAC;sBAC3C,SAAS,EAAE;wBACT0H,eAAe,EAAE9K,KAAK,CAACmD,QAAQ,CAACC,KAAK,EAAE,GAAG;sBAC5C;oBACF,CAAE;oBAAAiG,QAAA,eAEFnI,OAAA,CAACJ,WAAW;sBAAC4H,EAAE,EAAE;wBAAEtF,KAAK,EAAED,QAAQ,CAACC;sBAAM;oBAAE;sBAAA+G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC,EACR5H,mBAAmB,iBAClBxB,OAAA,CAACpB,UAAU;oBACTyF,IAAI,EAAC,OAAO;oBACZkG,OAAO,EAAG7G,CAAC,IAAK;sBACdA,CAAC,CAACsH,cAAc,CAAC,CAAC;sBAClBrG,kBAAkB,CAAC,CAAC;oBACtB,CAAE;oBACF6C,EAAE,EAAE;sBACFlF,QAAQ,EAAE,UAAU;sBACpB2I,GAAG,EAAE,CAAC,CAAC;sBACPC,KAAK,EAAE,CAAC,CAAC;sBACTtB,eAAe,EAAE,YAAY;sBAC7B1H,KAAK,EAAE,OAAO;sBACd,SAAS,EAAE;wBACT0H,eAAe,EAAE;sBACnB;oBACF,CAAE;oBAAAzB,QAAA,eAEFnI,OAAA,CAACN,KAAK;sBAACsJ,QAAQ,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CACb;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACRpJ,OAAA,CAAChC,UAAU;gBAACqL,OAAO,EAAC,SAAS;gBAAC3B,OAAO,EAAC,OAAO;gBAACF,EAAE,EAAE;kBAAEiC,EAAE,EAAE,CAAC;kBAAEvH,KAAK,EAAE;gBAAiB,CAAE;gBAAAiG,QAAA,EAClF3G,mBAAmB,GAAG,uBAAuB,GAAG;cAAuC;gBAAAyH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENpJ,OAAA,CAACH,KAAK;cAACsL,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAjD,QAAA,gBAC1BnI,OAAA,CAACH,KAAK;gBAACoI,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAC,QAAA,eACnBnI,OAAA,CAAC9B,SAAS;kBACR4L,SAAS;kBACT1C,KAAK,EAAC,YAAY;kBAClBzD,IAAI,EAAC,WAAW;kBAChBmF,MAAM,EAAC,QAAQ;kBACfO,OAAO,EAAC,UAAU;kBAClB7F,KAAK,EAAExC,YAAY,CAACE,SAAU;kBAC9BwI,QAAQ,EAAE5F,oBAAqB;kBAC/BmG,QAAQ,EAAEvJ,OAAQ;kBAClB8F,KAAK,EAAE,CAAC,CAAClF,WAAW,CAACJ,SAAU;kBAC/BiJ,UAAU,EAAE7I,WAAW,CAACJ,SAAU;kBAClCkJ,UAAU,EAAE;oBACVC,cAAc,eACZrK,OAAA,CAACrB,cAAc;sBAAC2D,QAAQ,EAAC,OAAO;sBAAA6F,QAAA,eAC9BnI,OAAA,CAACX,MAAM;wBAAC6C,KAAK,EAAC;sBAAQ;wBAAA+G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX;kBAEpB,CAAE;kBACF5B,EAAE,EAAE;oBACF,0BAA0B,EAAE;sBAC1B,wBAAwB,EAAE;wBACxB0C,WAAW,EAAEjI,QAAQ,CAACC;sBACxB;oBACF,CAAC;oBACD,mCAAmC,EAAE;sBACnCA,KAAK,EAAED,QAAQ,CAACC;oBAClB;kBACF;gBAAE;kBAAA+G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,eACRpJ,OAAA,CAACH,KAAK;gBAACoI,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAC,QAAA,eACnBnI,OAAA,CAAC9B,SAAS;kBACR4L,SAAS;kBACT1C,KAAK,EAAC,WAAW;kBACjBzD,IAAI,EAAC,UAAU;kBACfmF,MAAM,EAAC,QAAQ;kBACfO,OAAO,EAAC,UAAU;kBAClB7F,KAAK,EAAExC,YAAY,CAACG,QAAS;kBAC7BuI,QAAQ,EAAE5F,oBAAqB;kBAC/BmG,QAAQ,EAAEvJ,OAAQ;kBAClB8F,KAAK,EAAE,CAAC,CAAClF,WAAW,CAACH,QAAS;kBAC9BgJ,UAAU,EAAE7I,WAAW,CAACH,QAAS;kBACjCqG,EAAE,EAAE;oBACF,0BAA0B,EAAE;sBAC1B,wBAAwB,EAAE;wBACxB0C,WAAW,EAAEjI,QAAQ,CAACC;sBACxB;oBACF,CAAC;oBACD,mCAAmC,EAAE;sBACnCA,KAAK,EAAED,QAAQ,CAACC;oBAClB;kBACF;gBAAE;kBAAA+G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAERpJ,OAAA,CAAC9B,SAAS;cACR4L,SAAS;cACT1C,KAAK,EAAC,yBAAyB;cAC/BzD,IAAI,EAAC,OAAO;cACZmF,MAAM,EAAC,QAAQ;cACfO,OAAO,EAAC,UAAU;cAClB7F,KAAK,EAAExC,YAAY,CAACI,KAAM;cAC1BsI,QAAQ,EAAE5F,oBAAqB;cAC/BmG,QAAQ,EAAEvJ,OAAQ;cAClB8F,KAAK,EAAE,CAAC,CAAClF,WAAW,CAACF,KAAM;cAC3B+I,UAAU,EAAE7I,WAAW,CAACF,KAAK,IAAI,qCAAsC;cACvEgJ,UAAU,EAAE;gBACVC,cAAc,eACZrK,OAAA,CAACrB,cAAc;kBAAC2D,QAAQ,EAAC,OAAO;kBAAA6F,QAAA,eAC9BnI,OAAA,CAACV,KAAK;oBAAC4C,KAAK,EAAC;kBAAQ;oBAAA+G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAEpB,CAAE;cACF5B,EAAE,EAAE;gBACF,0BAA0B,EAAE;kBAC1B,wBAAwB,EAAE;oBACxB0C,WAAW,EAAEjI,QAAQ,CAACC;kBACxB;gBACF,CAAC;gBACD,mCAAmC,EAAE;kBACnCA,KAAK,EAAED,QAAQ,CAACC;gBAClB;cACF;YAAE;cAAA+G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEFpJ,OAAA,CAAC9B,SAAS;cACR4L,SAAS;cACT1C,KAAK,EAAC,eAAe;cACrBzD,IAAI,EAAC,OAAO;cACZS,IAAI,EAAC,OAAO;cACZ0E,MAAM,EAAC,QAAQ;cACfO,OAAO,EAAC,UAAU;cAClB7F,KAAK,EAAExC,YAAY,CAACF,KAAM;cAC1B4I,QAAQ,EAAE5F,oBAAqB;cAC/BmG,QAAQ,EAAEvJ,OAAQ;cAClB8F,KAAK,EAAE,CAAC,CAAClF,WAAW,CAACR,KAAM;cAC3BqJ,UAAU,EAAE7I,WAAW,CAACR,KAAM;cAC9BsJ,UAAU,EAAE;gBACVC,cAAc,eACZrK,OAAA,CAACrB,cAAc;kBAAC2D,QAAQ,EAAC,OAAO;kBAAA6F,QAAA,eAC9BnI,OAAA,CAACb,KAAK;oBAAC+C,KAAK,EAAC;kBAAQ;oBAAA+G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAEpB,CAAE;cACF5B,EAAE,EAAE;gBACF,0BAA0B,EAAE;kBAC1B,wBAAwB,EAAE;oBACxB0C,WAAW,EAAEjI,QAAQ,CAACC;kBACxB;gBACF,CAAC;gBACD,mCAAmC,EAAE;kBACnCA,KAAK,EAAED,QAAQ,CAACC;gBAClB;cACF;YAAE;cAAA+G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEFpJ,OAAA,CAAC9B,SAAS;cACR4L,SAAS;cACT1C,KAAK,EAAC,UAAU;cAChBzD,IAAI,EAAC,UAAU;cACfS,IAAI,EAAE5D,YAAY,GAAG,MAAM,GAAG,UAAW;cACzCsI,MAAM,EAAC,QAAQ;cACfO,OAAO,EAAC,UAAU;cAClB7F,KAAK,EAAExC,YAAY,CAACD,QAAS;cAC7B2I,QAAQ,EAAE5F,oBAAqB;cAC/BmG,QAAQ,EAAEvJ,OAAQ;cAClB8F,KAAK,EAAE,CAAC,CAAClF,WAAW,CAACP,QAAS;cAC9BoJ,UAAU,EAAE7I,WAAW,CAACP,QAAQ,IAAI,+EAAgF;cACpHqJ,UAAU,EAAE;gBACVC,cAAc,eACZrK,OAAA,CAACrB,cAAc;kBAAC2D,QAAQ,EAAC,OAAO;kBAAA6F,QAAA,eAC9BnI,OAAA,CAACZ,IAAI;oBAAC8C,KAAK,EAAC;kBAAQ;oBAAA+G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CACjB;gBACDkB,YAAY,eACVtK,OAAA,CAACrB,cAAc;kBAAC2D,QAAQ,EAAC,KAAK;kBAAA6F,QAAA,eAC5BnI,OAAA,CAACpB,UAAU;oBACT2L,OAAO,EAAE3F,uBAAwB;oBACjC4F,IAAI,EAAC,KAAK;oBACVP,QAAQ,EAAEvJ,OAAQ;oBAAAyH,QAAA,EAEjB3H,YAAY,gBAAGR,OAAA,CAACR,aAAa;sBAAAyJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAGpJ,OAAA,CAACT,UAAU;sBAAA0J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAEpB,CAAE;cACF5B,EAAE,EAAE;gBACF,0BAA0B,EAAE;kBAC1B,wBAAwB,EAAE;oBACxB0C,WAAW,EAAEjI,QAAQ,CAACC;kBACxB;gBACF,CAAC;gBACD,mCAAmC,EAAE;kBACnCA,KAAK,EAAED,QAAQ,CAACC;gBAClB;cACF;YAAE;cAAA+G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEFpJ,OAAA,CAAC/B,MAAM;cACLoL,OAAO,EAAC,WAAW;cACnBhF,IAAI,EAAC,OAAO;cACZyF,SAAS;cACTtC,EAAE,EAAE;gBACFiC,EAAE,EAAE,CAAC;gBACLE,EAAE,EAAE,GAAG;gBACPC,eAAe,EAAE3H,QAAQ,CAACC,KAAK;gBAC/B,SAAS,EAAE;kBACT0H,eAAe,EAAE9K,KAAK,CAACmD,QAAQ,CAACC,KAAK,EAAE,GAAG;gBAC5C,CAAC;gBACD,YAAY,EAAE;kBACZ0H,eAAe,EAAE9K,KAAK,CAACmD,QAAQ,CAACC,KAAK,EAAE,GAAG;gBAC5C,CAAC;gBACDsG,SAAS,EAAE,cAAc1J,KAAK,CAACmD,QAAQ,CAACC,KAAK,EAAE,GAAG,CAAC;cACrD,CAAE;cACFqI,OAAO,EAAE7D,oBAAqB;cAC9BuD,QAAQ,EAAEvJ,OAAQ;cAClBiK,OAAO,EAAEjK,OAAO,gBAAGV,OAAA,CAAChB,gBAAgB;gBAACqF,IAAI,EAAE,EAAG;gBAACnC,KAAK,EAAC;cAAS;gBAAA+G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGpJ,OAAA,CAACL,YAAY;gBAAAsJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAjB,QAAA,EAEpFzH,OAAO,GAAG,qBAAqB,GAAG;YAAgB;cAAAuI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNpJ,OAAA,CAAC7B,GAAG;UACFqJ,EAAE,EAAE;YACFiB,CAAC,EAAE,CAAC;YACJ4C,SAAS,EAAE,aAAalL,KAAK,CAAC+G,OAAO,CAACoE,OAAO,EAAE;YAC/C1B,eAAe,EAAE9K,KAAK,CAACqB,KAAK,CAAC+G,OAAO,CAACW,UAAU,CAAC0D,KAAK,EAAE,GAAG,CAAC;YAC3D7C,SAAS,EAAE;UACb,CAAE;UAAAP,QAAA,eAEFnI,OAAA,CAAChC,UAAU;YAACqL,OAAO,EAAC,OAAO;YAACnH,KAAK,EAAC,gBAAgB;YAAAiG,QAAA,GAC/C/H,OAAO,GAAG,wBAAwB,GAAG,0BAA0B,eAChEJ,OAAA,CAAC/B,MAAM;cACLuJ,EAAE,EAAE;gBACFgE,EAAE,EAAE,CAAC;gBACLtJ,KAAK,EAAED,QAAQ,CAACC,KAAK;gBACrBqH,UAAU,EAAE;cACd,CAAE;cACFgB,OAAO,EAAEA,CAAA,KAAMlK,UAAU,CAAC,CAACD,OAAO,CAAE;cACpC6J,QAAQ,EAAEvJ,OAAQ;cAAAyH,QAAA,EAEjB/H,OAAO,GAAG,SAAS,GAAG;YAAS;cAAA6I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAAClJ,EAAA,CAz2BID,iBAAiB;EAAA,QACPpB,QAAQ;AAAA;AAAA4M,EAAA,GADlBxL,iBAAiB;AA22BvB,eAAeA,iBAAiB;AAAC,IAAAwL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}