{"ast": null, "code": "var _jsxFileName = \"D:\\\\Github_Repos\\\\cafe-mangement-system-react\\\\cafe-management-sys\\\\src\\\\pages\\\\customer\\\\LoginRegisterPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Container, Typography, Button, TextField, Box, Select, MenuItem, FormControl, InputLabel, Tabs, Tab, Paper, InputAdornment, IconButton, useTheme, alpha, Avatar, CircularProgress, Chip } from '@mui/material';\nimport Swal from 'sweetalert2';\nimport { Email, Lock, Person, Phone, Visibility, VisibilityOff, Coffee, Close, ArrowForward, PhotoCamera } from '@mui/icons-material';\nimport Grid2 from '@mui/material/Unstable_Grid2';\nimport { authAPI } from '../../services/api';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoginRegisterPage = () => {\n  _s();\n  const theme = useTheme();\n  const {\n    login\n  } = useAuth();\n  const [isLogin, setIsLogin] = useState(true);\n  const [role, setRole] = useState('customer');\n  const [showPassword, setShowPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [loginData, setLoginData] = useState({\n    email: '',\n    password: ''\n  });\n  const [registerData, setRegisterData] = useState({\n    firstName: '',\n    lastName: '',\n    phone: '',\n    email: '',\n    password: '',\n    profilePhoto: null\n  });\n  const [fieldErrors, setFieldErrors] = useState({});\n  const [profilePhotoPreview, setProfilePhotoPreview] = useState('');\n  const showSuccessPopup = message => {\n    Swal.fire({\n      icon: 'success',\n      title: 'Success!',\n      text: message,\n      confirmButtonColor: roleInfo.color,\n      timer: 3000,\n      showConfirmButton: false,\n      toast: true,\n      position: 'top-end',\n      showClass: {\n        popup: 'animate__animated animate__slideInRight'\n      },\n      hideClass: {\n        popup: 'animate__animated animate__slideOutRight'\n      }\n    });\n  };\n  const showErrorPopup = message => {\n    Swal.fire({\n      icon: 'error',\n      title: 'Oops...',\n      text: message,\n      confirmButtonColor: roleInfo.color,\n      confirmButtonText: 'Try Again',\n      showClass: {\n        popup: 'animate__animated animate__shakeX'\n      }\n    });\n  };\n  const showValidationErrorPopup = errors => {\n    const errorList = Object.values(errors).join('\\n• ');\n    Swal.fire({\n      icon: 'warning',\n      title: 'Please check your input',\n      html: `<div style=\"text-align: left;\">• ${Object.values(errors).join('<br>• ')}</div>`,\n      confirmButtonColor: roleInfo.color,\n      confirmButtonText: 'Got it'\n    });\n  };\n  const handleTabChange = (event, newValue) => {\n    setIsLogin(newValue === 'login');\n    setFieldErrors({});\n  };\n  const handleRoleChange = event => {\n    setRole(event.target.value);\n    setFieldErrors({});\n  };\n  const handleLoginChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setLoginData(prevData => ({\n      ...prevData,\n      [name]: value\n    }));\n\n    // Clear field-specific error\n    if (fieldErrors[name]) {\n      setFieldErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  const handleRegisterChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setRegisterData(prevData => ({\n      ...prevData,\n      [name]: value\n    }));\n\n    // Clear field-specific error\n    if (fieldErrors[name]) {\n      setFieldErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  const handleProfilePhotoChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      // Validate file type\n      const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];\n      if (!validTypes.includes(file.type)) {\n        showErrorPopup('Please select a valid image file (JPEG, PNG, GIF, or WebP)');\n        return;\n      }\n\n      // Validate file size (max 5MB)\n      if (file.size > 5 * 1024 * 1024) {\n        showErrorPopup('Profile photo must be less than 5MB');\n        return;\n      }\n      setRegisterData(prev => ({\n        ...prev,\n        profilePhoto: file\n      }));\n\n      // Create preview\n      const reader = new FileReader();\n      reader.onload = e => {\n        setProfilePhotoPreview(e.target.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const removeProfilePhoto = () => {\n    setRegisterData(prev => ({\n      ...prev,\n      profilePhoto: null\n    }));\n    setProfilePhotoPreview('');\n  };\n  const handleClickShowPassword = () => {\n    setShowPassword(!showPassword);\n  };\n  const validateLoginForm = () => {\n    const errors = {};\n    if (!loginData.email) {\n      errors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(loginData.email)) {\n      errors.email = 'Please enter a valid email address';\n    }\n    if (!loginData.password) {\n      errors.password = 'Password is required';\n    }\n    setFieldErrors(errors);\n\n    // Show popup for validation errors\n    if (Object.keys(errors).length > 0) {\n      showValidationErrorPopup(errors);\n    }\n    return Object.keys(errors).length === 0;\n  };\n  const validateRegisterForm = () => {\n    const errors = {};\n\n    // First name validation (2-50 characters)\n    if (!registerData.firstName.trim()) {\n      errors.firstName = 'First name is required';\n    } else if (registerData.firstName.trim().length < 2 || registerData.firstName.trim().length > 50) {\n      errors.firstName = 'First name must be between 2 and 50 characters';\n    }\n\n    // Last name validation (2-50 characters)\n    if (!registerData.lastName.trim()) {\n      errors.lastName = 'Last name is required';\n    } else if (registerData.lastName.trim().length < 2 || registerData.lastName.trim().length > 50) {\n      errors.lastName = 'Last name must be between 2 and 50 characters';\n    }\n\n    // Email validation\n    if (!registerData.email) {\n      errors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(registerData.email)) {\n      errors.email = 'Please enter a valid email address';\n    } else if (registerData.email.length > 254) {\n      errors.email = 'Email address is too long';\n    }\n\n    // Enhanced password validation to match server requirements\n    if (!registerData.password) {\n      errors.password = 'Password is required';\n    } else {\n      const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$/;\n      if (!passwordRegex.test(registerData.password)) {\n        errors.password = 'Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character';\n      }\n    }\n\n    // Phone validation (enhanced)\n    if (registerData.phone && registerData.phone.trim()) {\n      const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\n      const cleanPhone = registerData.phone.replace(/[\\s\\-\\(\\)]/g, '');\n      if (!phoneRegex.test(cleanPhone)) {\n        errors.phone = 'Please enter a valid phone number (e.g., +1234567890)';\n      }\n    }\n    setFieldErrors(errors);\n\n    // Show popup for validation errors\n    if (Object.keys(errors).length > 0) {\n      showValidationErrorPopup(errors);\n    }\n    return Object.keys(errors).length === 0;\n  };\n  const handleLoginSubmit = async () => {\n    if (!validateLoginForm()) return;\n    setLoading(true);\n    try {\n      // Use the API service for consistent error handling\n      const responseData = await authAPI.login(loginData);\n\n      // Extract data from the standardized response format\n      const {\n        data\n      } = responseData;\n      if (!data || !data.token || !data.user) {\n        throw new Error('Invalid response format from server');\n      }\n\n      // Use AuthContext login method for proper authentication management\n      const loginSuccess = login(data.user, data.token, data.userType);\n      if (!loginSuccess) {\n        throw new Error('Failed to store authentication data');\n      }\n      showSuccessPopup(responseData.message || 'Login successful! Redirecting to your dashboard...');\n      setLoginData({\n        email: '',\n        password: ''\n      });\n\n      // Redirect based on user type after a short delay\n      setTimeout(() => {\n        if (data.userType === 'customer') {\n          window.location.href = '/menu';\n        } else {\n          // Staff or Admin (userType is 'staffOrAdmin')\n          window.location.href = '/admin';\n        }\n      }, 1500);\n    } catch (error) {\n      console.error('Login error:', error);\n      showErrorPopup(error.message || 'An error occurred during login');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleRegisterSubmit = async () => {\n    if (!validateRegisterForm()) return;\n    setLoading(true);\n    try {\n      const formData = new FormData();\n      formData.append('firstName', registerData.firstName.trim());\n      formData.append('lastName', registerData.lastName.trim());\n      formData.append('email', registerData.email.toLowerCase().trim());\n      formData.append('password', registerData.password);\n\n      // Only append phone if it's provided and not empty\n      if (registerData.phone && registerData.phone.trim()) {\n        formData.append('phone', registerData.phone.trim());\n      }\n      if (registerData.profilePhoto) {\n        formData.append('profilePhoto', registerData.profilePhoto);\n      }\n\n      // Use the appropriate API service method\n      let responseData;\n      if (role === 'customer') {\n        responseData = await authAPI.registerCustomer(formData);\n      } else {\n        formData.append('role', role);\n        responseData = await authAPI.registerStaff(formData);\n      }\n      showSuccessPopup(responseData.message || 'Registration successful! You can now login with your new account.');\n      setRegisterData({\n        firstName: '',\n        lastName: '',\n        phone: '',\n        email: '',\n        password: '',\n        profilePhoto: null\n      });\n      setProfilePhotoPreview('');\n\n      // Switch to login tab after successful registration\n      setTimeout(() => {\n        setIsLogin(true);\n      }, 2000);\n    } catch (error) {\n      console.error('Registration error:', error);\n      showErrorPopup(error.message || 'An error occurred during registration');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Get role-specific colors and labels\n  const getRoleInfo = () => {\n    switch (role) {\n      case 'admin':\n        return {\n          color: theme.palette.error.main,\n          label: 'Administrator',\n          description: 'Full system access and management'\n        };\n      case 'staff':\n        return {\n          color: theme.palette.info.main,\n          label: 'Staff Member',\n          description: 'Order management and customer service'\n        };\n      default:\n        return {\n          color: theme.palette.primary.main,\n          label: 'Customer',\n          description: 'Browse menu and place orders'\n        };\n    }\n  };\n  const roleInfo = getRoleInfo();\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      minHeight: '100vh',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      background: `linear-gradient(135deg, \n                    ${alpha(theme.palette.primary.light, 0.15)} 0%, \n                    ${alpha(theme.palette.primary.main, 0.05)} 50%,\n                    ${theme.palette.background.default} 100%)`,\n      padding: {\n        xs: 2,\n        sm: 4\n      }\n    },\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"sm\",\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        elevation: 4,\n        sx: {\n          overflow: 'hidden',\n          borderRadius: 3,\n          boxShadow: '0 10px 40px rgba(0, 0, 0, 0.1)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            p: 3,\n            background: `linear-gradient(to right, ${roleInfo.color}, ${alpha(roleInfo.color, 0.8)})`,\n            color: 'white',\n            textAlign: 'center',\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            sx: {\n              bgcolor: 'white',\n              width: 56,\n              height: 56,\n              margin: '0 auto 16px',\n              border: '2px solid white',\n              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'\n            },\n            children: /*#__PURE__*/_jsxDEV(Coffee, {\n              fontSize: \"large\",\n              sx: {\n                color: roleInfo.color\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            component: \"h1\",\n            fontWeight: \"bold\",\n            children: \"Caf\\xE9 Management System\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              opacity: 0.9,\n              mt: 1\n            },\n            children: isLogin ? 'Sign in to your account' : 'Create a new account'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n            label: roleInfo.label,\n            size: \"small\",\n            sx: {\n              mt: 2,\n              bgcolor: 'rgba(255, 255, 255, 0.2)',\n              color: 'white',\n              fontWeight: 'bold'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n          value: isLogin ? 'login' : 'register',\n          onChange: handleTabChange,\n          variant: \"fullWidth\",\n          sx: {\n            '& .MuiTab-root': {\n              fontWeight: 600,\n              py: 2\n            },\n            '& .Mui-selected': {\n              color: roleInfo.color\n            },\n            '& .MuiTabs-indicator': {\n              backgroundColor: roleInfo.color,\n              height: 3\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Login\",\n            value: \"login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Register\",\n            value: \"register\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            p: 4,\n            background: `linear-gradient(135deg, ${alpha(roleInfo.color, 0.05)} 0%, ${alpha(roleInfo.color, 0.02)} 100%)`\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 3\n            },\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              variant: \"outlined\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                id: \"role-select-label\",\n                children: \"I am a\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                labelId: \"role-select-label\",\n                value: role,\n                label: \"I am a\",\n                onChange: handleRoleChange,\n                disabled: loading,\n                sx: {\n                  '& .MuiOutlinedInput-notchedOutline': {\n                    borderColor: alpha(roleInfo.color, 0.5)\n                  },\n                  '&:hover .MuiOutlinedInput-notchedOutline': {\n                    borderColor: roleInfo.color\n                  },\n                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\n                    borderColor: roleInfo.color\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"customer\",\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      children: \"Customer\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 497,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: \"Browse menu and place orders\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 498,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 496,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 495,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"staff\",\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      children: \"Staff Member\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 505,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: \"Order management and customer service\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 506,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 504,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 503,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"admin\",\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      children: \"Administrator\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 513,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: \"Full system access and management\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 514,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 512,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 13\n          }, this), isLogin ?\n          /*#__PURE__*/\n          // Login Form\n          _jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Email Address\",\n              name: \"email\",\n              type: \"email\",\n              margin: \"normal\",\n              variant: \"outlined\",\n              value: loginData.email,\n              onChange: handleLoginChange,\n              disabled: loading,\n              error: !!fieldErrors.email,\n              helperText: fieldErrors.email,\n              InputProps: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(Email, {\n                    color: \"action\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 541,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 540,\n                  columnNumber: 23\n                }, this)\n              },\n              sx: {\n                '& .MuiOutlinedInput-root': {\n                  '&.Mui-focused fieldset': {\n                    borderColor: roleInfo.color\n                  }\n                },\n                '& .MuiInputLabel-root.Mui-focused': {\n                  color: roleInfo.color\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Password\",\n              name: \"password\",\n              type: showPassword ? 'text' : 'password',\n              margin: \"normal\",\n              variant: \"outlined\",\n              value: loginData.password,\n              onChange: handleLoginChange,\n              disabled: loading,\n              error: !!fieldErrors.password,\n              helperText: fieldErrors.password,\n              InputProps: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(Lock, {\n                    color: \"action\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 572,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 571,\n                  columnNumber: 23\n                }, this),\n                endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"end\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: handleClickShowPassword,\n                    edge: \"end\",\n                    disabled: loading,\n                    children: showPassword ? /*#__PURE__*/_jsxDEV(VisibilityOff, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 582,\n                      columnNumber: 43\n                    }, this) : /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 582,\n                      columnNumber: 63\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 577,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 576,\n                  columnNumber: 23\n                }, this)\n              },\n              sx: {\n                '& .MuiOutlinedInput-root': {\n                  '&.Mui-focused fieldset': {\n                    borderColor: roleInfo.color\n                  }\n                },\n                '& .MuiInputLabel-root.Mui-focused': {\n                  color: roleInfo.color\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mt: 1,\n                mb: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  cursor: 'pointer',\n                  color: roleInfo.color,\n                  '&:hover': {\n                    textDecoration: 'underline'\n                  }\n                },\n                children: \"Forgot password?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 600,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 599,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              size: \"large\",\n              fullWidth: true,\n              onClick: handleLoginSubmit,\n              disabled: loading,\n              sx: {\n                py: 1.5,\n                backgroundColor: roleInfo.color,\n                '&:hover': {\n                  backgroundColor: alpha(roleInfo.color, 0.9)\n                },\n                '&:disabled': {\n                  backgroundColor: alpha(roleInfo.color, 0.5)\n                },\n                boxShadow: `0 4px 12px ${alpha(roleInfo.color, 0.3)}`\n              },\n              endIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 20,\n                color: \"inherit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 629,\n                columnNumber: 38\n              }, this) : /*#__PURE__*/_jsxDEV(ArrowForward, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 629,\n                columnNumber: 87\n              }, this),\n              children: loading ? 'Signing In...' : 'Sign In'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 15\n          }, this) :\n          /*#__PURE__*/\n          // Register Form\n          _jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 3,\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                accept: \"image/*\",\n                style: {\n                  display: 'none'\n                },\n                id: \"profile-photo-upload\",\n                type: \"file\",\n                onChange: handleProfilePhotoChange,\n                disabled: loading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 639,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"profile-photo-upload\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    position: 'relative',\n                    display: 'inline-block',\n                    cursor: loading ? 'not-allowed' : 'pointer'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    src: profilePhotoPreview,\n                    sx: {\n                      width: 80,\n                      height: 80,\n                      border: `2px dashed ${alpha(roleInfo.color, 0.5)}`,\n                      backgroundColor: alpha(roleInfo.color, 0.1),\n                      '&:hover': {\n                        backgroundColor: alpha(roleInfo.color, 0.2)\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(PhotoCamera, {\n                      sx: {\n                        color: roleInfo.color\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 667,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 655,\n                    columnNumber: 23\n                  }, this), profilePhotoPreview && /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: e => {\n                      e.preventDefault();\n                      removeProfilePhoto();\n                    },\n                    sx: {\n                      position: 'absolute',\n                      top: -5,\n                      right: -5,\n                      backgroundColor: 'error.main',\n                      color: 'white',\n                      '&:hover': {\n                        backgroundColor: 'error.dark'\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Close, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 687,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 670,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 648,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 647,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                display: \"block\",\n                sx: {\n                  mt: 1,\n                  color: 'text.secondary'\n                },\n                children: profilePhotoPreview ? 'Click to change photo' : 'Click to add profile photo (optional)'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 692,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 638,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid2, {\n              container: true,\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Grid2, {\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"First Name\",\n                  name: \"firstName\",\n                  margin: \"normal\",\n                  variant: \"outlined\",\n                  value: registerData.firstName,\n                  onChange: handleRegisterChange,\n                  disabled: loading,\n                  error: !!fieldErrors.firstName,\n                  helperText: fieldErrors.firstName,\n                  InputProps: {\n                    startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                      position: \"start\",\n                      children: /*#__PURE__*/_jsxDEV(Person, {\n                        color: \"action\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 713,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 712,\n                      columnNumber: 27\n                    }, this)\n                  },\n                  sx: {\n                    '& .MuiOutlinedInput-root': {\n                      '&.Mui-focused fieldset': {\n                        borderColor: roleInfo.color\n                      }\n                    },\n                    '& .MuiInputLabel-root.Mui-focused': {\n                      color: roleInfo.color\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 699,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 698,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid2, {\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Last Name\",\n                  name: \"lastName\",\n                  margin: \"normal\",\n                  variant: \"outlined\",\n                  value: registerData.lastName,\n                  onChange: handleRegisterChange,\n                  disabled: loading,\n                  error: !!fieldErrors.lastName,\n                  helperText: fieldErrors.lastName,\n                  sx: {\n                    '& .MuiOutlinedInput-root': {\n                      '&.Mui-focused fieldset': {\n                        borderColor: roleInfo.color\n                      }\n                    },\n                    '& .MuiInputLabel-root.Mui-focused': {\n                      color: roleInfo.color\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 730,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 729,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 697,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Phone Number (Optional)\",\n              name: \"phone\",\n              margin: \"normal\",\n              variant: \"outlined\",\n              value: registerData.phone,\n              onChange: handleRegisterChange,\n              disabled: loading,\n              error: !!fieldErrors.phone,\n              helperText: fieldErrors.phone || 'e.g., +1234567890 or (*************',\n              InputProps: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(Phone, {\n                    color: \"action\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 769,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 768,\n                  columnNumber: 23\n                }, this)\n              },\n              sx: {\n                '& .MuiOutlinedInput-root': {\n                  '&.Mui-focused fieldset': {\n                    borderColor: roleInfo.color\n                  }\n                },\n                '& .MuiInputLabel-root.Mui-focused': {\n                  color: roleInfo.color\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 755,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Email Address\",\n              name: \"email\",\n              type: \"email\",\n              margin: \"normal\",\n              variant: \"outlined\",\n              value: registerData.email,\n              onChange: handleRegisterChange,\n              disabled: loading,\n              error: !!fieldErrors.email,\n              helperText: fieldErrors.email,\n              InputProps: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(Email, {\n                    color: \"action\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 800,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 799,\n                  columnNumber: 23\n                }, this)\n              },\n              sx: {\n                '& .MuiOutlinedInput-root': {\n                  '&.Mui-focused fieldset': {\n                    borderColor: roleInfo.color\n                  }\n                },\n                '& .MuiInputLabel-root.Mui-focused': {\n                  color: roleInfo.color\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 785,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Password\",\n              name: \"password\",\n              type: showPassword ? 'text' : 'password',\n              margin: \"normal\",\n              variant: \"outlined\",\n              value: registerData.password,\n              onChange: handleRegisterChange,\n              disabled: loading,\n              error: !!fieldErrors.password,\n              helperText: fieldErrors.password || 'Minimum 8 characters with uppercase, lowercase, number, and special character',\n              InputProps: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(Lock, {\n                    color: \"action\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 831,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 830,\n                  columnNumber: 23\n                }, this),\n                endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"end\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: handleClickShowPassword,\n                    edge: \"end\",\n                    disabled: loading,\n                    children: showPassword ? /*#__PURE__*/_jsxDEV(VisibilityOff, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 841,\n                      columnNumber: 43\n                    }, this) : /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 841,\n                      columnNumber: 63\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 836,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 835,\n                  columnNumber: 23\n                }, this)\n              },\n              sx: {\n                '& .MuiOutlinedInput-root': {\n                  '&.Mui-focused fieldset': {\n                    borderColor: roleInfo.color\n                  }\n                },\n                '& .MuiInputLabel-root.Mui-focused': {\n                  color: roleInfo.color\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 816,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              size: \"large\",\n              fullWidth: true,\n              sx: {\n                mt: 3,\n                py: 1.5,\n                backgroundColor: roleInfo.color,\n                '&:hover': {\n                  backgroundColor: alpha(roleInfo.color, 0.9)\n                },\n                '&:disabled': {\n                  backgroundColor: alpha(roleInfo.color, 0.5)\n                },\n                boxShadow: `0 4px 12px ${alpha(roleInfo.color, 0.3)}`\n              },\n              onClick: handleRegisterSubmit,\n              disabled: loading,\n              endIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 20,\n                color: \"inherit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 876,\n                columnNumber: 38\n              }, this) : /*#__PURE__*/_jsxDEV(ArrowForward, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 876,\n                columnNumber: 87\n              }, this),\n              children: loading ? 'Creating Account...' : 'Create Account'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 858,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 636,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            p: 3,\n            borderTop: `1px solid ${theme.palette.divider}`,\n            backgroundColor: alpha(theme.palette.background.paper, 0.5),\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: [isLogin ? \"Don't have an account?\" : \"Already have an account?\", /*#__PURE__*/_jsxDEV(Button, {\n              sx: {\n                ml: 1,\n                color: roleInfo.color,\n                fontWeight: 'bold'\n              },\n              onClick: () => setIsLogin(!isLogin),\n              disabled: loading,\n              children: isLogin ? \"Sign Up\" : \"Sign In\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 895,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 893,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 885,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 392,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 391,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 378,\n    columnNumber: 5\n  }, this);\n};\n_s(LoginRegisterPage, \"zEglhZ+L1BA5M68vHcCKlzfYayo=\", false, function () {\n  return [useTheme, useAuth];\n});\n_c = LoginRegisterPage;\nexport default LoginRegisterPage;\nvar _c;\n$RefreshReg$(_c, \"LoginRegisterPage\");", "map": {"version": 3, "names": ["React", "useState", "Container", "Typography", "<PERSON><PERSON>", "TextField", "Box", "Select", "MenuItem", "FormControl", "InputLabel", "Tabs", "Tab", "Paper", "InputAdornment", "IconButton", "useTheme", "alpha", "Avatar", "CircularProgress", "Chip", "<PERSON><PERSON>", "Email", "Lock", "Person", "Phone", "Visibility", "VisibilityOff", "Coffee", "Close", "ArrowForward", "PhotoCamera", "Grid2", "authAPI", "useAuth", "jsxDEV", "_jsxDEV", "LoginRegisterPage", "_s", "theme", "login", "is<PERSON>ogin", "setIsLogin", "role", "setRole", "showPassword", "setShowPassword", "loading", "setLoading", "loginData", "setLoginData", "email", "password", "registerData", "setRegisterData", "firstName", "lastName", "phone", "profilePhoto", "fieldErrors", "setFieldErrors", "profilePhotoPreview", "setProfilePhotoPreview", "showSuccessPopup", "message", "fire", "icon", "title", "text", "confirmButtonColor", "roleInfo", "color", "timer", "showConfirmButton", "toast", "position", "showClass", "popup", "hideClass", "showErrorPopup", "confirmButtonText", "showValidationErrorPopup", "errors", "errorList", "Object", "values", "join", "html", "handleTabChange", "event", "newValue", "handleRoleChange", "target", "value", "handleLoginChange", "e", "name", "prevData", "prev", "handleRegisterChange", "handleProfilePhotoChange", "file", "files", "validTypes", "includes", "type", "size", "reader", "FileReader", "onload", "result", "readAsDataURL", "removeProfilePhoto", "handleClickShowPassword", "validateLoginForm", "test", "keys", "length", "validateRegisterForm", "trim", "passwordRegex", "phoneRegex", "cleanPhone", "replace", "handleLoginSubmit", "responseData", "data", "token", "user", "Error", "loginSuccess", "userType", "setTimeout", "window", "location", "href", "error", "console", "handleRegisterSubmit", "formData", "FormData", "append", "toLowerCase", "registerCustomer", "registerStaff", "getRoleInfo", "palette", "main", "label", "description", "info", "primary", "sx", "minHeight", "display", "alignItems", "justifyContent", "background", "light", "default", "padding", "xs", "sm", "children", "max<PERSON><PERSON><PERSON>", "elevation", "overflow", "borderRadius", "boxShadow", "p", "textAlign", "bgcolor", "width", "height", "margin", "border", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "component", "fontWeight", "opacity", "mt", "onChange", "py", "backgroundColor", "mb", "fullWidth", "id", "labelId", "disabled", "borderColor", "helperText", "InputProps", "startAdornment", "endAdornment", "onClick", "edge", "cursor", "textDecoration", "endIcon", "accept", "style", "htmlFor", "src", "preventDefault", "top", "right", "container", "spacing", "borderTop", "divider", "paper", "ml", "_c", "$RefreshReg$"], "sources": ["D:/Github_Repos/cafe-mangement-system-react/cafe-management-sys/src/pages/customer/LoginRegisterPage.jsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport {\r\n  Con<PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>,\r\n  Button,\r\n  TextField,\r\n  Box,\r\n  Select,\r\n  MenuItem,\r\n  FormControl,\r\n  InputLabel,\r\n  Tabs,\r\n  Tab,\r\n  Paper,\r\n  InputAdornment,\r\n  IconButton,\r\n  useTheme,\r\n  alpha,\r\n  Avatar,\r\n  CircularProgress,\r\n  Chip\r\n} from '@mui/material';\r\nimport Swal from 'sweetalert2';\r\nimport {\r\n  Email,\r\n  Lock,\r\n  Person,\r\n  Phone,\r\n  Visibility,\r\n  VisibilityOff,\r\n  Coffee,\r\n  Close,\r\n  ArrowForward,\r\n  PhotoCamera,\r\n} from '@mui/icons-material';\r\n\r\nimport Grid2 from '@mui/material/Unstable_Grid2';\r\nimport { authAPI } from '../../services/api';\r\nimport { useAuth } from '../../contexts/AuthContext';\r\n\r\nconst LoginRegisterPage = () => {\r\n  const theme = useTheme();\r\n  const { login } = useAuth();\r\n  const [isLogin, setIsLogin] = useState(true);\r\n  const [role, setRole] = useState('customer');\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  const [loginData, setLoginData] = useState({\r\n    email: '',\r\n    password: ''\r\n  });\r\n\r\n  const [registerData, setRegisterData] = useState({\r\n    firstName: '',\r\n    lastName: '',\r\n    phone: '',\r\n    email: '',\r\n    password: '',\r\n    profilePhoto: null\r\n  });\r\n\r\n  const [fieldErrors, setFieldErrors] = useState({});\r\n  const [profilePhotoPreview, setProfilePhotoPreview] = useState('');\r\n\r\n  const showSuccessPopup = (message) => {\r\n    Swal.fire({\r\n      icon: 'success',\r\n      title: 'Success!',\r\n      text: message,\r\n      confirmButtonColor: roleInfo.color,\r\n      timer: 3000,\r\n      showConfirmButton: false,\r\n      toast: true,\r\n      position: 'top-end',\r\n      showClass: {\r\n        popup: 'animate__animated animate__slideInRight'\r\n      },\r\n      hideClass: {\r\n        popup: 'animate__animated animate__slideOutRight'\r\n      }\r\n    });\r\n  };\r\n\r\n  const showErrorPopup = (message) => {\r\n    Swal.fire({\r\n      icon: 'error',\r\n      title: 'Oops...',\r\n      text: message,\r\n      confirmButtonColor: roleInfo.color,\r\n      confirmButtonText: 'Try Again',\r\n      showClass: {\r\n        popup: 'animate__animated animate__shakeX'\r\n      }\r\n    });\r\n  };\r\n\r\n  const showValidationErrorPopup = (errors) => {\r\n    const errorList = Object.values(errors).join('\\n• ');\r\n    Swal.fire({\r\n      icon: 'warning',\r\n      title: 'Please check your input',\r\n      html: `<div style=\"text-align: left;\">• ${Object.values(errors).join('<br>• ')}</div>`,\r\n      confirmButtonColor: roleInfo.color,\r\n      confirmButtonText: 'Got it'\r\n    });\r\n  };\r\n  const handleTabChange = (event, newValue) => {\r\n    setIsLogin(newValue === 'login');\r\n    setFieldErrors({});\r\n  };\r\n\r\n  const handleRoleChange = (event) => {\r\n    setRole(event.target.value);\r\n    setFieldErrors({});\r\n  };\r\n\r\n  const handleLoginChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setLoginData((prevData) => ({ ...prevData, [name]: value }));\r\n\r\n    // Clear field-specific error\r\n    if (fieldErrors[name]) {\r\n      setFieldErrors(prev => ({ ...prev, [name]: '' }));\r\n    }\r\n  };\r\n\r\n  const handleRegisterChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setRegisterData((prevData) => ({ ...prevData, [name]: value }));\r\n\r\n    // Clear field-specific error\r\n    if (fieldErrors[name]) {\r\n      setFieldErrors(prev => ({ ...prev, [name]: '' }));\r\n    }\r\n  };\r\n\r\n  const handleProfilePhotoChange = (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      // Validate file type\r\n      const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];\r\n      if (!validTypes.includes(file.type)) {\r\n        showErrorPopup('Please select a valid image file (JPEG, PNG, GIF, or WebP)');\r\n        return;\r\n      }\r\n\r\n      // Validate file size (max 5MB)\r\n      if (file.size > 5 * 1024 * 1024) {\r\n        showErrorPopup('Profile photo must be less than 5MB');\r\n        return;\r\n      }\r\n\r\n      setRegisterData(prev => ({ ...prev, profilePhoto: file }));\r\n\r\n      // Create preview\r\n      const reader = new FileReader();\r\n      reader.onload = (e) => {\r\n        setProfilePhotoPreview(e.target.result);\r\n      };\r\n      reader.readAsDataURL(file);\r\n\r\n    }\r\n  };\r\n\r\n  const removeProfilePhoto = () => {\r\n    setRegisterData(prev => ({ ...prev, profilePhoto: null }));\r\n    setProfilePhotoPreview('');\r\n  };\r\n\r\n  const handleClickShowPassword = () => {\r\n    setShowPassword(!showPassword);\r\n  };\r\n\r\n  const validateLoginForm = () => {\r\n    const errors = {};\r\n\r\n    if (!loginData.email) {\r\n      errors.email = 'Email is required';\r\n    } else if (!/\\S+@\\S+\\.\\S+/.test(loginData.email)) {\r\n      errors.email = 'Please enter a valid email address';\r\n    }\r\n\r\n    if (!loginData.password) {\r\n      errors.password = 'Password is required';\r\n    }\r\n\r\n    setFieldErrors(errors);\r\n\r\n    // Show popup for validation errors\r\n    if (Object.keys(errors).length > 0) {\r\n      showValidationErrorPopup(errors);\r\n    }\r\n\r\n    return Object.keys(errors).length === 0;\r\n  };\r\n\r\n  const validateRegisterForm = () => {\r\n    const errors = {};\r\n\r\n    // First name validation (2-50 characters)\r\n    if (!registerData.firstName.trim()) {\r\n      errors.firstName = 'First name is required';\r\n    } else if (registerData.firstName.trim().length < 2 || registerData.firstName.trim().length > 50) {\r\n      errors.firstName = 'First name must be between 2 and 50 characters';\r\n    }\r\n\r\n    // Last name validation (2-50 characters)\r\n    if (!registerData.lastName.trim()) {\r\n      errors.lastName = 'Last name is required';\r\n    } else if (registerData.lastName.trim().length < 2 || registerData.lastName.trim().length > 50) {\r\n      errors.lastName = 'Last name must be between 2 and 50 characters';\r\n    }\r\n\r\n    // Email validation\r\n    if (!registerData.email) {\r\n      errors.email = 'Email is required';\r\n    } else if (!/\\S+@\\S+\\.\\S+/.test(registerData.email)) {\r\n      errors.email = 'Please enter a valid email address';\r\n    } else if (registerData.email.length > 254) {\r\n      errors.email = 'Email address is too long';\r\n    }\r\n\r\n    // Enhanced password validation to match server requirements\r\n    if (!registerData.password) {\r\n      errors.password = 'Password is required';\r\n    } else {\r\n      const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$/;\r\n      if (!passwordRegex.test(registerData.password)) {\r\n        errors.password = 'Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character';\r\n      }\r\n    }\r\n\r\n    // Phone validation (enhanced)\r\n    if (registerData.phone && registerData.phone.trim()) {\r\n      const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\r\n      const cleanPhone = registerData.phone.replace(/[\\s\\-\\(\\)]/g, '');\r\n      if (!phoneRegex.test(cleanPhone)) {\r\n        errors.phone = 'Please enter a valid phone number (e.g., +1234567890)';\r\n      }\r\n    }\r\n\r\n    setFieldErrors(errors);\r\n\r\n    // Show popup for validation errors\r\n    if (Object.keys(errors).length > 0) {\r\n      showValidationErrorPopup(errors);\r\n    }\r\n\r\n    return Object.keys(errors).length === 0;\r\n  };\r\n\r\n  const handleLoginSubmit = async () => {\r\n    if (!validateLoginForm()) return;\r\n\r\n    setLoading(true);\r\n\r\n    try {\r\n      // Use the API service for consistent error handling\r\n      const responseData = await authAPI.login(loginData);\r\n\r\n      // Extract data from the standardized response format\r\n      const { data } = responseData;\r\n\r\n      if (!data || !data.token || !data.user) {\r\n        throw new Error('Invalid response format from server');\r\n      }\r\n\r\n      // Use AuthContext login method for proper authentication management\r\n      const loginSuccess = login(data.user, data.token, data.userType);\r\n\r\n      if (!loginSuccess) {\r\n        throw new Error('Failed to store authentication data');\r\n      }\r\n\r\n      showSuccessPopup(responseData.message || 'Login successful! Redirecting to your dashboard...');\r\n      setLoginData({ email: '', password: '' });\r\n\r\n      // Redirect based on user type after a short delay\r\n      setTimeout(() => {\r\n        if (data.userType === 'customer') {\r\n          window.location.href = '/menu';\r\n        } else {\r\n          // Staff or Admin (userType is 'staffOrAdmin')\r\n          window.location.href = '/admin';\r\n        }\r\n      }, 1500);\r\n\r\n    } catch (error) {\r\n      console.error('Login error:', error);\r\n      showErrorPopup(error.message || 'An error occurred during login');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleRegisterSubmit = async () => {\r\n    if (!validateRegisterForm()) return;\r\n\r\n    setLoading(true);\r\n\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append('firstName', registerData.firstName.trim());\r\n      formData.append('lastName', registerData.lastName.trim());\r\n      formData.append('email', registerData.email.toLowerCase().trim());\r\n      formData.append('password', registerData.password);\r\n\r\n      // Only append phone if it's provided and not empty\r\n      if (registerData.phone && registerData.phone.trim()) {\r\n        formData.append('phone', registerData.phone.trim());\r\n      }\r\n\r\n      if (registerData.profilePhoto) {\r\n        formData.append('profilePhoto', registerData.profilePhoto);\r\n      }\r\n\r\n      // Use the appropriate API service method\r\n      let responseData;\r\n      if (role === 'customer') {\r\n        responseData = await authAPI.registerCustomer(formData);\r\n      } else {\r\n        formData.append('role', role);\r\n        responseData = await authAPI.registerStaff(formData);\r\n      }\r\n\r\n      showSuccessPopup(responseData.message || 'Registration successful! You can now login with your new account.');\r\n      setRegisterData({\r\n        firstName: '',\r\n        lastName: '',\r\n        phone: '',\r\n        email: '',\r\n        password: '',\r\n        profilePhoto: null\r\n      });\r\n      setProfilePhotoPreview('');\r\n\r\n      // Switch to login tab after successful registration\r\n      setTimeout(() => {\r\n        setIsLogin(true);\r\n      }, 2000);\r\n\r\n    } catch (error) {\r\n      console.error('Registration error:', error);\r\n      showErrorPopup(error.message || 'An error occurred during registration');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Get role-specific colors and labels\r\n  const getRoleInfo = () => {\r\n    switch (role) {\r\n      case 'admin':\r\n        return {\r\n          color: theme.palette.error.main,\r\n          label: 'Administrator',\r\n          description: 'Full system access and management'\r\n        };\r\n      case 'staff':\r\n        return {\r\n          color: theme.palette.info.main,\r\n          label: 'Staff Member',\r\n          description: 'Order management and customer service'\r\n        };\r\n      default:\r\n        return {\r\n          color: theme.palette.primary.main,\r\n          label: 'Customer',\r\n          description: 'Browse menu and place orders'\r\n        };\r\n    }\r\n  };\r\n\r\n  const roleInfo = getRoleInfo();\r\n\r\n  return (\r\n    <Box\r\n      sx={{\r\n        minHeight: '100vh',\r\n        display: 'flex',\r\n        alignItems: 'center',\r\n        justifyContent: 'center',\r\n        background: `linear-gradient(135deg, \r\n                    ${alpha(theme.palette.primary.light, 0.15)} 0%, \r\n                    ${alpha(theme.palette.primary.main, 0.05)} 50%,\r\n                    ${theme.palette.background.default} 100%)`,\r\n        padding: { xs: 2, sm: 4 }\r\n      }}\r\n    >\r\n      <Container maxWidth=\"sm\">\r\n        <Paper\r\n          elevation={4}\r\n          sx={{\r\n            overflow: 'hidden',\r\n            borderRadius: 3,\r\n            boxShadow: '0 10px 40px rgba(0, 0, 0, 0.1)'\r\n          }}\r\n        >\r\n          {/* Header Section */}\r\n          <Box\r\n            sx={{\r\n              p: 3,\r\n              background: `linear-gradient(to right, ${roleInfo.color}, ${alpha(roleInfo.color, 0.8)})`,\r\n              color: 'white',\r\n              textAlign: 'center',\r\n              position: 'relative'\r\n            }}\r\n          >\r\n            <Avatar\r\n              sx={{\r\n                bgcolor: 'white',\r\n                width: 56,\r\n                height: 56,\r\n                margin: '0 auto 16px',\r\n                border: '2px solid white',\r\n                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'\r\n              }}\r\n            >\r\n              <Coffee fontSize=\"large\" sx={{ color: roleInfo.color }} />\r\n            </Avatar>\r\n            <Typography variant=\"h5\" component=\"h1\" fontWeight=\"bold\">\r\n              Café Management System\r\n            </Typography>\r\n            <Typography variant=\"body2\" sx={{ opacity: 0.9, mt: 1 }}>\r\n              {isLogin ? 'Sign in to your account' : 'Create a new account'}\r\n            </Typography>\r\n\r\n            {/* Role Chip */}\r\n            <Chip\r\n              label={roleInfo.label}\r\n              size=\"small\"\r\n              sx={{\r\n                mt: 2,\r\n                bgcolor: 'rgba(255, 255, 255, 0.2)',\r\n                color: 'white',\r\n                fontWeight: 'bold'\r\n              }}\r\n            />\r\n          </Box>\r\n\r\n          {/* Tabs */}\r\n          <Tabs\r\n            value={isLogin ? 'login' : 'register'}\r\n            onChange={handleTabChange}\r\n            variant=\"fullWidth\"\r\n            sx={{\r\n              '& .MuiTab-root': {\r\n                fontWeight: 600,\r\n                py: 2\r\n              },\r\n              '& .Mui-selected': {\r\n                color: roleInfo.color,\r\n              },\r\n              '& .MuiTabs-indicator': {\r\n                backgroundColor: roleInfo.color,\r\n                height: 3\r\n              }\r\n            }}\r\n          >\r\n            <Tab label=\"Login\" value=\"login\" />\r\n            <Tab label=\"Register\" value=\"register\" />\r\n          </Tabs>\r\n\r\n\r\n          {/* Form Content */}\r\n          <Box\r\n            sx={{\r\n              p: 4,\r\n              background: `linear-gradient(135deg, ${alpha(roleInfo.color, 0.05)} 0%, ${alpha(roleInfo.color, 0.02)} 100%)`\r\n            }}\r\n          >\r\n            {/* Role Selection */}\r\n            <Box sx={{ mb: 3 }}>\r\n              <FormControl fullWidth variant=\"outlined\">\r\n                <InputLabel id=\"role-select-label\">I am a</InputLabel>\r\n                <Select\r\n                  labelId=\"role-select-label\"\r\n                  value={role}\r\n                  label=\"I am a\"\r\n                  onChange={handleRoleChange}\r\n                  disabled={loading}\r\n                  sx={{\r\n                    '& .MuiOutlinedInput-notchedOutline': {\r\n                      borderColor: alpha(roleInfo.color, 0.5),\r\n                    },\r\n                    '&:hover .MuiOutlinedInput-notchedOutline': {\r\n                      borderColor: roleInfo.color,\r\n                    },\r\n                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\r\n                      borderColor: roleInfo.color,\r\n                    }\r\n                  }}\r\n                >\r\n                  <MenuItem value=\"customer\">\r\n                    <Box>\r\n                      <Typography variant=\"body1\">Customer</Typography>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">\r\n                        Browse menu and place orders\r\n                      </Typography>\r\n                    </Box>\r\n                  </MenuItem>\r\n                  <MenuItem value=\"staff\">\r\n                    <Box>\r\n                      <Typography variant=\"body1\">Staff Member</Typography>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">\r\n                        Order management and customer service\r\n                      </Typography>\r\n                    </Box>\r\n                  </MenuItem>\r\n                  <MenuItem value=\"admin\">\r\n                    <Box>\r\n                      <Typography variant=\"body1\">Administrator</Typography>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">\r\n                        Full system access and management\r\n                      </Typography>\r\n                    </Box>\r\n                  </MenuItem>\r\n                </Select>\r\n              </FormControl>\r\n            </Box>\r\n\r\n            {isLogin ? (\r\n              // Login Form\r\n              <Box>\r\n                <TextField\r\n                  fullWidth\r\n                  label=\"Email Address\"\r\n                  name=\"email\"\r\n                  type=\"email\"\r\n                  margin=\"normal\"\r\n                  variant=\"outlined\"\r\n                  value={loginData.email}\r\n                  onChange={handleLoginChange}\r\n                  disabled={loading}\r\n                  error={!!fieldErrors.email}\r\n                  helperText={fieldErrors.email}\r\n                  InputProps={{\r\n                    startAdornment: (\r\n                      <InputAdornment position=\"start\">\r\n                        <Email color=\"action\" />\r\n                      </InputAdornment>\r\n                    ),\r\n                  }}\r\n                  sx={{\r\n                    '& .MuiOutlinedInput-root': {\r\n                      '&.Mui-focused fieldset': {\r\n                        borderColor: roleInfo.color,\r\n                      }\r\n                    },\r\n                    '& .MuiInputLabel-root.Mui-focused': {\r\n                      color: roleInfo.color,\r\n                    }\r\n                  }}\r\n                />\r\n\r\n                <TextField\r\n                  fullWidth\r\n                  label=\"Password\"\r\n                  name=\"password\"\r\n                  type={showPassword ? 'text' : 'password'}\r\n                  margin=\"normal\"\r\n                  variant=\"outlined\"\r\n                  value={loginData.password}\r\n                  onChange={handleLoginChange}\r\n                  disabled={loading}\r\n                  error={!!fieldErrors.password}\r\n                  helperText={fieldErrors.password}\r\n                  InputProps={{\r\n                    startAdornment: (\r\n                      <InputAdornment position=\"start\">\r\n                        <Lock color=\"action\" />\r\n                      </InputAdornment>\r\n                    ),\r\n                    endAdornment: (\r\n                      <InputAdornment position=\"end\">\r\n                        <IconButton\r\n                          onClick={handleClickShowPassword}\r\n                          edge=\"end\"\r\n                          disabled={loading}\r\n                        >\r\n                          {showPassword ? <VisibilityOff /> : <Visibility />}\r\n                        </IconButton>\r\n                      </InputAdornment>\r\n                    )\r\n                  }}\r\n                  sx={{\r\n                    '& .MuiOutlinedInput-root': {\r\n                      '&.Mui-focused fieldset': {\r\n                        borderColor: roleInfo.color,\r\n                      }\r\n                    },\r\n                    '& .MuiInputLabel-root.Mui-focused': {\r\n                      color: roleInfo.color,\r\n                    }\r\n                  }}\r\n                />\r\n\r\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 1, mb: 2 }}>\r\n                  <Typography\r\n                    variant=\"body2\"\r\n                    sx={{\r\n                      cursor: 'pointer',\r\n                      color: roleInfo.color,\r\n                      '&:hover': { textDecoration: 'underline' }\r\n                    }}\r\n                  >\r\n                    Forgot password?\r\n                  </Typography>\r\n                </Box>\r\n\r\n                <Button\r\n                  variant=\"contained\"\r\n                  size=\"large\"\r\n                  fullWidth\r\n                  onClick={handleLoginSubmit}\r\n                  disabled={loading}\r\n                  sx={{\r\n                    py: 1.5,\r\n                    backgroundColor: roleInfo.color,\r\n                    '&:hover': {\r\n                      backgroundColor: alpha(roleInfo.color, 0.9),\r\n                    },\r\n                    '&:disabled': {\r\n                      backgroundColor: alpha(roleInfo.color, 0.5),\r\n                    },\r\n                    boxShadow: `0 4px 12px ${alpha(roleInfo.color, 0.3)}`\r\n                  }}\r\n                  endIcon={loading ? <CircularProgress size={20} color=\"inherit\" /> : <ArrowForward />}\r\n                >\r\n                  {loading ? 'Signing In...' : 'Sign In'}\r\n                </Button>\r\n              </Box>\r\n            ) : (\r\n              // Register Form\r\n              <Box>\r\n                {/* Profile Photo Upload */}\r\n                <Box sx={{ mb: 3, textAlign: 'center' }}>\r\n                  <input\r\n                    accept=\"image/*\"\r\n                    style={{ display: 'none' }}\r\n                    id=\"profile-photo-upload\"\r\n                    type=\"file\"\r\n                    onChange={handleProfilePhotoChange}\r\n                    disabled={loading}\r\n                  />\r\n                  <label htmlFor=\"profile-photo-upload\">\r\n                    <Box\r\n                      sx={{\r\n                        position: 'relative',\r\n                        display: 'inline-block',\r\n                        cursor: loading ? 'not-allowed' : 'pointer'\r\n                      }}\r\n                    >\r\n                      <Avatar\r\n                        src={profilePhotoPreview}\r\n                        sx={{\r\n                          width: 80,\r\n                          height: 80,\r\n                          border: `2px dashed ${alpha(roleInfo.color, 0.5)}`,\r\n                          backgroundColor: alpha(roleInfo.color, 0.1),\r\n                          '&:hover': {\r\n                            backgroundColor: alpha(roleInfo.color, 0.2),\r\n                          }\r\n                        }}\r\n                      >\r\n                        <PhotoCamera sx={{ color: roleInfo.color }} />\r\n                      </Avatar>\r\n                      {profilePhotoPreview && (\r\n                        <IconButton\r\n                          size=\"small\"\r\n                          onClick={(e) => {\r\n                            e.preventDefault();\r\n                            removeProfilePhoto();\r\n                          }}\r\n                          sx={{\r\n                            position: 'absolute',\r\n                            top: -5,\r\n                            right: -5,\r\n                            backgroundColor: 'error.main',\r\n                            color: 'white',\r\n                            '&:hover': {\r\n                              backgroundColor: 'error.dark',\r\n                            }\r\n                          }}\r\n                        >\r\n                          <Close fontSize=\"small\" />\r\n                        </IconButton>\r\n                      )}\r\n                    </Box>\r\n                  </label>\r\n                  <Typography variant=\"caption\" display=\"block\" sx={{ mt: 1, color: 'text.secondary' }}>\r\n                    {profilePhotoPreview ? 'Click to change photo' : 'Click to add profile photo (optional)'}\r\n                  </Typography>\r\n                </Box>\r\n\r\n                <Grid2 container spacing={2}>\r\n                  <Grid2 xs={12} sm={6}>\r\n                    <TextField\r\n                      fullWidth\r\n                      label=\"First Name\"\r\n                      name=\"firstName\"\r\n                      margin=\"normal\"\r\n                      variant=\"outlined\"\r\n                      value={registerData.firstName}\r\n                      onChange={handleRegisterChange}\r\n                      disabled={loading}\r\n                      error={!!fieldErrors.firstName}\r\n                      helperText={fieldErrors.firstName}\r\n                      InputProps={{\r\n                        startAdornment: (\r\n                          <InputAdornment position=\"start\">\r\n                            <Person color=\"action\" />\r\n                          </InputAdornment>\r\n                        ),\r\n                      }}\r\n                      sx={{\r\n                        '& .MuiOutlinedInput-root': {\r\n                          '&.Mui-focused fieldset': {\r\n                            borderColor: roleInfo.color,\r\n                          }\r\n                        },\r\n                        '& .MuiInputLabel-root.Mui-focused': {\r\n                          color: roleInfo.color,\r\n                        }\r\n                      }}\r\n                    />\r\n                  </Grid2>\r\n                  <Grid2 xs={12} sm={6}>\r\n                    <TextField\r\n                      fullWidth\r\n                      label=\"Last Name\"\r\n                      name=\"lastName\"\r\n                      margin=\"normal\"\r\n                      variant=\"outlined\"\r\n                      value={registerData.lastName}\r\n                      onChange={handleRegisterChange}\r\n                      disabled={loading}\r\n                      error={!!fieldErrors.lastName}\r\n                      helperText={fieldErrors.lastName}\r\n                      sx={{\r\n                        '& .MuiOutlinedInput-root': {\r\n                          '&.Mui-focused fieldset': {\r\n                            borderColor: roleInfo.color,\r\n                          }\r\n                        },\r\n                        '& .MuiInputLabel-root.Mui-focused': {\r\n                          color: roleInfo.color,\r\n                        }\r\n                      }}\r\n                    />\r\n                  </Grid2>\r\n                </Grid2>\r\n\r\n                <TextField\r\n                  fullWidth\r\n                  label=\"Phone Number (Optional)\"\r\n                  name=\"phone\"\r\n                  margin=\"normal\"\r\n                  variant=\"outlined\"\r\n                  value={registerData.phone}\r\n                  onChange={handleRegisterChange}\r\n                  disabled={loading}\r\n                  error={!!fieldErrors.phone}\r\n                  helperText={fieldErrors.phone || 'e.g., +1234567890 or (*************'}\r\n                  InputProps={{\r\n                    startAdornment: (\r\n                      <InputAdornment position=\"start\">\r\n                        <Phone color=\"action\" />\r\n                      </InputAdornment>\r\n                    ),\r\n                  }}\r\n                  sx={{\r\n                    '& .MuiOutlinedInput-root': {\r\n                      '&.Mui-focused fieldset': {\r\n                        borderColor: roleInfo.color,\r\n                      }\r\n                    },\r\n                    '& .MuiInputLabel-root.Mui-focused': {\r\n                      color: roleInfo.color,\r\n                    }\r\n                  }}\r\n                />\r\n\r\n                <TextField\r\n                  fullWidth\r\n                  label=\"Email Address\"\r\n                  name=\"email\"\r\n                  type=\"email\"\r\n                  margin=\"normal\"\r\n                  variant=\"outlined\"\r\n                  value={registerData.email}\r\n                  onChange={handleRegisterChange}\r\n                  disabled={loading}\r\n                  error={!!fieldErrors.email}\r\n                  helperText={fieldErrors.email}\r\n                  InputProps={{\r\n                    startAdornment: (\r\n                      <InputAdornment position=\"start\">\r\n                        <Email color=\"action\" />\r\n                      </InputAdornment>\r\n                    ),\r\n                  }}\r\n                  sx={{\r\n                    '& .MuiOutlinedInput-root': {\r\n                      '&.Mui-focused fieldset': {\r\n                        borderColor: roleInfo.color,\r\n                      }\r\n                    },\r\n                    '& .MuiInputLabel-root.Mui-focused': {\r\n                      color: roleInfo.color,\r\n                    }\r\n                  }}\r\n                />\r\n\r\n                <TextField\r\n                  fullWidth\r\n                  label=\"Password\"\r\n                  name=\"password\"\r\n                  type={showPassword ? 'text' : 'password'}\r\n                  margin=\"normal\"\r\n                  variant=\"outlined\"\r\n                  value={registerData.password}\r\n                  onChange={handleRegisterChange}\r\n                  disabled={loading}\r\n                  error={!!fieldErrors.password}\r\n                  helperText={fieldErrors.password || 'Minimum 8 characters with uppercase, lowercase, number, and special character'}\r\n                  InputProps={{\r\n                    startAdornment: (\r\n                      <InputAdornment position=\"start\">\r\n                        <Lock color=\"action\" />\r\n                      </InputAdornment>\r\n                    ),\r\n                    endAdornment: (\r\n                      <InputAdornment position=\"end\">\r\n                        <IconButton\r\n                          onClick={handleClickShowPassword}\r\n                          edge=\"end\"\r\n                          disabled={loading}\r\n                        >\r\n                          {showPassword ? <VisibilityOff /> : <Visibility />}\r\n                        </IconButton>\r\n                      </InputAdornment>\r\n                    )\r\n                  }}\r\n                  sx={{\r\n                    '& .MuiOutlinedInput-root': {\r\n                      '&.Mui-focused fieldset': {\r\n                        borderColor: roleInfo.color,\r\n                      }\r\n                    },\r\n                    '& .MuiInputLabel-root.Mui-focused': {\r\n                      color: roleInfo.color,\r\n                    }\r\n                  }}\r\n                />\r\n\r\n                <Button\r\n                  variant=\"contained\"\r\n                  size=\"large\"\r\n                  fullWidth\r\n                  sx={{\r\n                    mt: 3,\r\n                    py: 1.5,\r\n                    backgroundColor: roleInfo.color,\r\n                    '&:hover': {\r\n                      backgroundColor: alpha(roleInfo.color, 0.9),\r\n                    },\r\n                    '&:disabled': {\r\n                      backgroundColor: alpha(roleInfo.color, 0.5),\r\n                    },\r\n                    boxShadow: `0 4px 12px ${alpha(roleInfo.color, 0.3)}`\r\n                  }}\r\n                  onClick={handleRegisterSubmit}\r\n                  disabled={loading}\r\n                  endIcon={loading ? <CircularProgress size={20} color=\"inherit\" /> : <ArrowForward />}\r\n                >\r\n                  {loading ? 'Creating Account...' : 'Create Account'}\r\n                </Button>\r\n              </Box>\r\n            )}\r\n          </Box>\r\n\r\n          {/* Footer */}\r\n          <Box\r\n            sx={{\r\n              p: 3,\r\n              borderTop: `1px solid ${theme.palette.divider}`,\r\n              backgroundColor: alpha(theme.palette.background.paper, 0.5),\r\n              textAlign: 'center'\r\n            }}\r\n          >\r\n            <Typography variant=\"body2\" color=\"text.secondary\">\r\n              {isLogin ? \"Don't have an account?\" : \"Already have an account?\"}\r\n              <Button\r\n                sx={{\r\n                  ml: 1,\r\n                  color: roleInfo.color,\r\n                  fontWeight: 'bold',\r\n                }}\r\n                onClick={() => setIsLogin(!isLogin)}\r\n                disabled={loading}\r\n              >\r\n                {isLogin ? \"Sign Up\" : \"Sign In\"}\r\n              </Button>\r\n            </Typography>\r\n          </Box>\r\n        </Paper>\r\n      </Container>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default LoginRegisterPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,SAAS,EACTC,UAAU,EACVC,MAAM,EACNC,SAAS,EACTC,GAAG,EACHC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,IAAI,EACJC,GAAG,EACHC,KAAK,EACLC,cAAc,EACdC,UAAU,EACVC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,gBAAgB,EAChBC,IAAI,QACC,eAAe;AACtB,OAAOC,IAAI,MAAM,aAAa;AAC9B,SACEC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,aAAa,EACbC,MAAM,EACNC,KAAK,EACLC,YAAY,EACZC,WAAW,QACN,qBAAqB;AAE5B,OAAOC,KAAK,MAAM,8BAA8B;AAChD,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,OAAO,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,KAAK,GAAGvB,QAAQ,CAAC,CAAC;EACxB,MAAM;IAAEwB;EAAM,CAAC,GAAGN,OAAO,CAAC,CAAC;EAC3B,MAAM,CAACO,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0C,IAAI,EAAEC,OAAO,CAAC,GAAG3C,QAAQ,CAAC,UAAU,CAAC;EAC5C,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAM,CAACgD,SAAS,EAAEC,YAAY,CAAC,GAAGjD,QAAQ,CAAC;IACzCkD,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAAC;IAC/CsD,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTN,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZM,YAAY,EAAE;EAChB,CAAC,CAAC;EAEF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG3D,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM,CAAC4D,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAElE,MAAM8D,gBAAgB,GAAIC,OAAO,IAAK;IACpC3C,IAAI,CAAC4C,IAAI,CAAC;MACRC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,UAAU;MACjBC,IAAI,EAAEJ,OAAO;MACbK,kBAAkB,EAAEC,QAAQ,CAACC,KAAK;MAClCC,KAAK,EAAE,IAAI;MACXC,iBAAiB,EAAE,KAAK;MACxBC,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE,SAAS;MACnBC,SAAS,EAAE;QACTC,KAAK,EAAE;MACT,CAAC;MACDC,SAAS,EAAE;QACTD,KAAK,EAAE;MACT;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,cAAc,GAAIf,OAAO,IAAK;IAClC3C,IAAI,CAAC4C,IAAI,CAAC;MACRC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAEJ,OAAO;MACbK,kBAAkB,EAAEC,QAAQ,CAACC,KAAK;MAClCS,iBAAiB,EAAE,WAAW;MAC9BJ,SAAS,EAAE;QACTC,KAAK,EAAE;MACT;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMI,wBAAwB,GAAIC,MAAM,IAAK;IAC3C,MAAMC,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACH,MAAM,CAAC,CAACI,IAAI,CAAC,MAAM,CAAC;IACpDjE,IAAI,CAAC4C,IAAI,CAAC;MACRC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,yBAAyB;MAChCoB,IAAI,EAAE,oCAAoCH,MAAM,CAACC,MAAM,CAACH,MAAM,CAAC,CAACI,IAAI,CAAC,QAAQ,CAAC,QAAQ;MACtFjB,kBAAkB,EAAEC,QAAQ,CAACC,KAAK;MAClCS,iBAAiB,EAAE;IACrB,CAAC,CAAC;EACJ,CAAC;EACD,MAAMQ,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3ChD,UAAU,CAACgD,QAAQ,KAAK,OAAO,CAAC;IAChC9B,cAAc,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC;EAED,MAAM+B,gBAAgB,GAAIF,KAAK,IAAK;IAClC7C,OAAO,CAAC6C,KAAK,CAACG,MAAM,CAACC,KAAK,CAAC;IAC3BjC,cAAc,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC;EAED,MAAMkC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEH;IAAM,CAAC,GAAGE,CAAC,CAACH,MAAM;IAChC1C,YAAY,CAAE+C,QAAQ,KAAM;MAAE,GAAGA,QAAQ;MAAE,CAACD,IAAI,GAAGH;IAAM,CAAC,CAAC,CAAC;;IAE5D;IACA,IAAIlC,WAAW,CAACqC,IAAI,CAAC,EAAE;MACrBpC,cAAc,CAACsC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACF,IAAI,GAAG;MAAG,CAAC,CAAC,CAAC;IACnD;EACF,CAAC;EAED,MAAMG,oBAAoB,GAAIJ,CAAC,IAAK;IAClC,MAAM;MAAEC,IAAI;MAAEH;IAAM,CAAC,GAAGE,CAAC,CAACH,MAAM;IAChCtC,eAAe,CAAE2C,QAAQ,KAAM;MAAE,GAAGA,QAAQ;MAAE,CAACD,IAAI,GAAGH;IAAM,CAAC,CAAC,CAAC;;IAE/D;IACA,IAAIlC,WAAW,CAACqC,IAAI,CAAC,EAAE;MACrBpC,cAAc,CAACsC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACF,IAAI,GAAG;MAAG,CAAC,CAAC,CAAC;IACnD;EACF,CAAC;EAED,MAAMI,wBAAwB,GAAIL,CAAC,IAAK;IACtC,MAAMM,IAAI,GAAGN,CAAC,CAACH,MAAM,CAACU,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACR;MACA,MAAME,UAAU,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;MACzE,IAAI,CAACA,UAAU,CAACC,QAAQ,CAACH,IAAI,CAACI,IAAI,CAAC,EAAE;QACnC1B,cAAc,CAAC,4DAA4D,CAAC;QAC5E;MACF;;MAEA;MACA,IAAIsB,IAAI,CAACK,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;QAC/B3B,cAAc,CAAC,qCAAqC,CAAC;QACrD;MACF;MAEAzB,eAAe,CAAC4C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAExC,YAAY,EAAE2C;MAAK,CAAC,CAAC,CAAC;;MAE1D;MACA,MAAMM,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAId,CAAC,IAAK;QACrBjC,sBAAsB,CAACiC,CAAC,CAACH,MAAM,CAACkB,MAAM,CAAC;MACzC,CAAC;MACDH,MAAM,CAACI,aAAa,CAACV,IAAI,CAAC;IAE5B;EACF,CAAC;EAED,MAAMW,kBAAkB,GAAGA,CAAA,KAAM;IAC/B1D,eAAe,CAAC4C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAExC,YAAY,EAAE;IAAK,CAAC,CAAC,CAAC;IAC1DI,sBAAsB,CAAC,EAAE,CAAC;EAC5B,CAAC;EAED,MAAMmD,uBAAuB,GAAGA,CAAA,KAAM;IACpCnE,eAAe,CAAC,CAACD,YAAY,CAAC;EAChC,CAAC;EAED,MAAMqE,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMhC,MAAM,GAAG,CAAC,CAAC;IAEjB,IAAI,CAACjC,SAAS,CAACE,KAAK,EAAE;MACpB+B,MAAM,CAAC/B,KAAK,GAAG,mBAAmB;IACpC,CAAC,MAAM,IAAI,CAAC,cAAc,CAACgE,IAAI,CAAClE,SAAS,CAACE,KAAK,CAAC,EAAE;MAChD+B,MAAM,CAAC/B,KAAK,GAAG,oCAAoC;IACrD;IAEA,IAAI,CAACF,SAAS,CAACG,QAAQ,EAAE;MACvB8B,MAAM,CAAC9B,QAAQ,GAAG,sBAAsB;IAC1C;IAEAQ,cAAc,CAACsB,MAAM,CAAC;;IAEtB;IACA,IAAIE,MAAM,CAACgC,IAAI,CAAClC,MAAM,CAAC,CAACmC,MAAM,GAAG,CAAC,EAAE;MAClCpC,wBAAwB,CAACC,MAAM,CAAC;IAClC;IAEA,OAAOE,MAAM,CAACgC,IAAI,CAAClC,MAAM,CAAC,CAACmC,MAAM,KAAK,CAAC;EACzC,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMpC,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAAC7B,YAAY,CAACE,SAAS,CAACgE,IAAI,CAAC,CAAC,EAAE;MAClCrC,MAAM,CAAC3B,SAAS,GAAG,wBAAwB;IAC7C,CAAC,MAAM,IAAIF,YAAY,CAACE,SAAS,CAACgE,IAAI,CAAC,CAAC,CAACF,MAAM,GAAG,CAAC,IAAIhE,YAAY,CAACE,SAAS,CAACgE,IAAI,CAAC,CAAC,CAACF,MAAM,GAAG,EAAE,EAAE;MAChGnC,MAAM,CAAC3B,SAAS,GAAG,gDAAgD;IACrE;;IAEA;IACA,IAAI,CAACF,YAAY,CAACG,QAAQ,CAAC+D,IAAI,CAAC,CAAC,EAAE;MACjCrC,MAAM,CAAC1B,QAAQ,GAAG,uBAAuB;IAC3C,CAAC,MAAM,IAAIH,YAAY,CAACG,QAAQ,CAAC+D,IAAI,CAAC,CAAC,CAACF,MAAM,GAAG,CAAC,IAAIhE,YAAY,CAACG,QAAQ,CAAC+D,IAAI,CAAC,CAAC,CAACF,MAAM,GAAG,EAAE,EAAE;MAC9FnC,MAAM,CAAC1B,QAAQ,GAAG,+CAA+C;IACnE;;IAEA;IACA,IAAI,CAACH,YAAY,CAACF,KAAK,EAAE;MACvB+B,MAAM,CAAC/B,KAAK,GAAG,mBAAmB;IACpC,CAAC,MAAM,IAAI,CAAC,cAAc,CAACgE,IAAI,CAAC9D,YAAY,CAACF,KAAK,CAAC,EAAE;MACnD+B,MAAM,CAAC/B,KAAK,GAAG,oCAAoC;IACrD,CAAC,MAAM,IAAIE,YAAY,CAACF,KAAK,CAACkE,MAAM,GAAG,GAAG,EAAE;MAC1CnC,MAAM,CAAC/B,KAAK,GAAG,2BAA2B;IAC5C;;IAEA;IACA,IAAI,CAACE,YAAY,CAACD,QAAQ,EAAE;MAC1B8B,MAAM,CAAC9B,QAAQ,GAAG,sBAAsB;IAC1C,CAAC,MAAM;MACL,MAAMoE,aAAa,GAAG,sEAAsE;MAC5F,IAAI,CAACA,aAAa,CAACL,IAAI,CAAC9D,YAAY,CAACD,QAAQ,CAAC,EAAE;QAC9C8B,MAAM,CAAC9B,QAAQ,GAAG,6GAA6G;MACjI;IACF;;IAEA;IACA,IAAIC,YAAY,CAACI,KAAK,IAAIJ,YAAY,CAACI,KAAK,CAAC8D,IAAI,CAAC,CAAC,EAAE;MACnD,MAAME,UAAU,GAAG,wBAAwB;MAC3C,MAAMC,UAAU,GAAGrE,YAAY,CAACI,KAAK,CAACkE,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;MAChE,IAAI,CAACF,UAAU,CAACN,IAAI,CAACO,UAAU,CAAC,EAAE;QAChCxC,MAAM,CAACzB,KAAK,GAAG,uDAAuD;MACxE;IACF;IAEAG,cAAc,CAACsB,MAAM,CAAC;;IAEtB;IACA,IAAIE,MAAM,CAACgC,IAAI,CAAClC,MAAM,CAAC,CAACmC,MAAM,GAAG,CAAC,EAAE;MAClCpC,wBAAwB,CAACC,MAAM,CAAC;IAClC;IAEA,OAAOE,MAAM,CAACgC,IAAI,CAAClC,MAAM,CAAC,CAACmC,MAAM,KAAK,CAAC;EACzC,CAAC;EAED,MAAMO,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACV,iBAAiB,CAAC,CAAC,EAAE;IAE1BlE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF;MACA,MAAM6E,YAAY,GAAG,MAAM5F,OAAO,CAACO,KAAK,CAACS,SAAS,CAAC;;MAEnD;MACA,MAAM;QAAE6E;MAAK,CAAC,GAAGD,YAAY;MAE7B,IAAI,CAACC,IAAI,IAAI,CAACA,IAAI,CAACC,KAAK,IAAI,CAACD,IAAI,CAACE,IAAI,EAAE;QACtC,MAAM,IAAIC,KAAK,CAAC,qCAAqC,CAAC;MACxD;;MAEA;MACA,MAAMC,YAAY,GAAG1F,KAAK,CAACsF,IAAI,CAACE,IAAI,EAAEF,IAAI,CAACC,KAAK,EAAED,IAAI,CAACK,QAAQ,CAAC;MAEhE,IAAI,CAACD,YAAY,EAAE;QACjB,MAAM,IAAID,KAAK,CAAC,qCAAqC,CAAC;MACxD;MAEAlE,gBAAgB,CAAC8D,YAAY,CAAC7D,OAAO,IAAI,oDAAoD,CAAC;MAC9Fd,YAAY,CAAC;QAAEC,KAAK,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG,CAAC,CAAC;;MAEzC;MACAgF,UAAU,CAAC,MAAM;QACf,IAAIN,IAAI,CAACK,QAAQ,KAAK,UAAU,EAAE;UAChCE,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,OAAO;QAChC,CAAC,MAAM;UACL;UACAF,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;QACjC;MACF,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpCzD,cAAc,CAACyD,KAAK,CAACxE,OAAO,IAAI,gCAAgC,CAAC;IACnE,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0F,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACpB,oBAAoB,CAAC,CAAC,EAAE;IAE7BtE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAM2F,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAExF,YAAY,CAACE,SAAS,CAACgE,IAAI,CAAC,CAAC,CAAC;MAC3DoB,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAExF,YAAY,CAACG,QAAQ,CAAC+D,IAAI,CAAC,CAAC,CAAC;MACzDoB,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAExF,YAAY,CAACF,KAAK,CAAC2F,WAAW,CAAC,CAAC,CAACvB,IAAI,CAAC,CAAC,CAAC;MACjEoB,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAExF,YAAY,CAACD,QAAQ,CAAC;;MAElD;MACA,IAAIC,YAAY,CAACI,KAAK,IAAIJ,YAAY,CAACI,KAAK,CAAC8D,IAAI,CAAC,CAAC,EAAE;QACnDoB,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAExF,YAAY,CAACI,KAAK,CAAC8D,IAAI,CAAC,CAAC,CAAC;MACrD;MAEA,IAAIlE,YAAY,CAACK,YAAY,EAAE;QAC7BiF,QAAQ,CAACE,MAAM,CAAC,cAAc,EAAExF,YAAY,CAACK,YAAY,CAAC;MAC5D;;MAEA;MACA,IAAImE,YAAY;MAChB,IAAIlF,IAAI,KAAK,UAAU,EAAE;QACvBkF,YAAY,GAAG,MAAM5F,OAAO,CAAC8G,gBAAgB,CAACJ,QAAQ,CAAC;MACzD,CAAC,MAAM;QACLA,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAElG,IAAI,CAAC;QAC7BkF,YAAY,GAAG,MAAM5F,OAAO,CAAC+G,aAAa,CAACL,QAAQ,CAAC;MACtD;MAEA5E,gBAAgB,CAAC8D,YAAY,CAAC7D,OAAO,IAAI,mEAAmE,CAAC;MAC7GV,eAAe,CAAC;QACdC,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,EAAE;QACTN,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZM,YAAY,EAAE;MAChB,CAAC,CAAC;MACFI,sBAAsB,CAAC,EAAE,CAAC;;MAE1B;MACAsE,UAAU,CAAC,MAAM;QACf1F,UAAU,CAAC,IAAI,CAAC;MAClB,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAO8F,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CzD,cAAc,CAACyD,KAAK,CAACxE,OAAO,IAAI,uCAAuC,CAAC;IAC1E,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMiG,WAAW,GAAGA,CAAA,KAAM;IACxB,QAAQtG,IAAI;MACV,KAAK,OAAO;QACV,OAAO;UACL4B,KAAK,EAAEhC,KAAK,CAAC2G,OAAO,CAACV,KAAK,CAACW,IAAI;UAC/BC,KAAK,EAAE,eAAe;UACtBC,WAAW,EAAE;QACf,CAAC;MACH,KAAK,OAAO;QACV,OAAO;UACL9E,KAAK,EAAEhC,KAAK,CAAC2G,OAAO,CAACI,IAAI,CAACH,IAAI;UAC9BC,KAAK,EAAE,cAAc;UACrBC,WAAW,EAAE;QACf,CAAC;MACH;QACE,OAAO;UACL9E,KAAK,EAAEhC,KAAK,CAAC2G,OAAO,CAACK,OAAO,CAACJ,IAAI;UACjCC,KAAK,EAAE,UAAU;UACjBC,WAAW,EAAE;QACf,CAAC;IACL;EACF,CAAC;EAED,MAAM/E,QAAQ,GAAG2E,WAAW,CAAC,CAAC;EAE9B,oBACE7G,OAAA,CAAC9B,GAAG;IACFkJ,EAAE,EAAE;MACFC,SAAS,EAAE,OAAO;MAClBC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE;AACpB,sBAAsB5I,KAAK,CAACsB,KAAK,CAAC2G,OAAO,CAACK,OAAO,CAACO,KAAK,EAAE,IAAI,CAAC;AAC9D,sBAAsB7I,KAAK,CAACsB,KAAK,CAAC2G,OAAO,CAACK,OAAO,CAACJ,IAAI,EAAE,IAAI,CAAC;AAC7D,sBAAsB5G,KAAK,CAAC2G,OAAO,CAACW,UAAU,CAACE,OAAO,QAAQ;MACtDC,OAAO,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE;IAC1B,CAAE;IAAAC,QAAA,eAEF/H,OAAA,CAAClC,SAAS;MAACkK,QAAQ,EAAC,IAAI;MAAAD,QAAA,eACtB/H,OAAA,CAACvB,KAAK;QACJwJ,SAAS,EAAE,CAAE;QACbb,EAAE,EAAE;UACFc,QAAQ,EAAE,QAAQ;UAClBC,YAAY,EAAE,CAAC;UACfC,SAAS,EAAE;QACb,CAAE;QAAAL,QAAA,gBAGF/H,OAAA,CAAC9B,GAAG;UACFkJ,EAAE,EAAE;YACFiB,CAAC,EAAE,CAAC;YACJZ,UAAU,EAAE,6BAA6BvF,QAAQ,CAACC,KAAK,KAAKtD,KAAK,CAACqD,QAAQ,CAACC,KAAK,EAAE,GAAG,CAAC,GAAG;YACzFA,KAAK,EAAE,OAAO;YACdmG,SAAS,EAAE,QAAQ;YACnB/F,QAAQ,EAAE;UACZ,CAAE;UAAAwF,QAAA,gBAEF/H,OAAA,CAAClB,MAAM;YACLsI,EAAE,EAAE;cACFmB,OAAO,EAAE,OAAO;cAChBC,KAAK,EAAE,EAAE;cACTC,MAAM,EAAE,EAAE;cACVC,MAAM,EAAE,aAAa;cACrBC,MAAM,EAAE,iBAAiB;cACzBP,SAAS,EAAE;YACb,CAAE;YAAAL,QAAA,eAEF/H,OAAA,CAACR,MAAM;cAACoJ,QAAQ,EAAC,OAAO;cAACxB,EAAE,EAAE;gBAAEjF,KAAK,EAAED,QAAQ,CAACC;cAAM;YAAE;cAAA0G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACThJ,OAAA,CAACjC,UAAU;YAACkL,OAAO,EAAC,IAAI;YAACC,SAAS,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAAApB,QAAA,EAAC;UAE1D;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbhJ,OAAA,CAACjC,UAAU;YAACkL,OAAO,EAAC,OAAO;YAAC7B,EAAE,EAAE;cAAEgC,OAAO,EAAE,GAAG;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAtB,QAAA,EACrD1H,OAAO,GAAG,yBAAyB,GAAG;UAAsB;YAAAwI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eAGbhJ,OAAA,CAAChB,IAAI;YACHgI,KAAK,EAAE9E,QAAQ,CAAC8E,KAAM;YACtB1C,IAAI,EAAC,OAAO;YACZ8C,EAAE,EAAE;cACFiC,EAAE,EAAE,CAAC;cACLd,OAAO,EAAE,0BAA0B;cACnCpG,KAAK,EAAE,OAAO;cACdgH,UAAU,EAAE;YACd;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNhJ,OAAA,CAACzB,IAAI;UACHkF,KAAK,EAAEpD,OAAO,GAAG,OAAO,GAAG,UAAW;UACtCiJ,QAAQ,EAAElG,eAAgB;UAC1B6F,OAAO,EAAC,WAAW;UACnB7B,EAAE,EAAE;YACF,gBAAgB,EAAE;cAChB+B,UAAU,EAAE,GAAG;cACfI,EAAE,EAAE;YACN,CAAC;YACD,iBAAiB,EAAE;cACjBpH,KAAK,EAAED,QAAQ,CAACC;YAClB,CAAC;YACD,sBAAsB,EAAE;cACtBqH,eAAe,EAAEtH,QAAQ,CAACC,KAAK;cAC/BsG,MAAM,EAAE;YACV;UACF,CAAE;UAAAV,QAAA,gBAEF/H,OAAA,CAACxB,GAAG;YAACwI,KAAK,EAAC,OAAO;YAACvD,KAAK,EAAC;UAAO;YAAAoF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnChJ,OAAA,CAACxB,GAAG;YAACwI,KAAK,EAAC,UAAU;YAACvD,KAAK,EAAC;UAAU;YAAAoF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eAIPhJ,OAAA,CAAC9B,GAAG;UACFkJ,EAAE,EAAE;YACFiB,CAAC,EAAE,CAAC;YACJZ,UAAU,EAAE,2BAA2B5I,KAAK,CAACqD,QAAQ,CAACC,KAAK,EAAE,IAAI,CAAC,QAAQtD,KAAK,CAACqD,QAAQ,CAACC,KAAK,EAAE,IAAI,CAAC;UACvG,CAAE;UAAA4F,QAAA,gBAGF/H,OAAA,CAAC9B,GAAG;YAACkJ,EAAE,EAAE;cAAEqC,EAAE,EAAE;YAAE,CAAE;YAAA1B,QAAA,eACjB/H,OAAA,CAAC3B,WAAW;cAACqL,SAAS;cAACT,OAAO,EAAC,UAAU;cAAAlB,QAAA,gBACvC/H,OAAA,CAAC1B,UAAU;gBAACqL,EAAE,EAAC,mBAAmB;gBAAA5B,QAAA,EAAC;cAAM;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACtDhJ,OAAA,CAAC7B,MAAM;gBACLyL,OAAO,EAAC,mBAAmB;gBAC3BnG,KAAK,EAAElD,IAAK;gBACZyG,KAAK,EAAC,QAAQ;gBACdsC,QAAQ,EAAE/F,gBAAiB;gBAC3BsG,QAAQ,EAAElJ,OAAQ;gBAClByG,EAAE,EAAE;kBACF,oCAAoC,EAAE;oBACpC0C,WAAW,EAAEjL,KAAK,CAACqD,QAAQ,CAACC,KAAK,EAAE,GAAG;kBACxC,CAAC;kBACD,0CAA0C,EAAE;oBAC1C2H,WAAW,EAAE5H,QAAQ,CAACC;kBACxB,CAAC;kBACD,gDAAgD,EAAE;oBAChD2H,WAAW,EAAE5H,QAAQ,CAACC;kBACxB;gBACF,CAAE;gBAAA4F,QAAA,gBAEF/H,OAAA,CAAC5B,QAAQ;kBAACqF,KAAK,EAAC,UAAU;kBAAAsE,QAAA,eACxB/H,OAAA,CAAC9B,GAAG;oBAAA6J,QAAA,gBACF/H,OAAA,CAACjC,UAAU;sBAACkL,OAAO,EAAC,OAAO;sBAAAlB,QAAA,EAAC;oBAAQ;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACjDhJ,OAAA,CAACjC,UAAU;sBAACkL,OAAO,EAAC,SAAS;sBAAC9G,KAAK,EAAC,gBAAgB;sBAAA4F,QAAA,EAAC;oBAErD;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACXhJ,OAAA,CAAC5B,QAAQ;kBAACqF,KAAK,EAAC,OAAO;kBAAAsE,QAAA,eACrB/H,OAAA,CAAC9B,GAAG;oBAAA6J,QAAA,gBACF/H,OAAA,CAACjC,UAAU;sBAACkL,OAAO,EAAC,OAAO;sBAAAlB,QAAA,EAAC;oBAAY;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACrDhJ,OAAA,CAACjC,UAAU;sBAACkL,OAAO,EAAC,SAAS;sBAAC9G,KAAK,EAAC,gBAAgB;sBAAA4F,QAAA,EAAC;oBAErD;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACXhJ,OAAA,CAAC5B,QAAQ;kBAACqF,KAAK,EAAC,OAAO;kBAAAsE,QAAA,eACrB/H,OAAA,CAAC9B,GAAG;oBAAA6J,QAAA,gBACF/H,OAAA,CAACjC,UAAU;sBAACkL,OAAO,EAAC,OAAO;sBAAAlB,QAAA,EAAC;oBAAa;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACtDhJ,OAAA,CAACjC,UAAU;sBAACkL,OAAO,EAAC,SAAS;sBAAC9G,KAAK,EAAC,gBAAgB;sBAAA4F,QAAA,EAAC;oBAErD;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,EAEL3I,OAAO;UAAA;UACN;UACAL,OAAA,CAAC9B,GAAG;YAAA6J,QAAA,gBACF/H,OAAA,CAAC/B,SAAS;cACRyL,SAAS;cACT1C,KAAK,EAAC,eAAe;cACrBpD,IAAI,EAAC,OAAO;cACZS,IAAI,EAAC,OAAO;cACZqE,MAAM,EAAC,QAAQ;cACfO,OAAO,EAAC,UAAU;cAClBxF,KAAK,EAAE5C,SAAS,CAACE,KAAM;cACvBuI,QAAQ,EAAE5F,iBAAkB;cAC5BmG,QAAQ,EAAElJ,OAAQ;cAClByF,KAAK,EAAE,CAAC,CAAC7E,WAAW,CAACR,KAAM;cAC3BgJ,UAAU,EAAExI,WAAW,CAACR,KAAM;cAC9BiJ,UAAU,EAAE;gBACVC,cAAc,eACZjK,OAAA,CAACtB,cAAc;kBAAC6D,QAAQ,EAAC,OAAO;kBAAAwF,QAAA,eAC9B/H,OAAA,CAACd,KAAK;oBAACiD,KAAK,EAAC;kBAAQ;oBAAA0G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAEpB,CAAE;cACF5B,EAAE,EAAE;gBACF,0BAA0B,EAAE;kBAC1B,wBAAwB,EAAE;oBACxB0C,WAAW,EAAE5H,QAAQ,CAACC;kBACxB;gBACF,CAAC;gBACD,mCAAmC,EAAE;kBACnCA,KAAK,EAAED,QAAQ,CAACC;gBAClB;cACF;YAAE;cAAA0G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEFhJ,OAAA,CAAC/B,SAAS;cACRyL,SAAS;cACT1C,KAAK,EAAC,UAAU;cAChBpD,IAAI,EAAC,UAAU;cACfS,IAAI,EAAE5D,YAAY,GAAG,MAAM,GAAG,UAAW;cACzCiI,MAAM,EAAC,QAAQ;cACfO,OAAO,EAAC,UAAU;cAClBxF,KAAK,EAAE5C,SAAS,CAACG,QAAS;cAC1BsI,QAAQ,EAAE5F,iBAAkB;cAC5BmG,QAAQ,EAAElJ,OAAQ;cAClByF,KAAK,EAAE,CAAC,CAAC7E,WAAW,CAACP,QAAS;cAC9B+I,UAAU,EAAExI,WAAW,CAACP,QAAS;cACjCgJ,UAAU,EAAE;gBACVC,cAAc,eACZjK,OAAA,CAACtB,cAAc;kBAAC6D,QAAQ,EAAC,OAAO;kBAAAwF,QAAA,eAC9B/H,OAAA,CAACb,IAAI;oBAACgD,KAAK,EAAC;kBAAQ;oBAAA0G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CACjB;gBACDkB,YAAY,eACVlK,OAAA,CAACtB,cAAc;kBAAC6D,QAAQ,EAAC,KAAK;kBAAAwF,QAAA,eAC5B/H,OAAA,CAACrB,UAAU;oBACTwL,OAAO,EAAEtF,uBAAwB;oBACjCuF,IAAI,EAAC,KAAK;oBACVP,QAAQ,EAAElJ,OAAQ;oBAAAoH,QAAA,EAEjBtH,YAAY,gBAAGT,OAAA,CAACT,aAAa;sBAAAsJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAGhJ,OAAA,CAACV,UAAU;sBAAAuJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAEpB,CAAE;cACF5B,EAAE,EAAE;gBACF,0BAA0B,EAAE;kBAC1B,wBAAwB,EAAE;oBACxB0C,WAAW,EAAE5H,QAAQ,CAACC;kBACxB;gBACF,CAAC;gBACD,mCAAmC,EAAE;kBACnCA,KAAK,EAAED,QAAQ,CAACC;gBAClB;cACF;YAAE;cAAA0G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEFhJ,OAAA,CAAC9B,GAAG;cAACkJ,EAAE,EAAE;gBAAEE,OAAO,EAAE,MAAM;gBAAEE,cAAc,EAAE,UAAU;gBAAE6B,EAAE,EAAE,CAAC;gBAAEI,EAAE,EAAE;cAAE,CAAE;cAAA1B,QAAA,eACrE/H,OAAA,CAACjC,UAAU;gBACTkL,OAAO,EAAC,OAAO;gBACf7B,EAAE,EAAE;kBACFiD,MAAM,EAAE,SAAS;kBACjBlI,KAAK,EAAED,QAAQ,CAACC,KAAK;kBACrB,SAAS,EAAE;oBAAEmI,cAAc,EAAE;kBAAY;gBAC3C,CAAE;gBAAAvC,QAAA,EACH;cAED;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENhJ,OAAA,CAAChC,MAAM;cACLiL,OAAO,EAAC,WAAW;cACnB3E,IAAI,EAAC,OAAO;cACZoF,SAAS;cACTS,OAAO,EAAE3E,iBAAkB;cAC3BqE,QAAQ,EAAElJ,OAAQ;cAClByG,EAAE,EAAE;gBACFmC,EAAE,EAAE,GAAG;gBACPC,eAAe,EAAEtH,QAAQ,CAACC,KAAK;gBAC/B,SAAS,EAAE;kBACTqH,eAAe,EAAE3K,KAAK,CAACqD,QAAQ,CAACC,KAAK,EAAE,GAAG;gBAC5C,CAAC;gBACD,YAAY,EAAE;kBACZqH,eAAe,EAAE3K,KAAK,CAACqD,QAAQ,CAACC,KAAK,EAAE,GAAG;gBAC5C,CAAC;gBACDiG,SAAS,EAAE,cAAcvJ,KAAK,CAACqD,QAAQ,CAACC,KAAK,EAAE,GAAG,CAAC;cACrD,CAAE;cACFoI,OAAO,EAAE5J,OAAO,gBAAGX,OAAA,CAACjB,gBAAgB;gBAACuF,IAAI,EAAE,EAAG;gBAACnC,KAAK,EAAC;cAAS;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGhJ,OAAA,CAACN,YAAY;gBAAAmJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAjB,QAAA,EAEpFpH,OAAO,GAAG,eAAe,GAAG;YAAS;cAAAkI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;UAAA;UAEN;UACAhJ,OAAA,CAAC9B,GAAG;YAAA6J,QAAA,gBAEF/H,OAAA,CAAC9B,GAAG;cAACkJ,EAAE,EAAE;gBAAEqC,EAAE,EAAE,CAAC;gBAAEnB,SAAS,EAAE;cAAS,CAAE;cAAAP,QAAA,gBACtC/H,OAAA;gBACEwK,MAAM,EAAC,SAAS;gBAChBC,KAAK,EAAE;kBAAEnD,OAAO,EAAE;gBAAO,CAAE;gBAC3BqC,EAAE,EAAC,sBAAsB;gBACzBtF,IAAI,EAAC,MAAM;gBACXiF,QAAQ,EAAEtF,wBAAyB;gBACnC6F,QAAQ,EAAElJ;cAAQ;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eACFhJ,OAAA;gBAAO0K,OAAO,EAAC,sBAAsB;gBAAA3C,QAAA,eACnC/H,OAAA,CAAC9B,GAAG;kBACFkJ,EAAE,EAAE;oBACF7E,QAAQ,EAAE,UAAU;oBACpB+E,OAAO,EAAE,cAAc;oBACvB+C,MAAM,EAAE1J,OAAO,GAAG,aAAa,GAAG;kBACpC,CAAE;kBAAAoH,QAAA,gBAEF/H,OAAA,CAAClB,MAAM;oBACL6L,GAAG,EAAElJ,mBAAoB;oBACzB2F,EAAE,EAAE;sBACFoB,KAAK,EAAE,EAAE;sBACTC,MAAM,EAAE,EAAE;sBACVE,MAAM,EAAE,cAAc9J,KAAK,CAACqD,QAAQ,CAACC,KAAK,EAAE,GAAG,CAAC,EAAE;sBAClDqH,eAAe,EAAE3K,KAAK,CAACqD,QAAQ,CAACC,KAAK,EAAE,GAAG,CAAC;sBAC3C,SAAS,EAAE;wBACTqH,eAAe,EAAE3K,KAAK,CAACqD,QAAQ,CAACC,KAAK,EAAE,GAAG;sBAC5C;oBACF,CAAE;oBAAA4F,QAAA,eAEF/H,OAAA,CAACL,WAAW;sBAACyH,EAAE,EAAE;wBAAEjF,KAAK,EAAED,QAAQ,CAACC;sBAAM;oBAAE;sBAAA0G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC,EACRvH,mBAAmB,iBAClBzB,OAAA,CAACrB,UAAU;oBACT2F,IAAI,EAAC,OAAO;oBACZ6F,OAAO,EAAGxG,CAAC,IAAK;sBACdA,CAAC,CAACiH,cAAc,CAAC,CAAC;sBAClBhG,kBAAkB,CAAC,CAAC;oBACtB,CAAE;oBACFwC,EAAE,EAAE;sBACF7E,QAAQ,EAAE,UAAU;sBACpBsI,GAAG,EAAE,CAAC,CAAC;sBACPC,KAAK,EAAE,CAAC,CAAC;sBACTtB,eAAe,EAAE,YAAY;sBAC7BrH,KAAK,EAAE,OAAO;sBACd,SAAS,EAAE;wBACTqH,eAAe,EAAE;sBACnB;oBACF,CAAE;oBAAAzB,QAAA,eAEF/H,OAAA,CAACP,KAAK;sBAACmJ,QAAQ,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CACb;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACRhJ,OAAA,CAACjC,UAAU;gBAACkL,OAAO,EAAC,SAAS;gBAAC3B,OAAO,EAAC,OAAO;gBAACF,EAAE,EAAE;kBAAEiC,EAAE,EAAE,CAAC;kBAAElH,KAAK,EAAE;gBAAiB,CAAE;gBAAA4F,QAAA,EAClFtG,mBAAmB,GAAG,uBAAuB,GAAG;cAAuC;gBAAAoH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENhJ,OAAA,CAACJ,KAAK;cAACmL,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAjD,QAAA,gBAC1B/H,OAAA,CAACJ,KAAK;gBAACiI,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAC,QAAA,eACnB/H,OAAA,CAAC/B,SAAS;kBACRyL,SAAS;kBACT1C,KAAK,EAAC,YAAY;kBAClBpD,IAAI,EAAC,WAAW;kBAChB8E,MAAM,EAAC,QAAQ;kBACfO,OAAO,EAAC,UAAU;kBAClBxF,KAAK,EAAExC,YAAY,CAACE,SAAU;kBAC9BmI,QAAQ,EAAEvF,oBAAqB;kBAC/B8F,QAAQ,EAAElJ,OAAQ;kBAClByF,KAAK,EAAE,CAAC,CAAC7E,WAAW,CAACJ,SAAU;kBAC/B4I,UAAU,EAAExI,WAAW,CAACJ,SAAU;kBAClC6I,UAAU,EAAE;oBACVC,cAAc,eACZjK,OAAA,CAACtB,cAAc;sBAAC6D,QAAQ,EAAC,OAAO;sBAAAwF,QAAA,eAC9B/H,OAAA,CAACZ,MAAM;wBAAC+C,KAAK,EAAC;sBAAQ;wBAAA0G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX;kBAEpB,CAAE;kBACF5B,EAAE,EAAE;oBACF,0BAA0B,EAAE;sBAC1B,wBAAwB,EAAE;wBACxB0C,WAAW,EAAE5H,QAAQ,CAACC;sBACxB;oBACF,CAAC;oBACD,mCAAmC,EAAE;sBACnCA,KAAK,EAAED,QAAQ,CAACC;oBAClB;kBACF;gBAAE;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,eACRhJ,OAAA,CAACJ,KAAK;gBAACiI,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAC,QAAA,eACnB/H,OAAA,CAAC/B,SAAS;kBACRyL,SAAS;kBACT1C,KAAK,EAAC,WAAW;kBACjBpD,IAAI,EAAC,UAAU;kBACf8E,MAAM,EAAC,QAAQ;kBACfO,OAAO,EAAC,UAAU;kBAClBxF,KAAK,EAAExC,YAAY,CAACG,QAAS;kBAC7BkI,QAAQ,EAAEvF,oBAAqB;kBAC/B8F,QAAQ,EAAElJ,OAAQ;kBAClByF,KAAK,EAAE,CAAC,CAAC7E,WAAW,CAACH,QAAS;kBAC9B2I,UAAU,EAAExI,WAAW,CAACH,QAAS;kBACjCgG,EAAE,EAAE;oBACF,0BAA0B,EAAE;sBAC1B,wBAAwB,EAAE;wBACxB0C,WAAW,EAAE5H,QAAQ,CAACC;sBACxB;oBACF,CAAC;oBACD,mCAAmC,EAAE;sBACnCA,KAAK,EAAED,QAAQ,CAACC;oBAClB;kBACF;gBAAE;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAERhJ,OAAA,CAAC/B,SAAS;cACRyL,SAAS;cACT1C,KAAK,EAAC,yBAAyB;cAC/BpD,IAAI,EAAC,OAAO;cACZ8E,MAAM,EAAC,QAAQ;cACfO,OAAO,EAAC,UAAU;cAClBxF,KAAK,EAAExC,YAAY,CAACI,KAAM;cAC1BiI,QAAQ,EAAEvF,oBAAqB;cAC/B8F,QAAQ,EAAElJ,OAAQ;cAClByF,KAAK,EAAE,CAAC,CAAC7E,WAAW,CAACF,KAAM;cAC3B0I,UAAU,EAAExI,WAAW,CAACF,KAAK,IAAI,qCAAsC;cACvE2I,UAAU,EAAE;gBACVC,cAAc,eACZjK,OAAA,CAACtB,cAAc;kBAAC6D,QAAQ,EAAC,OAAO;kBAAAwF,QAAA,eAC9B/H,OAAA,CAACX,KAAK;oBAAC8C,KAAK,EAAC;kBAAQ;oBAAA0G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAEpB,CAAE;cACF5B,EAAE,EAAE;gBACF,0BAA0B,EAAE;kBAC1B,wBAAwB,EAAE;oBACxB0C,WAAW,EAAE5H,QAAQ,CAACC;kBACxB;gBACF,CAAC;gBACD,mCAAmC,EAAE;kBACnCA,KAAK,EAAED,QAAQ,CAACC;gBAClB;cACF;YAAE;cAAA0G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEFhJ,OAAA,CAAC/B,SAAS;cACRyL,SAAS;cACT1C,KAAK,EAAC,eAAe;cACrBpD,IAAI,EAAC,OAAO;cACZS,IAAI,EAAC,OAAO;cACZqE,MAAM,EAAC,QAAQ;cACfO,OAAO,EAAC,UAAU;cAClBxF,KAAK,EAAExC,YAAY,CAACF,KAAM;cAC1BuI,QAAQ,EAAEvF,oBAAqB;cAC/B8F,QAAQ,EAAElJ,OAAQ;cAClByF,KAAK,EAAE,CAAC,CAAC7E,WAAW,CAACR,KAAM;cAC3BgJ,UAAU,EAAExI,WAAW,CAACR,KAAM;cAC9BiJ,UAAU,EAAE;gBACVC,cAAc,eACZjK,OAAA,CAACtB,cAAc;kBAAC6D,QAAQ,EAAC,OAAO;kBAAAwF,QAAA,eAC9B/H,OAAA,CAACd,KAAK;oBAACiD,KAAK,EAAC;kBAAQ;oBAAA0G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAEpB,CAAE;cACF5B,EAAE,EAAE;gBACF,0BAA0B,EAAE;kBAC1B,wBAAwB,EAAE;oBACxB0C,WAAW,EAAE5H,QAAQ,CAACC;kBACxB;gBACF,CAAC;gBACD,mCAAmC,EAAE;kBACnCA,KAAK,EAAED,QAAQ,CAACC;gBAClB;cACF;YAAE;cAAA0G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEFhJ,OAAA,CAAC/B,SAAS;cACRyL,SAAS;cACT1C,KAAK,EAAC,UAAU;cAChBpD,IAAI,EAAC,UAAU;cACfS,IAAI,EAAE5D,YAAY,GAAG,MAAM,GAAG,UAAW;cACzCiI,MAAM,EAAC,QAAQ;cACfO,OAAO,EAAC,UAAU;cAClBxF,KAAK,EAAExC,YAAY,CAACD,QAAS;cAC7BsI,QAAQ,EAAEvF,oBAAqB;cAC/B8F,QAAQ,EAAElJ,OAAQ;cAClByF,KAAK,EAAE,CAAC,CAAC7E,WAAW,CAACP,QAAS;cAC9B+I,UAAU,EAAExI,WAAW,CAACP,QAAQ,IAAI,+EAAgF;cACpHgJ,UAAU,EAAE;gBACVC,cAAc,eACZjK,OAAA,CAACtB,cAAc;kBAAC6D,QAAQ,EAAC,OAAO;kBAAAwF,QAAA,eAC9B/H,OAAA,CAACb,IAAI;oBAACgD,KAAK,EAAC;kBAAQ;oBAAA0G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CACjB;gBACDkB,YAAY,eACVlK,OAAA,CAACtB,cAAc;kBAAC6D,QAAQ,EAAC,KAAK;kBAAAwF,QAAA,eAC5B/H,OAAA,CAACrB,UAAU;oBACTwL,OAAO,EAAEtF,uBAAwB;oBACjCuF,IAAI,EAAC,KAAK;oBACVP,QAAQ,EAAElJ,OAAQ;oBAAAoH,QAAA,EAEjBtH,YAAY,gBAAGT,OAAA,CAACT,aAAa;sBAAAsJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAGhJ,OAAA,CAACV,UAAU;sBAAAuJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAEpB,CAAE;cACF5B,EAAE,EAAE;gBACF,0BAA0B,EAAE;kBAC1B,wBAAwB,EAAE;oBACxB0C,WAAW,EAAE5H,QAAQ,CAACC;kBACxB;gBACF,CAAC;gBACD,mCAAmC,EAAE;kBACnCA,KAAK,EAAED,QAAQ,CAACC;gBAClB;cACF;YAAE;cAAA0G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEFhJ,OAAA,CAAChC,MAAM;cACLiL,OAAO,EAAC,WAAW;cACnB3E,IAAI,EAAC,OAAO;cACZoF,SAAS;cACTtC,EAAE,EAAE;gBACFiC,EAAE,EAAE,CAAC;gBACLE,EAAE,EAAE,GAAG;gBACPC,eAAe,EAAEtH,QAAQ,CAACC,KAAK;gBAC/B,SAAS,EAAE;kBACTqH,eAAe,EAAE3K,KAAK,CAACqD,QAAQ,CAACC,KAAK,EAAE,GAAG;gBAC5C,CAAC;gBACD,YAAY,EAAE;kBACZqH,eAAe,EAAE3K,KAAK,CAACqD,QAAQ,CAACC,KAAK,EAAE,GAAG;gBAC5C,CAAC;gBACDiG,SAAS,EAAE,cAAcvJ,KAAK,CAACqD,QAAQ,CAACC,KAAK,EAAE,GAAG,CAAC;cACrD,CAAE;cACFgI,OAAO,EAAE7D,oBAAqB;cAC9BuD,QAAQ,EAAElJ,OAAQ;cAClB4J,OAAO,EAAE5J,OAAO,gBAAGX,OAAA,CAACjB,gBAAgB;gBAACuF,IAAI,EAAE,EAAG;gBAACnC,KAAK,EAAC;cAAS;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGhJ,OAAA,CAACN,YAAY;gBAAAmJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAjB,QAAA,EAEpFpH,OAAO,GAAG,qBAAqB,GAAG;YAAgB;cAAAkI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNhJ,OAAA,CAAC9B,GAAG;UACFkJ,EAAE,EAAE;YACFiB,CAAC,EAAE,CAAC;YACJ4C,SAAS,EAAE,aAAa9K,KAAK,CAAC2G,OAAO,CAACoE,OAAO,EAAE;YAC/C1B,eAAe,EAAE3K,KAAK,CAACsB,KAAK,CAAC2G,OAAO,CAACW,UAAU,CAAC0D,KAAK,EAAE,GAAG,CAAC;YAC3D7C,SAAS,EAAE;UACb,CAAE;UAAAP,QAAA,eAEF/H,OAAA,CAACjC,UAAU;YAACkL,OAAO,EAAC,OAAO;YAAC9G,KAAK,EAAC,gBAAgB;YAAA4F,QAAA,GAC/C1H,OAAO,GAAG,wBAAwB,GAAG,0BAA0B,eAChEL,OAAA,CAAChC,MAAM;cACLoJ,EAAE,EAAE;gBACFgE,EAAE,EAAE,CAAC;gBACLjJ,KAAK,EAAED,QAAQ,CAACC,KAAK;gBACrBgH,UAAU,EAAE;cACd,CAAE;cACFgB,OAAO,EAAEA,CAAA,KAAM7J,UAAU,CAAC,CAACD,OAAO,CAAE;cACpCwJ,QAAQ,EAAElJ,OAAQ;cAAAoH,QAAA,EAEjB1H,OAAO,GAAG,SAAS,GAAG;YAAS;cAAAwI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAAC9I,EAAA,CAv2BID,iBAAiB;EAAA,QACPrB,QAAQ,EACJkB,OAAO;AAAA;AAAAuL,EAAA,GAFrBpL,iBAAiB;AAy2BvB,eAAeA,iBAAiB;AAAC,IAAAoL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}