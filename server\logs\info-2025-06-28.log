{"timestamp":"2025-06-28T14:43:31.824Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:43:31.827Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-02.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:43:31.833Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T14:43:31.933Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:43:31.936Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-02.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:44:31.110Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:44:31.113Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-00.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:44:31.117Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T14:45:17.114Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:45:17.117Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-00.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:45:17.120Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T14:45:17.148Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:45:17.151Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-01.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:45:40.663Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:45:40.665Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-00.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:45:40.669Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T14:46:06.633Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:46:06.637Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-02.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:46:06.641Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T14:46:49.600Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:46:49.603Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-00.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:46:49.608Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T14:46:53.042Z","level":"INFO","message":"Test log message"}
{"timestamp":"2025-06-28T14:47:46.712Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:47:46.714Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-01.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:47:46.719Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T14:48:13.180Z","level":"INFO","message":"Test log message"}
{"timestamp":"2025-06-28T14:48:13.181Z","level":"INFO","message":"Login attempt","email":"<EMAIL>","success":true,"ip":"127.0.0.1","userAgent":"test-agent","type":"SECURITY_EVENT"}
{"timestamp":"2025-06-28T14:48:51.115Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:48:51.119Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-02.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:51:32.436Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:51:32.439Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-02.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:51:32.443Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T14:57:30.063Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:30.222Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"160ms","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:30.230Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:30.347Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"117ms","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:40.764Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu/667f898f8c9390f770b7e95f","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:40.866Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu/667f898f8c9390f770b7e95f","statusCode":200,"duration":"102ms","contentLength":"465","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:43.393Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu/667f898f8c9390f770b7e95f","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:43.484Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu/667f898f8c9390f770b7e95f","statusCode":200,"duration":"91ms","contentLength":"465","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:51.276Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:51.399Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"123ms","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:51.403Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:51.496Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"93ms","userId":"anonymous"}
{"timestamp":"2025-06-28T14:59:43.737Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:59:43.739Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-02.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:59:43.743Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T15:00:15.105Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T15:00:15.107Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-00.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T15:00:15.111Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T15:00:15.171Z","level":"INFO","message":"Incoming request","method":"POST","url":"/api/auth/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","contentLength":"50","userId":"anonymous"}
{"timestamp":"2025-06-28T15:00:19.022Z","level":"INFO","message":"Outgoing response","method":"POST","url":"/api/auth/login","statusCode":200,"duration":"3851ms","contentLength":"725","userId":"anonymous"}
{"timestamp":"2025-06-28T15:05:15.428Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T15:06:56.599Z","level":"INFO","message":"Mongoose connected to MongoDB"}
