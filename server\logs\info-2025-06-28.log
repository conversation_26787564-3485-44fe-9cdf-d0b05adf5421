{"timestamp":"2025-06-28T14:43:31.824Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:43:31.827Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-02.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:43:31.833Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T14:43:31.933Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:43:31.936Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-02.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:44:31.110Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:44:31.113Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-00.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:44:31.117Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T14:45:17.114Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:45:17.117Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-00.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:45:17.120Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T14:45:17.148Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:45:17.151Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-01.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:45:40.663Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:45:40.665Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-00.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:45:40.669Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T14:46:06.633Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:46:06.637Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-02.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:46:06.641Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T14:46:49.600Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:46:49.603Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-00.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:46:49.608Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T14:46:53.042Z","level":"INFO","message":"Test log message"}
{"timestamp":"2025-06-28T14:47:46.712Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:47:46.714Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-01.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:47:46.719Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T14:48:13.180Z","level":"INFO","message":"Test log message"}
{"timestamp":"2025-06-28T14:48:13.181Z","level":"INFO","message":"Login attempt","email":"<EMAIL>","success":true,"ip":"127.0.0.1","userAgent":"test-agent","type":"SECURITY_EVENT"}
{"timestamp":"2025-06-28T14:48:51.115Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:48:51.119Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-02.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:51:32.436Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:51:32.439Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-02.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:51:32.443Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T14:57:30.063Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:30.222Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"160ms","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:30.230Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:30.347Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"117ms","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:40.764Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu/667f898f8c9390f770b7e95f","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:40.866Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu/667f898f8c9390f770b7e95f","statusCode":200,"duration":"102ms","contentLength":"465","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:43.393Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu/667f898f8c9390f770b7e95f","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:43.484Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu/667f898f8c9390f770b7e95f","statusCode":200,"duration":"91ms","contentLength":"465","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:51.276Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:51.399Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"123ms","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:51.403Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:51.496Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"93ms","userId":"anonymous"}
{"timestamp":"2025-06-28T14:59:43.737Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:59:43.739Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-02.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:59:43.743Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T15:00:15.105Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T15:00:15.107Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-00.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T15:00:15.111Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T15:00:15.171Z","level":"INFO","message":"Incoming request","method":"POST","url":"/api/auth/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","contentLength":"50","userId":"anonymous"}
{"timestamp":"2025-06-28T15:00:19.022Z","level":"INFO","message":"Outgoing response","method":"POST","url":"/api/auth/login","statusCode":200,"duration":"3851ms","contentLength":"725","userId":"anonymous"}
{"timestamp":"2025-06-28T15:05:15.428Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T15:06:56.599Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T15:10:44.883Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T15:10:44.895Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-00.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T15:12:06.076Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/docs","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:06.091Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/docs","statusCode":301,"duration":"15ms","contentLength":183,"userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:06.101Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/docs/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:06.107Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/docs/","statusCode":200,"duration":"6ms","contentLength":"3091","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:06.229Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/docs/swagger-ui.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:06.235Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/docs/swagger-ui-bundle.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:06.237Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/docs/swagger-ui-standalone-preset.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:06.242Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/docs/swagger-ui-init.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:06.243Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/docs/swagger-ui-init.js","statusCode":200,"duration":"2ms","contentLength":"5488","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:06.247Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/docs/swagger-ui.css","statusCode":200,"duration":"18ms","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:06.267Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/docs/swagger-ui-standalone-preset.js","statusCode":200,"duration":"30ms","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:06.312Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/docs/swagger-ui-bundle.js","statusCode":200,"duration":"77ms","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:06.645Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/docs/favicon-32x32.png","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:06.648Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/docs/favicon-32x32.png","statusCode":200,"duration":"3ms","contentLength":628,"userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:40.899Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:41.597Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"697ms","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:41.601Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:41.672Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"71ms","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:47.198Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu/667f898f8c9390f770b7e95f","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:47.277Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu/667f898f8c9390f770b7e95f","statusCode":200,"duration":"79ms","contentLength":"465","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:58.726Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu/667f8a7f8c9390f770b7e962","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:58.873Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu/667f8a7f8c9390f770b7e962","statusCode":200,"duration":"147ms","contentLength":"522","userId":"anonymous"}
{"timestamp":"2025-06-28T15:13:25.566Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T15:13:25.618Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"52ms","userId":"anonymous"}
{"timestamp":"2025-06-28T15:13:25.622Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T15:13:25.683Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"61ms","userId":"anonymous"}
{"timestamp":"2025-06-28T15:13:31.349Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu/667f898f8c9390f770b7e95f","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T15:13:31.419Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu/667f898f8c9390f770b7e95f","statusCode":200,"duration":"70ms","contentLength":"465","userId":"anonymous"}
{"timestamp":"2025-06-28T15:14:01.362Z","level":"INFO","message":"Incoming request","method":"POST","url":"/api/auth/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","contentLength":"50","userId":"anonymous"}
{"timestamp":"2025-06-28T15:14:01.689Z","level":"INFO","message":"Outgoing response","method":"POST","url":"/api/auth/login","statusCode":200,"duration":"327ms","contentLength":"725","userId":"anonymous"}
{"timestamp":"2025-06-28T16:00:48.976Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T16:00:48.979Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-00.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T16:00:48.984Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T16:01:21.766Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T16:01:21.865Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"100ms","userId":"anonymous"}
{"timestamp":"2025-06-28T16:01:21.871Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T16:01:21.929Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"58ms","userId":"anonymous"}
{"timestamp":"2025-06-28T16:02:08.256Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T16:02:08.259Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-02.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T16:02:08.264Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T16:02:09.459Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T16:02:09.585Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"126ms","userId":"anonymous"}
{"timestamp":"2025-06-28T16:02:09.594Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T16:02:09.682Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"89ms","userId":"anonymous"}
{"timestamp":"2025-06-28T16:02:38.674Z","level":"INFO","message":"Incoming request","method":"POST","url":"/api/auth/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","contentLength":"50","userId":"anonymous"}
{"timestamp":"2025-06-28T16:02:42.935Z","level":"INFO","message":"Outgoing response","method":"POST","url":"/api/auth/login","statusCode":200,"duration":"4261ms","contentLength":"725","userId":"anonymous"}
