{"timestamp":"2025-06-28T14:43:31.824Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:43:31.827Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-02.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:43:31.833Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T14:43:31.933Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:43:31.936Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-02.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:44:31.110Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:44:31.113Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-00.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:44:31.117Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T14:45:17.114Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:45:17.117Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-00.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:45:17.120Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T14:45:17.148Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:45:17.151Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-01.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:45:40.663Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:45:40.665Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-00.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:45:40.669Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T14:46:06.633Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:46:06.637Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-02.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:46:06.641Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T14:46:49.600Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:46:49.603Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-00.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:46:49.608Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T14:46:53.042Z","level":"INFO","message":"Test log message"}
{"timestamp":"2025-06-28T14:47:46.712Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:47:46.714Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-01.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:47:46.719Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T14:48:13.180Z","level":"INFO","message":"Test log message"}
{"timestamp":"2025-06-28T14:48:13.181Z","level":"INFO","message":"Login attempt","email":"<EMAIL>","success":true,"ip":"127.0.0.1","userAgent":"test-agent","type":"SECURITY_EVENT"}
{"timestamp":"2025-06-28T14:48:51.115Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:48:51.119Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-02.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:51:32.436Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:51:32.439Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-02.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:51:32.443Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T14:57:30.063Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:30.222Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"160ms","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:30.230Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:30.347Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"117ms","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:40.764Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu/667f898f8c9390f770b7e95f","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:40.866Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu/667f898f8c9390f770b7e95f","statusCode":200,"duration":"102ms","contentLength":"465","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:43.393Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu/667f898f8c9390f770b7e95f","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:43.484Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu/667f898f8c9390f770b7e95f","statusCode":200,"duration":"91ms","contentLength":"465","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:51.276Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:51.399Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"123ms","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:51.403Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T14:57:51.496Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"93ms","userId":"anonymous"}
{"timestamp":"2025-06-28T14:59:43.737Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T14:59:43.739Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-02.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T14:59:43.743Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T15:00:15.105Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T15:00:15.107Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-00.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T15:00:15.111Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T15:00:15.171Z","level":"INFO","message":"Incoming request","method":"POST","url":"/api/auth/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","contentLength":"50","userId":"anonymous"}
{"timestamp":"2025-06-28T15:00:19.022Z","level":"INFO","message":"Outgoing response","method":"POST","url":"/api/auth/login","statusCode":200,"duration":"3851ms","contentLength":"725","userId":"anonymous"}
{"timestamp":"2025-06-28T15:05:15.428Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T15:06:56.599Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T15:10:44.883Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T15:10:44.895Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-00.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T15:12:06.076Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/docs","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:06.091Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/docs","statusCode":301,"duration":"15ms","contentLength":183,"userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:06.101Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/docs/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:06.107Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/docs/","statusCode":200,"duration":"6ms","contentLength":"3091","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:06.229Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/docs/swagger-ui.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:06.235Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/docs/swagger-ui-bundle.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:06.237Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/docs/swagger-ui-standalone-preset.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:06.242Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/docs/swagger-ui-init.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:06.243Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/docs/swagger-ui-init.js","statusCode":200,"duration":"2ms","contentLength":"5488","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:06.247Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/docs/swagger-ui.css","statusCode":200,"duration":"18ms","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:06.267Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/docs/swagger-ui-standalone-preset.js","statusCode":200,"duration":"30ms","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:06.312Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/docs/swagger-ui-bundle.js","statusCode":200,"duration":"77ms","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:06.645Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/docs/favicon-32x32.png","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:06.648Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/docs/favicon-32x32.png","statusCode":200,"duration":"3ms","contentLength":628,"userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:40.899Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:41.597Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"697ms","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:41.601Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:41.672Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"71ms","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:47.198Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu/667f898f8c9390f770b7e95f","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:47.277Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu/667f898f8c9390f770b7e95f","statusCode":200,"duration":"79ms","contentLength":"465","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:58.726Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu/667f8a7f8c9390f770b7e962","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T15:12:58.873Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu/667f8a7f8c9390f770b7e962","statusCode":200,"duration":"147ms","contentLength":"522","userId":"anonymous"}
{"timestamp":"2025-06-28T15:13:25.566Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T15:13:25.618Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"52ms","userId":"anonymous"}
{"timestamp":"2025-06-28T15:13:25.622Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T15:13:25.683Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"61ms","userId":"anonymous"}
{"timestamp":"2025-06-28T15:13:31.349Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu/667f898f8c9390f770b7e95f","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T15:13:31.419Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu/667f898f8c9390f770b7e95f","statusCode":200,"duration":"70ms","contentLength":"465","userId":"anonymous"}
{"timestamp":"2025-06-28T15:14:01.362Z","level":"INFO","message":"Incoming request","method":"POST","url":"/api/auth/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","contentLength":"50","userId":"anonymous"}
{"timestamp":"2025-06-28T15:14:01.689Z","level":"INFO","message":"Outgoing response","method":"POST","url":"/api/auth/login","statusCode":200,"duration":"327ms","contentLength":"725","userId":"anonymous"}
{"timestamp":"2025-06-28T16:00:48.976Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T16:00:48.979Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-00.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T16:00:48.984Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T16:01:21.766Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T16:01:21.865Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"100ms","userId":"anonymous"}
{"timestamp":"2025-06-28T16:01:21.871Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T16:01:21.929Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"58ms","userId":"anonymous"}
{"timestamp":"2025-06-28T16:02:08.256Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T16:02:08.259Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-02.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T16:02:08.264Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T16:02:09.459Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T16:02:09.585Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"126ms","userId":"anonymous"}
{"timestamp":"2025-06-28T16:02:09.594Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T16:02:09.682Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"89ms","userId":"anonymous"}
{"timestamp":"2025-06-28T16:02:38.674Z","level":"INFO","message":"Incoming request","method":"POST","url":"/api/auth/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","contentLength":"50","userId":"anonymous"}
{"timestamp":"2025-06-28T16:02:42.935Z","level":"INFO","message":"Outgoing response","method":"POST","url":"/api/auth/login","statusCode":200,"duration":"4261ms","contentLength":"725","userId":"anonymous"}
{"timestamp":"2025-06-28T16:11:59.827Z","level":"INFO","message":"Incoming request","method":"POST","url":"/api/auth/register/customer","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"multipart/form-data; boundary=----WebKitFormBoundaryGcDIZbqQnGRA25F7","contentLength":"456","userId":"anonymous"}
{"timestamp":"2025-06-28T16:12:00.452Z","level":"INFO","message":"Outgoing response","method":"POST","url":"/api/auth/register/customer","statusCode":201,"duration":"625ms","contentLength":"871","userId":"anonymous"}
{"timestamp":"2025-06-28T16:13:08.231Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T16:13:08.233Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-02.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T16:13:08.236Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T16:13:12.188Z","level":"INFO","message":"Incoming request","method":"POST","url":"/api/auth/register/customer","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"multipart/form-data; boundary=----WebKitFormBoundarydwXe8iM0Z7mrYUsA","contentLength":"456","userId":"anonymous"}
{"timestamp":"2025-06-28T16:13:12.306Z","level":"INFO","message":"Outgoing response","method":"POST","url":"/api/auth/register/customer","statusCode":409,"duration":"118ms","contentLength":"110","userId":"anonymous"}
{"timestamp":"2025-06-28T16:13:15.761Z","level":"INFO","message":"Incoming request","method":"POST","url":"/api/auth/register/customer","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"multipart/form-data; boundary=----WebKitFormBoundaryqYLH8weSw3sdmJ7Q","contentLength":"456","userId":"anonymous"}
{"timestamp":"2025-06-28T16:13:15.814Z","level":"INFO","message":"Outgoing response","method":"POST","url":"/api/auth/register/customer","statusCode":409,"duration":"53ms","contentLength":"110","userId":"anonymous"}
{"timestamp":"2025-06-28T16:15:49.104Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T16:18:20.285Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T16:22:03.101Z","level":"INFO","message":"Incoming request","method":"POST","url":"/api/auth/register/customer","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"multipart/form-data; boundary=----WebKitFormBoundary5ajw7SLaopyElJqT","contentLength":"456","userId":"anonymous"}
{"timestamp":"2025-06-28T16:22:04.502Z","level":"INFO","message":"Outgoing response","method":"POST","url":"/api/auth/register/customer","statusCode":201,"duration":"1401ms","contentLength":"871","userId":"anonymous"}
{"timestamp":"2025-06-28T16:22:21.156Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T16:22:22.714Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"1558ms","userId":"anonymous"}
{"timestamp":"2025-06-28T16:22:22.722Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T16:22:22.785Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"64ms","userId":"anonymous"}
{"timestamp":"2025-06-28T16:22:36.052Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu/667f898f8c9390f770b7e95f","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T16:22:36.164Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu/667f898f8c9390f770b7e95f","statusCode":304,"duration":"113ms","userId":"anonymous"}
{"timestamp":"2025-06-28T16:35:35.525Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T16:35:35.528Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-01.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T16:35:35.533Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T16:40:48.783Z","level":"INFO","message":"Mongoose connected to MongoDB"}
{"timestamp":"2025-06-28T16:40:48.786Z","level":"INFO","message":"MongoDB connected successfully","host":"ac-qbbqtuk-shard-00-02.vnxfnlq.mongodb.net","port":27017,"database":"bug-latte"}
{"timestamp":"2025-06-28T16:40:48.789Z","level":"INFO","message":"Server started successfully","port":"4969","environment":"development","url":"http://localhost:4969","docs":"http://localhost:4969/api/docs"}
{"timestamp":"2025-06-28T16:43:24.941Z","level":"INFO","message":"Incoming request","method":"POST","url":"/api/auth/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","contentLength":"50","userId":"anonymous"}
{"timestamp":"2025-06-28T16:43:25.317Z","level":"INFO","message":"Outgoing response","method":"POST","url":"/api/auth/login","statusCode":200,"duration":"377ms","contentLength":"725","userId":"anonymous"}
{"timestamp":"2025-06-28T16:43:36.467Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?today=true","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:43:36.470Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?today=true","statusCode":403,"duration":"3ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:43:36.472Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:43:36.477Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?limit=5","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:43:36.480Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?limit=5","statusCode":403,"duration":"2ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:43:36.484Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?today=true","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:43:36.486Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?today=true","statusCode":403,"duration":"2ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:43:36.489Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?limit=5","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:43:36.491Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?limit=5","statusCode":403,"duration":"2ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:43:36.494Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?today=true","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:43:36.496Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?today=true","statusCode":403,"duration":"2ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:43:36.499Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?today=true","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:43:36.500Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?today=true","statusCode":403,"duration":"1ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:43:36.546Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"74ms","userId":"anonymous"}
{"timestamp":"2025-06-28T16:43:36.550Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:43:36.617Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"67ms","userId":"anonymous"}
{"timestamp":"2025-06-28T16:44:06.460Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?today=true","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:44:06.462Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?today=true","statusCode":403,"duration":"2ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:44:06.465Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:44:06.468Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?limit=5","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:44:06.471Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?limit=5","statusCode":403,"duration":"3ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:44:06.474Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?today=true","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:44:06.476Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?today=true","statusCode":403,"duration":"2ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:44:06.531Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"66ms","userId":"anonymous"}
{"timestamp":"2025-06-28T16:44:37.174Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?today=true","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:44:37.175Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?today=true","statusCode":403,"duration":"1ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:44:37.177Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:44:37.180Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?limit=5","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:44:37.181Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?limit=5","statusCode":403,"duration":"1ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:44:37.183Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?today=true","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:44:37.184Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?today=true","statusCode":403,"duration":"1ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:44:37.233Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"56ms","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:07.178Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?today=true","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:07.179Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?today=true","statusCode":403,"duration":"1ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:07.181Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:07.184Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?limit=5","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:07.185Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?limit=5","statusCode":403,"duration":"1ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:07.187Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?today=true","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:07.188Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?today=true","statusCode":403,"duration":"1ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:07.233Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"52ms","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:37.175Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?today=true","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:37.176Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?today=true","statusCode":403,"duration":"1ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:37.179Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:37.181Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?limit=5","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:37.182Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?limit=5","statusCode":403,"duration":"1ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:37.184Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?today=true","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:37.185Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?today=true","statusCode":403,"duration":"1ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:37.242Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"63ms","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:54.223Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?today=true","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:54.224Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?today=true","statusCode":403,"duration":"1ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:54.226Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:54.229Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?limit=5","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:54.230Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?limit=5","statusCode":403,"duration":"1ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:54.232Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?today=true","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:54.233Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?today=true","statusCode":403,"duration":"1ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:54.235Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?limit=5","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:54.236Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?limit=5","statusCode":403,"duration":"1ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:54.237Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?today=true","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:54.238Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?today=true","statusCode":403,"duration":"1ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:54.241Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?today=true","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:54.242Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?today=true","statusCode":403,"duration":"1ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:54.278Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"52ms","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:54.281Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:45:54.335Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"54ms","userId":"anonymous"}
{"timestamp":"2025-06-28T16:46:25.172Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?today=true","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:46:25.173Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?today=true","statusCode":403,"duration":"1ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:46:25.175Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:46:25.178Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?limit=5","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:46:25.180Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?limit=5","statusCode":403,"duration":"2ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:46:25.183Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?today=true","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:46:25.185Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?today=true","statusCode":403,"duration":"2ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:46:25.255Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"80ms","userId":"anonymous"}
{"timestamp":"2025-06-28T16:46:55.183Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?today=true","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:46:55.184Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?today=true","statusCode":403,"duration":"1ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:46:55.186Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:46:55.189Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?limit=5","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:46:55.190Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?limit=5","statusCode":403,"duration":"1ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:46:55.193Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?today=true","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:46:55.194Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?today=true","statusCode":403,"duration":"1ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:46:55.627Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"441ms","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:25.185Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?today=true","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:25.187Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?today=true","statusCode":403,"duration":"2ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:25.189Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:25.191Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?limit=5","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:25.193Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?limit=5","statusCode":403,"duration":"2ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:25.195Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?today=true","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:25.196Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?today=true","statusCode":403,"duration":"1ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:25.252Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"63ms","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:32.465Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:32.529Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"64ms","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:32.532Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:32.604Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"72ms","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:38.510Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:38.576Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"66ms","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:38.580Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:38.647Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"67ms","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:44.212Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?today=true","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:44.214Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?today=true","statusCode":403,"duration":"1ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:44.217Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:44.220Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?limit=5","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:44.222Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?limit=5","statusCode":403,"duration":"2ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:44.225Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?today=true","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:44.226Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?today=true","statusCode":403,"duration":"1ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:44.229Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?limit=5","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:44.231Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?limit=5","statusCode":403,"duration":"2ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:44.235Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?today=true","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:44.237Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?today=true","statusCode":403,"duration":"2ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:44.240Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/orders?today=true","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:44.242Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/orders?today=true","statusCode":403,"duration":"2ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:44.277Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"60ms","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:44.280Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:44.362Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"82ms","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:52.268Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/staff-admin","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:52.270Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/staff-admin","statusCode":403,"duration":"2ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:52.272Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/staff-admin","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","userId":"anonymous"}
{"timestamp":"2025-06-28T16:47:52.272Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/staff-admin","statusCode":403,"duration":"1ms","contentLength":"109","userId":"anonymous"}
{"timestamp":"2025-06-28T17:06:17.172Z","level":"INFO","message":"Incoming request","method":"POST","url":"/api/auth/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","contentType":"application/json","contentLength":"53","userId":"anonymous"}
{"timestamp":"2025-06-28T17:06:19.897Z","level":"INFO","message":"Outgoing response","method":"POST","url":"/api/auth/login","statusCode":200,"duration":"2725ms","contentLength":"894","userId":"anonymous"}
{"timestamp":"2025-06-28T17:06:22.292Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T17:06:22.961Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"669ms","userId":"anonymous"}
{"timestamp":"2025-06-28T17:06:22.964Z","level":"INFO","message":"Incoming request","method":"GET","url":"/api/menu","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"timestamp":"2025-06-28T17:06:23.380Z","level":"INFO","message":"Outgoing response","method":"GET","url":"/api/menu","statusCode":304,"duration":"416ms","userId":"anonymous"}
