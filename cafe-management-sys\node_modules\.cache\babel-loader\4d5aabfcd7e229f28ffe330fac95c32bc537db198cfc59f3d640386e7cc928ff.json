{"ast": null, "code": "var _jsxFileName = \"D:\\\\Github_Repos\\\\cafe-mangement-system-react\\\\cafe-management-sys\\\\src\\\\index.jsx\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { BrowserRouter } from 'react-router-dom';\nimport App from './App';\nimport { CartProvider } from './components/CartContext';\nimport { ThemeProvider } from './contexts/ThemeContext';\nimport { AuthProvider } from './contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render( /*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(BrowserRouter, {\n    children: /*#__PURE__*/_jsxDEV(AuthProvider, {\n      children: /*#__PURE__*/_jsxDEV(ThemeProvider, {\n        children: /*#__PURE__*/_jsxDEV(CartProvider, {\n          children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 12,\n  columnNumber: 3\n}, this));", "map": {"version": 3, "names": ["React", "ReactDOM", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "App", "CartProvider", "ThemeProvider", "<PERSON>th<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["D:/Github_Repos/cafe-mangement-system-react/cafe-management-sys/src/index.jsx"], "sourcesContent": ["import React from 'react';\r\nimport ReactDOM from 'react-dom/client';\r\nimport { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom';\r\nimport App from './App';\r\nimport { CartProvider } from './components/CartContext';\r\nimport { ThemeProvider } from './contexts/ThemeContext';\r\nimport { AuthProvider } from './contexts/AuthContext';\r\n\r\nconst root = ReactDOM.createRoot(document.getElementById('root'));\r\n\r\nroot.render(\r\n  <React.StrictMode>\r\n    <BrowserRouter>\r\n      <AuthProvider>\r\n        <ThemeProvider>\r\n          <CartProvider>\r\n            <App />\r\n          </CartProvider>\r\n        </ThemeProvider>\r\n      </AuthProvider>\r\n    </BrowserRouter>\r\n  </React.StrictMode>\r\n);\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,SAASC,aAAa,QAAQ,kBAAkB;AAChD,OAAOC,GAAG,MAAM,OAAO;AACvB,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,YAAY,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,IAAI,GAAGR,QAAQ,CAACS,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC;AAEjEH,IAAI,CAACI,MAAM,eACTL,OAAA,CAACR,KAAK,CAACc,UAAU;EAAAC,QAAA,eACfP,OAAA,CAACN,aAAa;IAAAa,QAAA,eACZP,OAAA,CAACF,YAAY;MAAAS,QAAA,eACXP,OAAA,CAACH,aAAa;QAAAU,QAAA,eACZP,OAAA,CAACJ,YAAY;UAAAW,QAAA,eACXP,OAAA,CAACL,GAAG;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACA,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}