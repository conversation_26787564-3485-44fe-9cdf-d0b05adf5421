{"ast": null, "code": "var _jsxFileName = \"D:\\\\Github_Repos\\\\cafe-mangement-system-react\\\\cafe-management-sys\\\\src\\\\components\\\\Layout.jsx\";\nimport React from 'react';\nimport Navbar from './Navbar';\nimport { Outlet } from 'react-router-dom';\nimport Footer from './Footer';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction Layout() {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "Outlet", "Footer", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Layout", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Github_Repos/cafe-mangement-system-react/cafe-management-sys/src/components/Layout.jsx"], "sourcesContent": ["import React from 'react'\r\nimport Navbar from './Navbar'\r\nimport { Outlet } from 'react-router-dom'\r\nimport Footer from './Footer'\r\nfunction Layout() {\r\n  return (\r\n    <>\r\n      <Navbar />\r\n      <Outlet />\r\n      <Footer />\r\n    </>\r\n  )\r\n}\r\n\r\nexport default Layout"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,MAAM,QAAQ,kBAAkB;AACzC,OAAOC,MAAM,MAAM,UAAU;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAC7B,SAASC,MAAMA,CAAA,EAAG;EAChB,oBACEH,OAAA,CAAAE,SAAA;IAAAE,QAAA,gBACEJ,OAAA,CAACJ,MAAM;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVR,OAAA,CAACH,MAAM;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVR,OAAA,CAACF,MAAM;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eACV,CAAC;AAEP;AAACC,EAAA,GARQN,MAAM;AAUf,eAAeA,MAAM;AAAA,IAAAM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}