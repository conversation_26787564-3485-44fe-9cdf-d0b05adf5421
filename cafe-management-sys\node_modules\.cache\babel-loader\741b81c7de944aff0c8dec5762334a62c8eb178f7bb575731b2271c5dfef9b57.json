{"ast": null, "code": "var _jsxFileName = \"D:\\\\Github_Repos\\\\cafe-mangement-system-react\\\\cafe-management-sys\\\\src\\\\pages\\\\NotFound.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Box, Typography, Button, Container } from '@mui/material';\nimport { useNavigate } from 'react-router-dom';\nimport { Home, ArrowBack } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NotFound = () => {\n  _s();\n  const navigate = useNavigate();\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"md\",\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '100vh',\n        textAlign: 'center',\n        gap: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h1\",\n        sx: {\n          fontSize: {\n            xs: '6rem',\n            md: '8rem'\n          },\n          fontWeight: 'bold',\n          color: 'primary.main',\n          textShadow: '2px 2px 4px rgba(0,0,0,0.1)'\n        },\n        children: \"404\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          fontWeight: 600,\n          color: 'text.primary',\n          mb: 1\n        },\n        children: \"Page Not Found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        sx: {\n          color: 'text.secondary',\n          maxWidth: 500,\n          mb: 3\n        },\n        children: \"Sorry, the page you are looking for doesn't exist or has been moved. Please check the URL or navigate back to a safe place.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 2,\n          flexWrap: 'wrap',\n          justifyContent: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 24\n          }, this),\n          onClick: () => navigate('/'),\n          sx: {\n            px: 3,\n            py: 1.5,\n            borderRadius: 2\n          },\n          children: \"Go Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(ArrowBack, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 24\n          }, this),\n          onClick: () => navigate(-1),\n          sx: {\n            px: 3,\n            py: 1.5,\n            borderRadius: 2\n          },\n          children: \"Go Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n};\n_s(NotFound, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = NotFound;\nexport default NotFound;\nvar _c;\n$RefreshReg$(_c, \"NotFound\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "<PERSON><PERSON>", "Container", "useNavigate", "Home", "ArrowBack", "jsxDEV", "_jsxDEV", "NotFound", "_s", "navigate", "max<PERSON><PERSON><PERSON>", "children", "sx", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "textAlign", "gap", "variant", "fontSize", "xs", "md", "fontWeight", "color", "textShadow", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mb", "flexWrap", "startIcon", "onClick", "px", "py", "borderRadius", "_c", "$RefreshReg$"], "sources": ["D:/Github_Repos/cafe-mangement-system-react/cafe-management-sys/src/pages/NotFound.jsx"], "sourcesContent": ["import React from 'react';\nimport { Box, Typography, Button, Container } from '@mui/material';\nimport { useNavigate } from 'react-router-dom';\nimport { Home, ArrowBack } from '@mui/icons-material';\n\nconst NotFound = () => {\n  const navigate = useNavigate();\n\n  return (\n    <Container maxWidth=\"md\">\n      <Box\n        sx={{\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          minHeight: '100vh',\n          textAlign: 'center',\n          gap: 3,\n        }}\n      >\n        <Typography\n          variant=\"h1\"\n          sx={{\n            fontSize: { xs: '6rem', md: '8rem' },\n            fontWeight: 'bold',\n            color: 'primary.main',\n            textShadow: '2px 2px 4px rgba(0,0,0,0.1)',\n          }}\n        >\n          404\n        </Typography>\n        \n        <Typography\n          variant=\"h4\"\n          sx={{\n            fontWeight: 600,\n            color: 'text.primary',\n            mb: 1,\n          }}\n        >\n          Page Not Found\n        </Typography>\n        \n        <Typography\n          variant=\"body1\"\n          sx={{\n            color: 'text.secondary',\n            maxWidth: 500,\n            mb: 3,\n          }}\n        >\n          Sorry, the page you are looking for doesn't exist or has been moved. \n          Please check the URL or navigate back to a safe place.\n        </Typography>\n        \n        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', justifyContent: 'center' }}>\n          <Button\n            variant=\"contained\"\n            startIcon={<Home />}\n            onClick={() => navigate('/')}\n            sx={{\n              px: 3,\n              py: 1.5,\n              borderRadius: 2,\n            }}\n          >\n            Go Home\n          </Button>\n          \n          <Button\n            variant=\"outlined\"\n            startIcon={<ArrowBack />}\n            onClick={() => navigate(-1)}\n            sx={{\n              px: 3,\n              py: 1.5,\n              borderRadius: 2,\n            }}\n          >\n            Go Back\n          </Button>\n        </Box>\n      </Box>\n    </Container>\n  );\n};\n\nexport default NotFound;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,UAAU,EAAEC,MAAM,EAAEC,SAAS,QAAQ,eAAe;AAClE,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,IAAI,EAAEC,SAAS,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAE9B,oBACEI,OAAA,CAACL,SAAS;IAACS,QAAQ,EAAC,IAAI;IAAAC,QAAA,eACtBL,OAAA,CAACR,GAAG;MACFc,EAAE,EAAE;QACFC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,OAAO;QAClBC,SAAS,EAAE,QAAQ;QACnBC,GAAG,EAAE;MACP,CAAE;MAAAR,QAAA,gBAEFL,OAAA,CAACP,UAAU;QACTqB,OAAO,EAAC,IAAI;QACZR,EAAE,EAAE;UACFS,QAAQ,EAAE;YAAEC,EAAE,EAAE,MAAM;YAAEC,EAAE,EAAE;UAAO,CAAC;UACpCC,UAAU,EAAE,MAAM;UAClBC,KAAK,EAAE,cAAc;UACrBC,UAAU,EAAE;QACd,CAAE;QAAAf,QAAA,EACH;MAED;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbxB,OAAA,CAACP,UAAU;QACTqB,OAAO,EAAC,IAAI;QACZR,EAAE,EAAE;UACFY,UAAU,EAAE,GAAG;UACfC,KAAK,EAAE,cAAc;UACrBM,EAAE,EAAE;QACN,CAAE;QAAApB,QAAA,EACH;MAED;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbxB,OAAA,CAACP,UAAU;QACTqB,OAAO,EAAC,OAAO;QACfR,EAAE,EAAE;UACFa,KAAK,EAAE,gBAAgB;UACvBf,QAAQ,EAAE,GAAG;UACbqB,EAAE,EAAE;QACN,CAAE;QAAApB,QAAA,EACH;MAGD;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbxB,OAAA,CAACR,GAAG;QAACc,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEM,GAAG,EAAE,CAAC;UAAEa,QAAQ,EAAE,MAAM;UAAEhB,cAAc,EAAE;QAAS,CAAE;QAAAL,QAAA,gBAC/EL,OAAA,CAACN,MAAM;UACLoB,OAAO,EAAC,WAAW;UACnBa,SAAS,eAAE3B,OAAA,CAACH,IAAI;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACpBI,OAAO,EAAEA,CAAA,KAAMzB,QAAQ,CAAC,GAAG,CAAE;UAC7BG,EAAE,EAAE;YACFuB,EAAE,EAAE,CAAC;YACLC,EAAE,EAAE,GAAG;YACPC,YAAY,EAAE;UAChB,CAAE;UAAA1B,QAAA,EACH;QAED;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETxB,OAAA,CAACN,MAAM;UACLoB,OAAO,EAAC,UAAU;UAClBa,SAAS,eAAE3B,OAAA,CAACF,SAAS;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBI,OAAO,EAAEA,CAAA,KAAMzB,QAAQ,CAAC,CAAC,CAAC,CAAE;UAC5BG,EAAE,EAAE;YACFuB,EAAE,EAAE,CAAC;YACLC,EAAE,EAAE,GAAG;YACPC,YAAY,EAAE;UAChB,CAAE;UAAA1B,QAAA,EACH;QAED;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB,CAAC;AAACtB,EAAA,CAjFID,QAAQ;EAAA,QACKL,WAAW;AAAA;AAAAoC,EAAA,GADxB/B,QAAQ;AAmFd,eAAeA,QAAQ;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}